-- Vyt<PERSON><PERSON>e tabuľky pre nastavenia notifikácií pou<PERSON>vateľov
-- <PERSON><PERSON>ti tento skript raz pre vytvorenie tabuľky

CREATE TABLE IF NOT EXISTS `user_notification_settings` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT NOT NULL,
  `notifications_enabled` BOOLEAN NOT NULL DEFAULT 1 COMMENT 'Či má pou<PERSON>ľ povolené notifikácie',
  `notification_time` TIME NOT NULL DEFAULT '07:00:00' COMMENT '<PERSON>as dennej notifikácie',
  `workdays_only` BOOLEAN NOT NULL DEFAULT 1 COMMENT 'Notifikácie len v pracovné dni',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  UNIQUE KEY `unique_user` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Vytvorenie indexu pre rýchlejšie vyhľadávanie
CREATE INDEX `idx_notifications_enabled` ON `user_notification_settings` (`notifications_enabled`);
CREATE INDEX `idx_notification_time` ON `user_notification_settings` (`notification_time`);

-- Vloženie predvolených nastavení pre všetkých existujúcich používateľov
INSERT INTO `user_notification_settings` (`user_id`, `notifications_enabled`, `notification_time`, `workdays_only`)
SELECT `id`, 1, '07:00:00', 1 
FROM `users` 
WHERE `is_active` = 1
ON DUPLICATE KEY UPDATE `user_id` = `user_id`; -- Zabráni duplikátom ak tabuľka už existuje

-- Zobrazenie výsledku
SELECT 
    u.name,
    u.username,
    uns.notifications_enabled,
    uns.notification_time,
    uns.workdays_only
FROM users u
LEFT JOIN user_notification_settings uns ON u.id = uns.user_id
WHERE u.is_active = 1
ORDER BY u.name;
