<?php
// Potlačenie varovaní o zastaraných funkciách
error_reporting(E_ALL & ~E_DEPRECATED & ~E_NOTICE);
require_once 'config.php';
require_once 'functions.php';

// Overenie prihlásenia
requireLogin();

// Získanie PDO spojenia
$pdo = getDbConnection();

// Načítanie údajov prihláseného používateľa
$loggedInUserId = $_SESSION['user_id'] ?? 0;
$loggedUser = getUserById($loggedInUserId, $pdo);

// Inicializácia premenných pre výsledok
// $requestStatus = null; // Túto premennú už nebudeme primárne používať pre toto tlačidlo
$externalStatusResult = null; // Premenná pre výsledok z minv.sk
$errorMessage = null;
$successMessage = null; // Môžeme použiť na všeobecné správy, ale stav sa zobrazí vo vlastnom bloku
$uploadedImagePath = null;

// Spracovanie formulára
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Spracovanie nahrávania obrázku (zostáva nezmenené)
    if (isset($_FILES['request_image']) && $_FILES['request_image']['error'] === UPLOAD_ERR_OK) {
        $uploadDir = 'uploads/';
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }
        $fileName = $_FILES['request_image']['name'];
        $fileType = $_FILES['request_image']['type'];
        $fileTmpName = $_FILES['request_image']['tmp_name'];
        $fileError = $_FILES['request_image']['error'];
        $fileSize = $_FILES['request_image']['size'];
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
        if (!in_array($fileType, $allowedTypes)) {
            $errorMessage = "Neplatný formát súboru. Povolené formáty sú: JPG, PNG, GIF.";
        } elseif ($fileSize > 5 * 1024 * 1024) {
            $errorMessage = "Súbor je príliš veľký. Maximálna veľkosť je 5MB.";
        } else {
            $newFileName = uniqid() . '_' . $fileName;
            $destination = $uploadDir . $newFileName;
            if (move_uploaded_file($fileTmpName, $destination)) {
                $uploadedImagePath = $destination;
                // $successMessage = "Obrázok bol úspešne nahraný."; // Zobrazíme skôr stav žiadosti
                // Uloženie cesty k obrázku do DB by išlo sem, ak by bolo potrebné
            } else {
                $errorMessage = "Nastala chyba pri nahrávaní súboru.";
            }
        }
    }

    // --- ZMENA: Spracovanie kontroly stavu žiadosti (tlačidlo check_status) ---
    // Toto tlačidlo teraz bude zisťovať stav na minv.sk
    if (isset($_POST['check_status'])) {
        $requestNumber = isset($_POST['request_number']) ? htmlspecialchars(trim($_POST['request_number']), ENT_QUOTES, 'UTF-8') : '';

        // Validácia vstupu
        if (empty($requestNumber)) {
            $errorMessage = "Prosím, zadajte číslo žiadosti.";
        } else {
            // Volanie funkcie na získanie stavu žiadosti z externého systému (minv.sk)
            try {
                $externalStatusResult = getExternalRequestStatus($requestNumber);
                // Môžeme pridať logovanie alebo špecifickú success message, ak treba
                // $successMessage = "Stav žiadosti z minv.sk bol úspešne zistený.";
            } catch (Exception $e) {
                $errorMessage = "Nastala chyba pri zisťovaní stavu žiadosti na minv.sk: " . $e->getMessage();
            }
        }
    }

    // --- ODSTRÁNENÉ: Pôvodný blok pre check_external_status ---
    // Logika je teraz presunutá do bloku pre check_status
    /*
    if (isset($_POST['check_external_status'])) {
        // ... pôvodný kód ...
    }
    */
}

// Funkcia na získanie stavu žiadosti z externého systému (zostáva nezmenená)
function getExternalRequestStatus($requestNumber) {
    $url = 'https://www.minv.sk/?zistenie-stavu-spracovania-ziadosti';
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, "cislo=" . urlencode($requestNumber));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // Dôležité pre prípad presmerovaní
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'); // User agent môže pomôcť
    curl_setopt($ch, CURLOPT_TIMEOUT, 15); // Nastavenie timeoutu (v sekundách)

    $response = curl_exec($ch);

    if (curl_errno($ch)) {
        $error_msg = curl_error($ch);
        curl_close($ch);
        throw new Exception('Chyba pri komunikácii so serverom minv.sk (cURL Error): ' . $error_msg);
    }
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($http_code !== 200) {
         throw new Exception("Server minv.sk vrátil neočakávaný stavový kód: {$http_code}");
    }
    if (empty($response)) {
         throw new Exception("Server minv.sk vrátil prázdnu odpoveď.");
    }


    // Extrakcia výsledku z odpovede
    $result = [];

    // Hľadanie stavu žiadosti v odpovedi (hľadáme text vnútri <h4>)
    if (preg_match('/<h4[^>]*>(.*?)<\/h4>/is', $response, $matches)) {
        // Odstránime prípadné HTML tagy vo vnútri h4 a orežeme biele znaky
        $result['status'] = trim(strip_tags($matches[1]));
    } else {
        // Skúsime nájsť chybovú hlášku alebo inú informáciu, ak <h4> chýba
         if (strpos($response, 'nebola nájdená') !== false) {
              $result['status'] = "Žiadosť s daným číslom nebola nájdená.";
         } elseif (strpos($response, 'nesprávny formát') !== false) {
              $result['status'] = "Zadané číslo žiadosti má nesprávny formát.";
         } else {
            $result['status'] = "Nepodarilo sa zistiť stav žiadosti (neznáma odpoveď zo servera).";
            // Pre debug môžete zalogovať časť odpovede: error_log("MINV Response (H4 not found): " . substr($response, 0, 500));
         }
    }

    // Hľadanie ďalších informácií v odpovedi (<div class="text">)
    if (preg_match('/<div class="text"[^>]*>(.*?)<\/div>/is', $response, $matches)) {
        // Odstránime HTML tagy a orežeme biele znaky
        $result['details'] = trim(strip_tags($matches[1]));
    }

    return $result;
}

// Určenie návratovej URL (zostáva nezmenené)
$returnUrl = $_GET['return'] ?? 'dashboard.php';
if (!preg_match('/^(dashboard\.php|monthly_overview\.php|absences\.php|weekly_absences\.php)/', $returnUrl) && !filter_var($returnUrl, FILTER_VALIDATE_URL)) {
    $returnUrl = 'dashboard.php';
}
?>

<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Zistenie stavu žiadosti</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdn.jsdelivr.net/npm/tesseract.js@4/dist/tesseract.min.js"></script>
    <style>
        /* CSS štýly zostávajú nezmenené */
        .request-form { max-width: 500px; margin: 0 auto; padding: 20px; background-color: #2a2a2a; border-radius: 8px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3); margin-bottom: 20px; }
        .request-result { max-width: 500px; margin: 0 auto; padding: 20px; background-color: #2a2a2a; border-radius: 8px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3); margin-bottom: 20px; }
        .request-result h3 { margin-top: 0; color: #f0f0f0; border-bottom: 1px solid #444; padding-bottom: 10px; }
        .status-item { margin-bottom: 15px; }
        .status-label { font-weight: bold; color: #8ab4f8; margin-bottom: 5px; }
        .status-value { color: #f0f0f0; background-color: #333; padding: 8px 12px; border-radius: 4px; border: 1px solid #444; }
        .error-message { color: #ff6b6b; background-color: rgba(255, 107, 107, 0.1); border: 1px solid rgba(255, 107, 107, 0.3); padding: 10px 15px; border-radius: 4px; margin-bottom: 15px; text-align: center; }
        /* Upravený success-message pre výraznejšie zobrazenie stavu */
        .status-success-display {
            color: #4CAF50;
            background-color: rgba(76, 175, 80, 0.15);
            border: 1px solid rgba(76, 175, 80, 0.4);
            padding: 15px 20px; /* Väčší padding */
            border-radius: 8px; /* Väčšie zaoblenie */
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.3em; /* Väčšie písmo */
            font-weight: bold; /* Tučné písmo */
        }
         /* Štýl pre ostatné stavy (neúspešné, nenájdené, atď.) */
        .status-other-display {
            color: #f0f0f0;
            background-color: #3a3a3a; /* Trochu iná tmavá */
            border: 1px solid #555;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.2em;
        }
        .status-details { background-color: #2a2a2a; padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #444; }
        .status-details strong { color: #8ab4f8; display: block; margin-bottom: 8px; }
        .status-details p { color: #f0f0f0; margin: 0; line-height: 1.6; }

        .buttons { display: flex; justify-content: space-between; margin-top: 20px; }
        .back-button { background-color: #555; color: #fff; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer; transition: background-color 0.3s; text-decoration: none; /* Pre prípad a tagu */ display: inline-block; /* Pre správne zobrazenie paddingu */ }
        .back-button:hover { background-color: #666; }
        .check-button { background-color: #3a6ea5; color: #fff; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer; transition: background-color 0.3s; }
        .check-button:hover { background-color: #4a8ec5; }
        .file-upload { margin-bottom: 15px; }
        .file-upload label { display: block; margin-bottom: 5px; color: #f0f0f0; }
        .file-upload-wrapper { position: relative; margin-bottom: 10px; }
        .file-upload input[type="file"] { width: 100%; padding: 10px; background-color: #333; border: 1px dashed #555; border-radius: 4px; color: #e0e0e0; cursor: pointer; box-sizing: border-box; }
        .file-info { color: #bbb; font-size: 0.9em; margin-top: 5px; }
        .uploaded-image { max-width: 100%; margin-top: 10px; border: 1px solid #444; border-radius: 4px; }
        .image-preview { margin-top: 15px; text-align: center; }
        .image-preview img { max-width: 100%; max-height: 200px; border: 1px solid #444; border-radius: 4px; background-color: #333; }
        .image-preview p { margin-bottom: 5px; color: #f0f0f0; }
        .loading-indicator { display: none; text-align: center; margin-top: 10px; color: #8ab4f8; }
        .loading-spinner { display: inline-block; width: 20px; height: 20px; border: 3px solid rgba(138, 180, 248, 0.3); border-radius: 50%; border-top-color: #8ab4f8; animation: spin 1s ease-in-out infinite; margin-right: 10px; vertical-align: middle; }
        @keyframes spin { to { transform: rotate(360deg); } }
        .ocr-debug { margin-top: 15px; padding: 10px; background-color: #333; border: 1px solid #555; border-radius: 4px; color: #f0f0f0; font-family: monospace; white-space: pre-wrap; word-break: break-word; max-height: 200px; overflow-y: auto; }
        .ocr-debug h4 { margin-top: 0; color: #8ab4f8; border-bottom: 1px solid #444; padding-bottom: 5px; }
        .ocr-error { color: #ff6b6b; }
        .ocr-matches { color: #4CAF50; }
        label { color: #f0f0f0; margin-bottom: 5px; display: block; }
        input[type="text"] { width: 100%; padding: 10px; margin-bottom: 15px; background-color: #333; border: 1px solid #555; border-radius: 4px; color: #f0f0f0; box-sizing: border-box; }

    </style>
</head>
<body>
    <div class="container">
        <div class="header" style="padding: 15px; background-color: #333; color: #f0f0f0; text-align: center; margin-bottom: 20px; border-radius: 8px;">Zistenie stavu žiadosti</div>

         <?php if ($externalStatusResult): ?>
        <div class="external-status-result">
            <?php
            $statusText = $externalStatusResult['status'] ?? 'Neznámy stav';
            // Použijeme mb_strtolower pre porovnanie bez ohľadu na veľkosť písmen a podporu UTF-8
            if (mb_stripos($statusText, 'prijatá') !== false) {
                $statusClass = 'status-success-display'; // Zelený výrazný štýl
            } else {
                $statusClass = 'status-other-display'; // Neutrálny tmavý štýl
            }
            ?>
            <div class="<?= $statusClass ?>">
                 <?= htmlspecialchars($statusText) ?>
            </div>

            <?php if (!empty($externalStatusResult['details'])): ?>
                <div class="status-details">
                    <strong>Detaily:</strong>
                    <p><?= nl2br(htmlspecialchars($externalStatusResult['details'])) ?></p>
                </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <?php if ($errorMessage): ?>
            <div class="error-message"><?= htmlspecialchars($errorMessage) ?></div>
        <?php endif; ?>
        <?php if ($successMessage): ?>
            <div class="success-message"><?= htmlspecialchars($successMessage) ?></div>
        <?php endif; ?>


        <div class="request-form">
             <form method="post" action="request_status.php<?= !empty($returnUrl) ? '?return=' . urlencode($returnUrl) : '' ?>" enctype="multipart/form-data">
                <label for="request_number">Zadajte číslo žiadosti:</label>
                <input type="text" id="request_number" name="request_number" required
                       value="<?= htmlspecialchars($_POST['request_number'] ?? '') ?>"
                       placeholder="Napr. DS-JPPO1-123456-ABC">

                <div class="file-upload">
                    <label for="request_image">Nahrajte obrázok (voliteľné, pre OCR):</label>
                    <div class="file-upload-wrapper">
                        <input type="file" id="request_image" name="request_image" accept="image/jpeg, image/png, image/gif">
                    </div>
                    <div class="file-info">Povolené formáty: JPG, PNG, GIF. Max. veľkosť: 5MB</div>
                    <div class="loading-indicator" id="ocr-loading">
                        <div class="loading-spinner"></div>
                        <span>Rozpoznávanie textu z obrázka...</span>
                    </div>

                    <div id="ocr-debug-container" style="display: none;">
                        <div id="ocr-recognized-text" class="ocr-debug">
                            <h4>Rozpoznaný text:</h4>
                            <div id="ocr-text-content"></div>
                        </div>
                        <div id="ocr-matches-container" class="ocr-debug">
                            <h4>Nájdené zhody:</h4>
                            <div id="ocr-matches-content"></div>
                        </div>
                        <div id="ocr-error-container" class="ocr-debug" style="display: none;">
                            <h4>Chyba OCR:</h4>
                            <div id="ocr-error-content" class="ocr-error"></div>
                        </div>
                    </div>
                     <?php if ($uploadedImagePath): ?>
                    <div class="image-preview" id="uploaded-image-preview">
                        <p>Nahraný obrázok:</p>
                        <img src="<?= htmlspecialchars($uploadedImagePath) ?>" alt="Nahraný obrázok">
                    </div>
                    <?php endif; ?>
                     <div class="image-preview" id="js-image-preview" style="display: none;">
                         <p>Náhľad obrázka:</p>
                         <img id="js-preview-img" src="#" alt="Náhľad obrázka">
                    </div>
                </div>


                <div class="buttons">
                    <a href="<?= htmlspecialchars($returnUrl) ?>" class="back-button">Späť</a>
                    <button type="submit" name="check_status" class="check-button">
                        Zisti stav žiadosti
                    </button>
                    </div>
            </form>
        </div>

        <?php /* if ($requestStatus): ?>
        <div class="request-result">
            <h3>Výsledok vyhľadávania (Lokálna DB)</h3>
             ... pôvodný kód na zobrazenie lokálneho stavu ...
        </div>
        <?php endif; */ ?>

        <div style="text-align: center; margin-top: 20px; font-size: 0.9em; color: #777;">
            Prihlásený ako: <?php echo htmlspecialchars($loggedUser['name'] ?? $_SESSION['user_name'] ?? 'Neznámy'); ?>
        </div>
    </div>

    <script>
        // JavaScript pre OCR a náhľad obrázka (zostáva prevažne nezmenený)
        const requestImageInput = document.getElementById('request_image');
        const jsPreviewContainer = document.getElementById('js-image-preview');
        const jsPreviewImage = document.getElementById('js-preview-img');
        const uploadedPreviewContainer = document.getElementById('uploaded-image-preview'); // Ak existuje obrázok z PHP

        requestImageInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // Validácia (zostáva rovnaká)
                const fileType = file.type;
                const validImageTypes = ['image/jpeg', 'image/png', 'image/gif'];
                if (!validImageTypes.includes(fileType)) {
                    alert('Neplatný formát súboru. Povolené formáty sú: JPG, PNG, GIF.');
                    this.value = ''; // Reset inputu
                    jsPreviewContainer.style.display = 'none'; // Skryjeme JS náhľad
                    return;
                }
                if (file.size > 5 * 1024 * 1024) {
                    alert('Súbor je príliš veľký. Maximálna veľkosť je 5MB.');
                    this.value = '';
                    jsPreviewContainer.style.display = 'none';
                    return;
                }

                // Vytvorenie JS náhľadu
                const reader = new FileReader();
                reader.onload = function(event) {
                     if (uploadedPreviewContainer) {
                         uploadedPreviewContainer.style.display = 'none'; // Skryjeme PHP náhľad, ak existuje
                     }
                    jsPreviewImage.src = event.target.result;
                    jsPreviewContainer.style.display = 'block'; // Zobrazíme JS náhľad

                    // Resetovanie debug kontajnera OCR (zostáva rovnaké)
                    document.getElementById('ocr-debug-container').style.display = 'none';
                    document.getElementById('ocr-text-content').textContent = '';
                    document.getElementById('ocr-matches-content').textContent = '';
                    document.getElementById('ocr-error-container').style.display = 'none';
                    document.getElementById('ocr-error-content').textContent = '';

                    // Spustenie OCR (zostáva rovnaké)
                    recognizeTextFromImage(event.target.result);
                };
                reader.readAsDataURL(file);
            } else {
                 // Ak používateľ zruší výber súboru
                 jsPreviewContainer.style.display = 'none';
                  if (uploadedPreviewContainer) {
                      uploadedPreviewContainer.style.display = 'block'; // Zobrazíme späť PHP náhľad, ak existoval
                  }
            }
        });

        // Funkcia na rozpoznanie textu z obrázka pomocou Tesseract.js (zostáva nezmenená)

        async function recognizeTextFromImage(imageData) { // Pridané async kvôli čakaniu na obrázok
        const loadingIndicator = document.getElementById('ocr-loading');
        loadingIndicator.style.display = 'block';
        const debugContainer = document.getElementById('ocr-debug-container');
        const textContent = document.getElementById('ocr-text-content');
        const matchesContent = document.getElementById('ocr-matches-content');
        const errorContainer = document.getElementById('ocr-error-container');
        const errorContent = document.getElementById('ocr-error-content');

        // Reset pred spustením
        textContent.textContent = '';
        matchesContent.textContent = '';
        errorContent.textContent = '';
        errorContainer.style.display = 'none';
        debugContainer.style.display = 'block'; // Zobrazíme debug kontajner

        try {
            // --- KROK 1: Predspracovanie obrázka na Canvas ---
            const processedImageDataUrl = await preprocessImage(imageData);
            // Zobrazenie predspracovaného obrázka (voliteľné, pre debug)
            // const previewImg = document.getElementById('js-preview-img'); // Alebo vytvorte nový img element
            // previewImg.src = processedImageDataUrl;
            // console.log("Obrázok po predspracovaní (binarizácii).");
            // --- KONIEC KROKU 1 ---


            // --- KROK 2: Spustenie Tesseractu s PREDSPRACOVANÝM obrázkom ---
            const { data: { text } } = await Tesseract.recognize(
                processedImageDataUrl, // Použijeme spracovaný obrázok
                'slk+eng', // Stále používame slovenčinu a angličtinu
                {
                    // logger: m => console.log(m) // Odkomentujte pre detailné logovanie
                }
            );
            // --- KONIEC KROKU 2 ---

            textContent.textContent = text || '(žiadny text nerozpoznaný)';

            // Zvyšok kódu pre hľadanie zhôd zostáva rovnaký...
            const separatorPattern = '\\s*(?:[-–—‐‑]|\\s+)\\s*';
            const pattern = new RegExp(
                `([A-Z]{2})${separatorPattern}([A-Z0-9]{4,5})${separatorPattern}([0-9O]{6})${separatorPattern}([A-Z0-9]{3})`,
                'ig'
            );
            let match;
            let foundMatches = [];
            let correctedMatches = [];
            while ((match = pattern.exec(text)) !== null) {
                let originalMatch = match[0];
                foundMatches.push(originalMatch);
                let part1 = match[1].toUpperCase();
                let part2 = match[2].toUpperCase().replace(/0/g, 'O');
                let part3 = match[3].toUpperCase().replace(/O/g, '0');
                let part4 = match[4].toUpperCase();
                let corrected = `${part1}-${part2}-${part3}-${part4}`;
                correctedMatches.push(corrected);
            }
            if (correctedMatches.length > 0) {
                matchesContent.innerHTML =
                    '<span class="ocr-matches">Nájdené potenciálne čísla (kliknite pre vloženie):</span><br>' +
                    correctedMatches.map(cm => `<span class="ocr-matches" style="cursor:pointer; text-decoration: underline;" onclick="setRequestNumber('${cm}')">${cm}</span>`).join('<br>');
                document.getElementById('request_number').value = correctedMatches[0];
                console.log("Nájdené a korigované číslo žiadosti (prvé):", correctedMatches[0]);
            } else {
                matchesContent.innerHTML = 'Neboli nájdené žiadne zhody pre formát čísla žiadosti.';
                console.log("Nebolo nájdené žiadne číslo žiadosti v rozpoznanom texte.");
            }

        } catch (err) {
            console.error("Chyba pri OCR alebo predspracovaní:", err);
            errorContent.textContent = `Chyba: ${err.message || err}`;
            errorContainer.style.display = 'block';
        } finally {
            loadingIndicator.style.display = 'none';
        }
    }

    // --- NOVÁ POMOCNÁ FUNKCIA PRE PREDSPRACOVANIE OBRÁZKA ---
    function preprocessImage(imageDataUrl) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => {
                const canvas = document.createElement('canvas');
                canvas.width = img.width;
                canvas.height = img.height;
                const ctx = canvas.getContext('2d');
                if (!ctx) {
                    return reject(new Error('Nepodarilo sa získať 2D context z canvasu.'));
                }

                // 1. Vykreslenie obrázka na canvas
                ctx.drawImage(img, 0, 0);

                // 2. Získanie pixelových dát
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                const data = imageData.data; // Pole [R, G, B, A, R, G, B, A, ...]

                // --- TU NASTAVTE PRAHOVÚ HODNOTU (Threshold) ---
                // Hodnota medzi 0 (čierna) a 255 (biela).
                // Hodnota 128 je bežný stred.
                // Hodnota 242 zodpovedá cca 95% jasu - pravdepodobne príliš vysoká!
                // Experimentujte s hodnotami napr. medzi 100 a 170.
                const thresholdValue = 80;
                // const thresholdValue = 242; // Ak naozaj chcete skúsiť 95%
                // ------------------------------------------------

                // 3. Prechádzanie pixelov a aplikácia thresholdingu
                for (let i = 0; i < data.length; i += 4) {
                    // Výpočet jasu (grayscale)
                    const R = data[i];
                    const G = data[i + 1];
                    const B = data[i + 2];
                    const gray = 0.299 * R + 0.587 * G + 0.114 * B;

                    // Aplikácia prahu (binarizácia)
                    let newValue = 0; // Predvolená čierna
                    if (gray > thresholdValue) {
                        newValue = 255; // Biela
                    }

                    data[i] = newValue;     // Red
                    data[i + 1] = newValue; // Green
                    data[i + 2] = newValue; // Blue
                    // data[i + 3] zostáva nezmenené (Alpha)
                }

                // 4. Vloženie upravených dát späť na canvas
                ctx.putImageData(imageData, 0, 0);

                // 5. Exportovanie obrázka z canvasu ako Data URL (PNG)
                resolve(canvas.toDataURL('image/png'));
            };
            img.onerror = (err) => {
                reject(new Error('Nepodarilo sa načítať obrázok pre predspracovanie.'));
            };
            img.src = imageDataUrl; // Nastavenie zdroja pre objekt Image
        });
    }









// Pomocná funkcia na kliknutie na nájdené číslo (zostáva rovnaká)
function setRequestNumber(reqNum) {
     document.getElementById('request_number').value = reqNum;
}






    </script>
</body>
</html>