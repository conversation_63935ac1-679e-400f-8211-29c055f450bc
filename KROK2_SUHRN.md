# Krok 2: Úprava hlavnej výpočtovej logiky - DOKONČENÝ

## Čo bolo vykonané:

### 1. Pridanie načítania metadát do calculateDailyAssignments
- Pridaný riadok na načítanie metadát na začiatok funkcie: `$meta = getAssignmentMetadataMeta($targetDate, $oddelenie_id, $pdo);`
- Toto umožňuje funkcii prístup k uloženému `forced_start_order`

### 2. Oprava chyby v logike určenia štartovacieho poradia
- Opravená chyba v názve premennej: `$forcedStartOrderParam` → `$forcedStartOrder`
- Logika teraz správne funguje s prioritami:
  1. **Najvyššia priorita**: Vynútené poradie z parametra funkcie (pri manuálnom reštarte)
  2. **Stredná priorita**: Vynútené poradie z metadát (pri prepočte dňa s uloženým nastavením)
  3. **Najnižšia priorita**: Automatický výpočet podľa predchádzajúceho dňa

### 3. Úprava recalculateAssignmentsWithForcedStart
- Funkcia teraz správne ukladá `forced_start_order` do metadát pre prvý deň
- Pridané uloženie metadát s `forced_start_order` pre každý prepočítaný deň
- Pre prvý deň sa uloží vynútené poradie, pre ostatné dni `null`

### 4. Oprava "deštruktívneho" prepočtu v regenerateAssignmentsForDates
- **KĽÚČOVÁ OPRAVA**: Funkcia teraz zachováva existujúce `forced_start_order` pri prepočte
- Pred vymazaním metadát sa načíta existujúce `forced_start_order`
- Po prepočte sa uloží nové metadáta so zachovaným `forced_start_order`
- Toto rieši problém, kde bežný prepočet (napr. po pridaní absencie) zmazal manuálne nastavenie

## Výsledok:
✅ Hlavná výpočtová logika teraz **rešpektuje uložené manuálne nastavenie** s najvyššou prioritou
✅ **Opravený "deštruktívny" prepočet** - manuálne nastavenia sa už nestrácajú pri bežných prepočtoch
✅ Systém správne rozlišuje medzi manuálnym reštartom a automatickým prepočtom

## Testovanie:
- Vytvorený test súbor: `test_krok2_calculate_assignments.php`
- Test overuje všetky scenáre použitia `forced_start_order`

## Ďalší krok:
Pokračuj s **Krokom 3**: Úprava funkcie manuálneho reštartu
