<?php
require_once 'config.php';
require_once 'functions.php';

// Overenie prihlásenia
requireLogin();

// Získanie PDO spojenia
$pdo = getDbConnection();

// Načítanie údajov prihláseného používateľa
$loggedInUserId = $_SESSION['user_id'] ?? 0;
$loggedUser = getUserById($loggedInUserId, $pdo);

// --- KĽÚČOVÁ ZMENA: Načítanie aktívneho oddelenia ---
// Prioritu má oddelenie zvolené v dropdowne na dashboarde, potom defaultné oddelenie používateľa.
$activeOddelenieId = $_SESSION['selected_oddelenie_id'] ?? $_SESSION['oddelenie_id'] ?? null;
$activeOddelenieNazov = $_SESSION['selected_oddelenie_nazov'] ?? $_SESSION['oddelenie_nazov'] ?? null;

// Kontrol<PERSON>, či je oddelenie zvolené
if ($activeOddelenieId === null) {
    $_SESSION['message'] = "Prosím, vyberte oddelenie na Dashboarde pre zobrazenie týždenného rozpisu.";
    header("Location: dashboard.php");
    exit;
}

// --- Získanie dátumu pre zobrazenie ---
$requested_date_str = $_GET['date'] ?? date('Y-m-d');
try {
    $current_date = new DateTime($requested_date_str);
} catch (Exception $e) {
    $current_date = new DateTime();
}

// Nastavenie na začiatok týždňa (pondelok)
$dayOfWeek = $current_date->format('N');
$startOfWeek = clone $current_date;
$startOfWeek->modify('-' . ($dayOfWeek - 1) . ' days');

// Koniec týždňa (piatok)
$endOfWeek = clone $startOfWeek;
$endOfWeek->modify('+4 days');

// Formátovanie dátumov pre zobrazenie
$display_week_str = $startOfWeek->format('d.m.Y') . ' - ' . $endOfWeek->format('d.m.Y');
if ($activeOddelenieNazov) { // Použijeme aktívny názov oddelenia
    $display_week_str .= ' (Oddelenie: ' . htmlspecialchars($activeOddelenieNazov) . ')';
}

// Vypočítanie predchádzajúceho a nasledujúceho týždňa
$prev_week = clone $startOfWeek;
$prev_week->modify('-7 days');
$next_week = clone $startOfWeek;
$next_week->modify('+7 days');

$prev_week_str = $prev_week->format('Y-m-d');
$next_week_str = $next_week->format('Y-m-d');

// Získanie len strojníkov (používateľov so strojnik_order)
$users = [];
if ($activeOddelenieId) { // Použijeme activeOddelenieId
    $stmtUsers = $pdo->prepare("SELECT * FROM users WHERE strojnik_order IS NOT NULL AND is_active = 1 AND oddelenie_id = ? ORDER BY strojnik_order");
    $stmtUsers->execute([$activeOddelenieId]);
    $users = $stmtUsers->fetchAll(PDO::FETCH_ASSOC);
} else {
    error_log("Weekly Schedule: No active department ID, cannot fetch users.");
}

// Získanie priradení pre aktuálny týždeň
$weekAssignments = [];
if ($activeOddelenieId) { // Použijeme activeOddelenieId
    $stmt = $pdo->prepare("
        SELECT a.assignment_date, a.user_id, a.machine_number
        FROM assignments a
        JOIN users u ON a.user_id = u.id
        WHERE a.assigned_role = 'strojnik'
        AND u.oddelenie_id = :oddelenie_id 
        AND a.assignment_date BETWEEN :start_date AND :end_date
    ");
    $stmt->execute([':oddelenie_id' => $activeOddelenieId, ':start_date' => $startOfWeek->format('Y-m-d'), ':end_date' => $endOfWeek->format('Y-m-d')]);
    $assignments = $stmt->fetchAll(PDO::FETCH_ASSOC);
} else {
    $assignments = [];
}
// Organizácia priradení podľa dátumu
foreach ($assignments as $assignment) {
    $date = $assignment['assignment_date'];
    if (!isset($weekAssignments[$date])) {
        $weekAssignments[$date] = [];
    }
    $weekAssignments[$date][] = $assignment;
}

// Debug výpis pre admina
if (isset($_SESSION['is_admin']) && $_SESSION['is_admin']) {
    echo "<div style='background: #d4edda; padding: 10px; margin: 10px; font-size: 0.9em;'>";
    echo "<h3>Debug informácie o priradeniach:</h3>";
    echo "<pre>";
    print_r($weekAssignments);
    echo "</pre>";
    echo "</div>";
}

// Získanie absencií pre celý týždeň
$weekAbsences = [];
$currentDay = clone $startOfWeek;
while ($currentDay <= $endOfWeek) {
    $dateStr = $currentDay->format('Y-m-d');
    $weekAbsences[$dateStr] = getAbsentUsersForDate($currentDay, $activeOddelenieId, $pdo); // Použijeme activeOddelenieId
    $currentDay->modify('+1 day');
}

// Aktuálny dátum pre zvýraznenie
$today_date_str = date('Y-m-d');

// Kontrola, či je prihlásený Radis (alebo iný admin s právami na úpravu)
$isRadisLoggedIn = true; // Nastavíme na true pre všetkých používateľov

// Debug výpis pre kontrolu štruktúry absencií
if (isset($_SESSION['is_admin']) && $_SESSION['is_admin']) {
    echo '<pre style="display:none;">';
    print_r($weekAbsences);
    echo '</pre>';
}

// Pridajme debug výpis pre kontrolu priradení
if (isset($_SESSION['is_admin']) && $_SESSION['is_admin']) {
    echo "<div style='background: #d4edda; padding: 10px; margin: 10px; font-size: 0.9em;'>";
    echo "<h3>Debug informácie o priradeniach:</h3>";
    echo "<pre>";
    print_r($weekAssignments);
    echo "</pre>";
    echo "</div>";
}
?>
<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Týždenný Rozpis Priradení</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .weekly-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 5px;
            table-layout: fixed;
        }
        .weekly-table th, .weekly-table td {
            border: 1px solid #444;
            padding: 6px 4px;
            text-align: center;
            font-size: 0.9em;
            height: 34px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .weekly-table th {
            background-color: #333;
            color: #fff;
        }
        .weekly-table th.day-header {
            width: 16%;
        }
        .weekly-table td.user-name {
            text-align: left;
            font-weight: bold;
            background-color: #3a3a3a;
            width: 20%;
            font-size: 1.1em; /* Zväčšené o 10% z pôvodnej veľkosti 1.0em */
        }
        /* Zvýraznenie dnešného dňa - stĺpec */
        .weekly-table th.today {
            background-color: #2a5a3a; /* Výraznejšia zelená pre hlavičku */
        }
        .weekly-table td.today {
            background-color: #2a4a3a; /* Zelená pre bunky */
        }
        /* Zvýraznenie riadku prihláseného používateľa */
        .weekly-table tr.logged-user td {
            background-color: #3a3a4a; /* Modrá pre celý riadok */
        }
        /* Kombinácia zvýraznení */
        .weekly-table tr.logged-user td.today {
            background-color: #3a5a5a; /* Tyrkysová pre priesečník */
        }
        /* Zachovanie pôvodných štýlov */
        .weekly-table td.absent {
            background-color: #4a2a2a;
        }
        .weekly-table tr.logged-user td.absent {
            background-color: #5a3a4a; /* Tmavšia červená pre absenciu prihláseného používateľa */
        }
        .weekly-table td.non-working-day {
            background-color: #2a2a2a;
            color: #777;
        }
        .swipe-area {
            position: relative;
            overflow-x: auto; /* Horizontálny scroll pre malé obrazovky */
            -webkit-overflow-scrolling: touch; /* Plynulý scroll na iOS */
        }
        
        /* Responzívne štýly pre mobilné zariadenia */
        @media (max-width: 768px) {
            .weekly-table th, .weekly-table td {
                padding: 4px 2px;
                font-size: 0.85em;
            }
            .weekly-table td.user-name {
                font-size: 1.045em; /* Zväčšené o 10% z 0.95em */
            }
            .weekly-table th.day-header {
                width: 15%;
            }
            .weekly-table td.user-name {
                width: 25%;
            }
        }
        
        @media (max-width: 480px) {
            .weekly-table th, .weekly-table td {
                padding: 3px 1px;
                font-size: 0.8em;
            }
            .weekly-table td.user-name {
                font-size: 0.99em; /* Zväčšené o 10% z 0.9em */
            }
            .weekly-table th.day-header {
                width: 14%;
            }
            .weekly-table td.user-name {
                width: 30%;
            }
        }
        
        /* Upravené štýly pre tlačidlá */
        .buttons {
            text-align: center;
            margin-top: 5px; /* Výrazne zmenšený margin-top */
            padding-top: 5px; /* Výrazne zmenšený padding-top */
            border-top: 1px solid #333;
        }
        
        /* Upravený štýl pre table-responsive */
        .table-responsive {
            position: relative;
            overflow: auto;
            height: auto; /* Odstránenie fixnej výšky */
            max-height: none; /* Odstránenie max-height */
        }
        
        /* Responzívne štýly pre mobilné zariadenia */
        @media (max-width: 768px) {
            /* ... (ponechať existujúci kód) ... */
            .buttons {
                margin-top: 3px; /* Ešte menší margin na mobiloch */
                padding-top: 3px;
            }
        }
        
        @media (max-width: 480px) {
            /* ... (ponechať existujúci kód) ... */
            .buttons {
                margin-top: 2px; /* Najmenší margin na malých mobiloch */
                padding-top: 2px;
            }
        }
    </style>
</head>
<body>
    <div class="container container-wide">
        <div class="header">Týždenný Rozpis Priradení</div>

        <div class="navigation">
            <a href="weekly_schedule.php?date=<?php echo $prev_week_str; ?>">&laquo; Predošlý týždeň</a>
            <span>Týždeň: <?php echo htmlspecialchars($display_week_str); ?></span>
            <a href="weekly_schedule.php?date=<?php echo $next_week_str; ?>">Nasledujúci týždeň &raquo;</a>
        </div>

        <div class="swipe-area">
            <div class="table-responsive">
                <table class="weekly-table">
                    <thead>
                        <tr>
                            <th>Priezvisko</th>
                            <?php
                            $dayNames = ['Po', 'Ut', 'St', 'Št', 'Pi'];
                            $currentDay = clone $startOfWeek;
                            for ($i = 0; $i < 5; $i++) {
                                $dateStr = $currentDay->format('Y-m-d');
                                $isWorkDay = isWorkingDay($currentDay, $pdo);
                                $headerClass = 'day-header';
                                if ($dateStr === $today_date_str) {
                                    $headerClass .= ' today';
                                }
                                if (!$isWorkDay) {
                                    $headerClass .= ' non-working-day';
                                }
                                echo '<th class="' . $headerClass . '">' . $dayNames[$i] . '<br>' . $currentDay->format('d') . '</th>';
                                $currentDay->modify('+1 day');
                            }
                            ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                            <?php $isLoggedUser = ($user['id'] == $loggedInUserId); ?>
                            <tr <?php echo $isLoggedUser ? 'class="logged-user"' : ''; ?>>
                                <td class="user-name">
                                <?php 
                                    // Extrakcia prvého mena (priezviska) z celého mena
                                    // Keďže $users je už filtrované pre $activeOddelenieId,
                                    // všetci používatelia v tomto cykle patria do rovnakého oddelenia.
                                    $numMachinesForUserDept = 3; // Predvolené
                                    if ($activeOddelenieNazov === 'ID3') { // Použijeme aktívny názov oddelenia
                                        $numMachinesForUserDept = 2;
                                    }
                                    $fullName = $user['name'] ?? '';
                                    $nameParts = explode(' ', $fullName);
                                    $firstName = !empty($nameParts) ? $nameParts[0] : '';
                                    echo htmlspecialchars($firstName);
                                ?>
                                </td>
                                <?php
                                $currentDay = clone $startOfWeek;
                                for ($i = 0; $i < 5; $i++) {
                                    $dateStr = $currentDay->format('Y-m-d');
                                    $isWorkDay = isWorkingDay($currentDay, $pdo);
                                    $cellClass = '';
                                    
                                    if ($dateStr === $today_date_str) {
                                        $cellClass .= ' today';
                                    }
                                    
                                    if (!$isWorkDay) {
                                        $cellClass .= ' non-working-day';
                                        echo '<td class="' . trim($cellClass) . '"></td>'; // Prázdna bunka pre nepracovný deň
                                        $currentDay->modify('+1 day');
                                        continue;
                                    }
                                    
                                    // Kontrola, či je používateľ absentný
                                    $isAbsent = false;
                                    if (isset($weekAbsences[$dateStr]) && is_array($weekAbsences[$dateStr])) {
                                        foreach ($weekAbsences[$dateStr] as $absence) {
                                            // Kontrola, či existuje kľúč 'user_id' alebo 'id'
                                            $absenceUserId = $absence['user_id'] ?? $absence['id'] ?? null;
                                            if ($absenceUserId == $user['id']) {
                                                $isAbsent = true;
                                                $cellClass .= ' absent';
                                                break;
                                            }
                                        }
                                    }
                                    
                                    if ($isAbsent) {
                                        echo '<td class="' . trim($cellClass) . '"></td>'; // Prázdna bunka pre absenciu
                                        $currentDay->modify('+1 day');
                                        continue;
                                    }
                                    
                                    // Zistenie priradenia stroja
                                    $machineNumber = '';
                                    if (isset($weekAssignments[$dateStr])) {
                                        foreach ($weekAssignments[$dateStr] as $assignment) {
                                            if ($assignment['user_id'] == $user['id']) {
                                                $machineNumber = $assignment['machine_number'];
                                                // Debug výpis pre admina
                                                if (isset($_SESSION['is_admin']) && $_SESSION['is_admin']) {
                                                    echo "<!-- Debug: Dátum: $dateStr, User: {$user['id']}, Stroj: $machineNumber -->";
                                                }
                                                break;
                                            }
                                        }
                                    }
                                    
                                    // Pridanie triedy a dátových atribútov pre Radisa alebo admina
                                    $dataAttributes = '';
                                    if ($isRadisLoggedIn) {
                                        $cellClass .= ' manual-assign-cell';
                                        $dataAttributes = " data-date=\"{$dateStr}\" data-userid=\"{$user['id']}\" data-current-machine=\"".htmlspecialchars((string)$machineNumber)."\" data-num-machines=\"{$numMachinesForUserDept}\"";
                                    }
                                    
                                    // Zobrazíme len priradenia k strojom 1, 2, 3
                                    $displayValue = '';
                                    if (in_array($machineNumber, [1, 2, 3, '1', '2', '3'], true)) {
                                        $displayValue = $machineNumber;
                                    }
                                    
                                    echo '<td class="' . trim($cellClass) . '"' . $dataAttributes . '>' . htmlspecialchars((string)$displayValue) . '</td>';
                                    $currentDay->modify('+1 day');
                                }
                                ?>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="buttons">
            <button onclick="location.href='dashboard.php'" class="back-button">Späť na Dashboard</button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const swipeArea = document.querySelector('.swipe-area');
            let startX, endX;
            
            swipeArea.addEventListener('touchstart', function(e) {
                startX = e.touches[0].clientX;
            });
            
            swipeArea.addEventListener('touchend', function(e) {
                endX = e.changedTouches[0].clientX;
                handleSwipe();
            });
            
            function handleSwipe() {
                const threshold = 100; // minimálna vzdialenosť pre detekciu swipe
                
                if (startX - endX > threshold) {
                    // Swipe doľava - nasledujúci týždeň
                    window.location.href = 'weekly_schedule.php?date=<?php echo $next_week_str; ?>';
                }
                
                if (endX - startX > threshold) {
                    // Swipe doprava - predchádzajúci týždeň
                    window.location.href = 'weekly_schedule.php?date=<?php echo $prev_week_str; ?>';
                }
            }
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Existujúci kód pre swipe zostáva nezmenený
            
            // --- KÓD PRE MANUÁLNE PRIRADENIE ---
            let currentDropdown = null; // Na sledovanie aktuálne otvoreného dropdownu

            // Funkcia na zatvorenie dropdownu
            function closeDropdown() {
                if (currentDropdown) {
                    currentDropdown.remove();
                    currentDropdown = null;
                }
            }

            // Pridáme debug výpis, aby sme videli, či sa kód vykonáva
            console.log("Inicializácia kódu pre manuálne priradenie");
            
            // Získame všetky bunky s triedou manual-assign-cell
            const assignCells = document.querySelectorAll('td.manual-assign-cell');
            console.log("Počet buniek s triedou manual-assign-cell:", assignCells.length);

            const table = document.querySelector('.weekly-table');
            if (table) {
                table.addEventListener('click', (event) => {
                    console.log("Kliknutie na tabuľku", event.target);
                    const targetCell = event.target.closest('td.manual-assign-cell');

                    if (targetCell) {
                        console.log("Kliknutie na bunku s možnosťou priradenia", targetCell);
                        event.stopPropagation(); // Zabráni aktivácii iných udalostí pri kliku na bunku
                        closeDropdown(); // Zatvorí existujúci dropdown, ak nejaký je

                        const date = targetCell.dataset.date;
                        const userId = targetCell.dataset.userid;
                        console.log("Dátum:", date, "User ID:", userId);

                        // Vytvorenie dropdownu
                        const dropdown = document.createElement('div');
                        dropdown.className = 'machine-dropdown';
                        dropdown.style.position = 'absolute';
                        // Upravené pozicovanie - berie do úvahy skrolovanie
                        const rect = targetCell.getBoundingClientRect();
                        dropdown.style.left = `${window.scrollX + rect.left}px`;
                        dropdown.style.top = `${window.scrollY + rect.bottom}px`; // Pod bunku
                        dropdown.style.backgroundColor = '#333';
                        dropdown.style.border = '1px solid #555';
                        dropdown.style.borderRadius = '4px';
                        dropdown.style.zIndex = '1000'; // Zvýšime z-index, aby bol nad všetkým
                        dropdown.style.boxShadow = '0 2px 5px rgba(0,0,0,0.5)';
                        dropdown.innerHTML = `
                            `; // Vyčistíme predchádzajúci obsah

                        const numMachines = parseInt(targetCell.dataset.numMachines, 10) || 3; // Fallback na 3
                        let dropdownHTML = '';
                        for (let i = 1; i <= numMachines; i++) {
                            dropdownHTML += `<div class="dropdown-option" data-machine="${i}">Stroj ${i}</div>`;
                        }
                        dropdownHTML += `<div class="dropdown-option" data-machine="0">Stroj 0 (žiadny)</div>`;
                        dropdownHTML += `<div class="dropdown-option" data-machine="null">Zrušiť priradenie</div>`;
                        dropdown.innerHTML = dropdownHTML;

                        document.body.appendChild(dropdown);
                        currentDropdown = dropdown;

                        // Pridanie listenera na voľby v dropdowne
                        dropdown.addEventListener('click', (dropdownEvent) => {
                            if (dropdownEvent.target.classList.contains('dropdown-option')) {
                                const selectedMachine = dropdownEvent.target.dataset.machine;
                                console.log("Vybraný stroj:", selectedMachine);
                                saveMachineAssignment(date, userId, selectedMachine, targetCell);
                                closeDropdown();
                            }
                        });

                    } else if (!event.target.closest('.machine-dropdown')) {
                        // Ak klikli mimo bunky s možnosťou priradenia A zároveň mimo dropdownu, zatvor dropdown
                        closeDropdown();
                    }
                });
            } else {
                console.error("Tabuľka .weekly-table nebola nájdená!");
            }

            // Listener na kliknutie kdekoľvek v dokumente pre zatvorenie dropdownu
            document.addEventListener('click', (event) => {
                // Zatvorí, len ak existuje dropdown A klik nebol na bunku na priradenie A klik nebol dovnútra dropdownu
                if (currentDropdown && !event.target.closest('td.manual-assign-cell') && !currentDropdown.contains(event.target)) {
                    closeDropdown();
                }
            });

            // Funkcia na odoslanie dát na server
            function saveMachineAssignment(date, userId, machineNumber, cellElement) {
                console.log("Odosielam na server:", date, userId, machineNumber);
                
                const formData = new FormData();
                formData.append('date', date);
                formData.append('user_id', userId);
                formData.append('machine_number', machineNumber);
                formData.append('action', 'save_manual_assignment'); // Pridáme späť parameter action

                fetch('save_manual_assignment.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    console.log("Odpoveď zo servera:", response);
                    if (!response.ok) {
                        throw new Error('Sieťová odpoveď nebola v poriadku');
                    }
                    return response.json();
                })
                .then(data => {
                    console.log("Dáta z odpovede:", data);
                    if (data.success) {
                        // Aktualizácia obsahu bunky v tabuľke - zobrazujeme len stroje 1, 2, 3
                        if (machineNumber === 'null' || machineNumber === '0') {
                            cellElement.textContent = ''; // Prázdna bunka pre zrušené priradenie alebo stroj 0
                        } else if (in_array(machineNumber, ['1', '2', '3'], true)) {
                            cellElement.textContent = machineNumber; // Zobrazí len číslo stroja 1, 2, 3
                        } else {
                            cellElement.textContent = ''; // Pre všetky ostatné hodnoty zobrazíme prázdnu bunku
                        }
                        
                        // Vizuálna spätná väzba
                        cellElement.style.transition = 'background-color 0.1s ease-in-out';
                        cellElement.style.backgroundColor = '#4CAF50';
                        setTimeout(() => {
                            cellElement.style.backgroundColor = '';
                            setTimeout(() => { cellElement.style.transition = ''; }, 150);
                        }, 500);
                    } else {
                        alert('Chyba pri ukladaní: ' + (data.error || 'Neznáma chyba'));
                    }
                })
                .catch(error => {
                    console.error('Fetch chyba:', error);
                    alert('Nepodarilo sa uložiť priradenie: ' + error.message);
                });
            }
            
            // Pomocná funkcia in_array (podobná PHP funkcii)
            function in_array(needle, haystack, strict = false) {
                return haystack.indexOf(needle) > -1;
            }
        });
    </script>
</body>
</html>
