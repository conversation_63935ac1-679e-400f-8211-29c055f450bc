<?php
// erase_all_tables.php
// Upozornenie: Tento skript vymaže všetky tabuľky v tvojej databáze a odstráni všetky dáta.
// Použi ho opatrne!

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Pripojenie k databáze cez config.php – uisti sa, že máš správne nastavené pripojenie
require_once 'config.php';

// Bezpečnostná kontrola – skript sa vykoná iba ak je v URL parameter confirm=true
if (!isset($_GET['confirm']) || $_GET['confirm'] !== 'true') {
    echo "Upozornenie: Tento skript vymaže všetky tabuľky v databáze.<br>";
    echo "Ak si istý, spusti stránku nasledovne: erase_all.php?confirm=true";
    exit;
}

try {
    // Získanie zoznamu všetkých tabuliek v aktuálnej databáze
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_NUM);

    if (!$tables) {
        echo "Databáza je prázdna. Žiadne tabuľky na vymazanie.";
        exit;
    }

    // Iterácia cez každú tabuľku a jej vymazanie
    foreach ($tables as $table) {
        $tableName = $table[0];
        $pdo->exec("DROP TABLE IF EXISTS `$tableName`");
        echo "Vymazaná tabuľka: " . htmlspecialchars($tableName) . "<br>";
    }
    
    echo "Všetky tabuľky boli úspešne vymazané.";
} catch (Exception $e) {
    echo "Chyba: " . $e->getMessage();
}
?>