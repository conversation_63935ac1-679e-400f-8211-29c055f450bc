<?php
require_once 'config.php';
require_once 'functions.php';
require_once 'calculate_assignments.php';

$date = new DateTime('2025-04-29');
$pdo = getDbConnection();

try {
    $pdo->beginTransaction();
    
    // Vymazanie existujúcich priradení
    $stmt = $pdo->prepare("DELETE FROM assignments WHERE assignment_date = ?");
    $stmt->execute([$date->format('Y-m-d')]);
    
    // Spustenie výpočtu
    $assignments = calculateAssignments($date, $pdo);
    
    // Kontrola výsledku
    echo "Calculated assignments:\n";
    print_r($assignments);
    
    $pdo->commit();
    
    // Kontrola databázy po commite
    $stmt = $pdo->prepare("
        SELECT a.*, u.name 
        FROM assignments a 
        JOIN users u ON a.user_id = u.id 
        WHERE a.assignment_date = ?
        ORDER BY a.machine_number
    ");
    $stmt->execute([$date->format('Y-m-d')]);
    
    echo "\nSaved assignments in database:\n";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        print_r($row);
    }
    
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    echo "Error: " . $e->getMessage() . "\n";
}