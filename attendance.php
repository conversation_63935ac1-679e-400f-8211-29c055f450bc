<?php
require_once 'config.php';
require_once 'functions.php';

requireLogin();

$pdo = getDbConnection();

// --- KĽÚČOVÁ ZMENA: Načítanie aktívneho oddelenia ---
$activeOddelenieId = $_SESSION['selected_oddelenie_id'] ?? $_SESSION['oddelenie_id'] ?? null;
$activeOddelenieNazov = $_SESSION['selected_oddelenie_nazov'] ?? $_SESSION['oddelenie_nazov'] ?? null;

// Kontrola, či je oddelenie zvolené
if ($activeOddelenieId === null) {
    $_SESSION['message'] = "Prosím, vyberte oddelenie na Dashboarde pre zobrazenie dochádzky.";
    header("Location: dashboard.php");
    exit;
}

// Získanie dátumu pre zobrazenie
$requested_date_str = $_GET['date'] ?? null;
$current_date = determineTargetDate($requested_date_str, $pdo); // Vždy pracovný deň
$current_date_str = $current_date->format('Y-m-d');
$display_date_str = $current_date->format('d.m.Y');
if ($activeOddelenieNazov) {
    $display_date_str .= ' (Oddelenie: ' . htmlspecialchars($activeOddelenieNazov) . ')';
}

// Vypočítanie predchádzajúceho a nasledujúceho pracovného dňa
$prev_date_obj = findPreviousWorkingDay($current_date, $pdo);
$next_date_obj = findNextWorkingDay($current_date, $pdo);
$prev_date_str = $prev_date_obj->format('Y-m-d');
$next_date_str = $next_date_obj->format('Y-m-d');
// Načítanie absentných používateľov pre daný deň
$absentUsers = getAbsentUsersForDate($current_date, $activeOddelenieId, $pdo);

?>
<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Dochádzka - Absencie <?php echo $activeOddelenieNazov ? '- ' . htmlspecialchars($activeOddelenieNazov) : ''; ?></title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <div class="header">Prehľad Absencií</div>

        <div class="navigation">
            <a href="attendance.php?date=<?php echo $prev_date_str; ?>">&laquo; Predošlý deň</a>
            <span>Zobrazený deň: <?php echo htmlspecialchars($display_date_str); ?></span>
            <a href="attendance.php?date=<?php echo $next_date_str; ?>">Nasledujúci deň &raquo;</a>
        </div>

        <h3>Zoznam Absentných Pracovníkov pre <?php echo htmlspecialchars($display_date_str); ?></h3>

        <?php if (empty($absentUsers)): ?>
            <div class="message info">Pre tento deň nie sú evidované žiadne absencie.</div>
        <?php else: ?>
             <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>Meno Pracovníka</th>
                            <th>Typ Absencie</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($absentUsers as $absence): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($absence['user_name']); ?></td>
                            <td><?php echo htmlspecialchars($absence['absence_type']); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>

        <div class="buttons">
             <button onclick="location.href='dashboard.php'" class="back-button">Späť na Dashboard</button>
        </div>

    </div>
</body>
</html>