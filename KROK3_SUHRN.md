# Krok 3: Úprava funkcie manuálneho reštartu - DOKONČENÝ

## Čo bolo vykonané:

### 1. Analýza existujúcej implementácie
- Identifikovaný problém v pôvodnej implementácii `recalculateAssignmentsWithForcedStart`
- Funkcia ukladala metadáta pre všetky dni v cykle, čo mohlo prepísať existujúce `forced_start_order` na `null`

### 2. Správna implementácia logiky manuálneho reštartu
- **Pre prvý deň**: Funkcia ukladá `forced_start_order` do metadát s vynúteným poradím
- **Pre nasledujúce dni**: Vymaže existujúce priradenia a metadáta, potom prepočíta s **novým automatickým cyklom**
- To<PERSON> zabezpečuje, že od dňa manuálneho reštartu pokračuje nový automatický cyklus

### 3. Vylepšená logika prepočtu
```php
if ($isFirstDay) {
    // Pre prvý deň: použiť vynútené poradie a uložiť forced_start_order
    $assignments = calculateDailyAssignments($currentDateImmutable, $oddelenieId, $pdo, $forcedStartOrder);
    saveAssignments($currentDateImmutable, $assignments, $oddelenieId, $pdo);
    upsertAssignmentMetadataMeta($currentDateImmutable, $signature, null, null, $oddelenieId, $pdo, (string)$forcedStartOrder);
} else {
    // Pre nasledujúce dni: vymazať existujúce a prepočítať s novým automatickým cyklom
    // Vymazanie existujúcich priradení a metadát
    $pdo->prepare("DELETE a FROM assignments...")->execute(...);
    $pdo->prepare("DELETE FROM assignment_metadata...")->execute(...);
    // Prepočítanie s automatickým cyklom (bez forced_start_order)
    $assignments = calculateDailyAssignments($currentDateImmutable, $oddelenieId, $pdo, null);
    upsertAssignmentMetadataMeta($currentDateImmutable, $signature, null, null, $oddelenieId, $pdo, null);
}
```

### 4. Integrácia s API endpointom
- Funkcia sa volá z `api_force_machinist_start.php`
- API endpoint prijíma parametre: `date`, `strojnik_order`, `oddelenie_id`
- Validuje oprávnenia používateľa (vedúci, schifflieder, alebo radis)
- Kontroluje, či sa neupravuje minulosť (okrem používateľa 'radis')

## Výsledok:
✅ **Správne uloženie forced_start_order**: Manuálny reštart uloží `forced_start_order` do metadát pre prvý deň
✅ **Nový automatický cyklus**: Od dňa manuálneho reštartu pokračuje nový automatický cyklus bez starých manuálnych nastavení
✅ **Správne správanie**: Ak vynútim štart od strojníka #3, nasledujúce dni budú pokračovať 3→1→2→3→1...

## Testovanie:
- Vytvorený test súbor: `test_krok3_manual_restart.php`
- Test overuje:
  - Správne uloženie `forced_start_order` pre prvý deň
  - Prepisovanie existujúcich `forced_start_order` na automatický cyklus pre nasledujúce dni
  - Scenáre s a bez existujúcich nastavení

## Ďalší krok:
Pokračuj s **Krokom 4**: Finálna oprava "deštruktívneho" prepočtu (už čiastočne vyriešené v Kroku 2, ale možno potrebuje ďalšie doladenie)
