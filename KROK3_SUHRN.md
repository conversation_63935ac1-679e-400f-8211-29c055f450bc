# Krok 3: Úprava funkcie manuálneho reštartu - DOKONČENÝ

## Čo bolo vykonané:

### 1. Analýza existujúcej implementácie
- Identifikovaný problém v pôvodnej implementácii `recalculateAssignmentsWithForcedStart`
- Funkcia ukladala metadáta pre všetky dni v cykle, čo mohlo prepísať existujúce `forced_start_order` na `null`

### 2. Optimalizácia logiky ukladania metadát
- **Pre prvý deň**: Funkcia teraz explicitne ukladá `forced_start_order` do metadát
- **Pre ostatné dni**: Používa sa `regenerateAssignmentsForDates`, ktor<PERSON> zachováva existujúce `forced_start_order`
- <PERSON>to zabezpečuje, že manuálny reštart neprepisuje existujúce manuálne nastavenia v nasledujúcich dňoch

### 3. Vylepšená logika prepočtu
```php
if ($isFirstDay) {
    // Pre prvý deň: použiť vynútené poradie a uložiť forced_start_order
    $assignments = calculateDailyAssignments($currentDateImmutable, $oddelenieId, $pdo, $forcedStartOrder);
    saveAssignments($currentDateImmutable, $assignments, $oddelenieId, $pdo);
    upsertAssignmentMetadataMeta($currentDateImmutable, $signature, null, null, $oddelenieId, $pdo, (string)$forcedStartOrder);
} else {
    // Pre ostatné dni: použiť regenerateAssignmentsForDates (zachováva existujúce forced_start_order)
    regenerateAssignmentsForDates($currentDateImmutable, $oddelenieId, $pdo);
}
```

### 4. Integrácia s API endpointom
- Funkcia sa volá z `api_force_machinist_start.php`
- API endpoint prijíma parametre: `date`, `strojnik_order`, `oddelenie_id`
- Validuje oprávnenia používateľa (vedúci, schifflieder, alebo radis)
- Kontroluje, či sa neupravuje minulosť (okrem používateľa 'radis')

## Výsledok:
✅ **Správne uloženie forced_start_order**: Manuálny reštart teraz správne uloží `forced_start_order` do metadát pre prvý deň
✅ **Zachovanie existujúcich nastavení**: Funkcia nezmaže existujúce `forced_start_order` v nasledujúcich dňoch
✅ **Optimalizovaný prepočet**: Používa efektívnu kombináciu priameho uloženia a regenerácie

## Testovanie:
- Vytvorený test súbor: `test_krok3_manual_restart.php`
- Test overuje:
  - Správne uloženie `forced_start_order` pre prvý deň
  - Zachovanie existujúcich `forced_start_order` pre ostatné dni
  - Scenáre s a bez existujúcich nastavení

## Ďalší krok:
Pokračuj s **Krokom 4**: Finálna oprava "deštruktívneho" prepočtu (už čiastočne vyriešené v Kroku 2, ale možno potrebuje ďalšie doladenie)
