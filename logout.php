<?php
// logout.php
require_once 'config.php'; // Načíta konfiguráciu a spustí session_start()
require_once 'functions.php'; // Načíta pomocné funkcie (vr<PERSON><PERSON>e tých pre Remember Me)

$pdo = getDbConnection(); // Získanie PDO spojenia

// Ak je používateľ prihlásený, zmažeme jeho persistentný login token z DB
if (isset($_SESSION['user_id'])) {
    $userId = (int)$_SESSION['user_id'];

    // Zmažeme všetky persistentné login tokeny pre tohto používateľa z DB
    // Toto je bezpečnejšie ako mazať len konkrétnu sériu,
    // pretože pri explicitnom odhlásení chceme zneplatniť všetky "remember me" session.
    try {
        if (function_exists('deletePersistentLoginsForUser')) {
            deletePersistentLoginsForUser($userId, $pdo);
        } else {
            error_log("Funkcia deletePersistentLoginsForUser neexistuje v logout.php.");
        }
    } catch (Exception $e) {
        error_log("Chyba pri mazaní persistentných prihlásení pre používateľa $userId počas odhlásenia: " . $e->getMessage());
        // Pokračujeme v odhlasovaní aj pri chybe DB
    }
}

// Zmažeme remember me cookie z prehliadača
if (function_exists('deletePersistentLoginCookie')) {
    deletePersistentLoginCookie();
} else {
    error_log("Funkcia deletePersistentLoginCookie neexistuje v logout.php.");
}

// Zničenie všetkých session dát
$_SESSION = array();

// Ak sa používa session cookie, odstráni sa aj cookie.
// Toto je dôležité pre úplné zničenie session.
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Zničenie session
session_destroy();

// Presmerovanie na prihlasovaciu stránku
header("Location: login.php");
exit;
?>
