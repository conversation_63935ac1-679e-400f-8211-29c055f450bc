# Krok 1: Úprava pomocných funkcií pre metadáta - DOKONČENÝ

## Čo bolo vykonané:

### 1. Pridanie stĺpca do databázy
- Vytvorený migračný súbor: `migration_add_forced_start_order.sql`
- Vytvorený spúšťací skript: `run_migration.php`
- Pridaný nový stĺpec `forced_start_order VARCHAR(255) NULL` do tabuľky `assignment_metadata`

### 2. Úprava funkcií v `functions.php`
- **getAssignmentMetadataMeta()**: Pridané načítanie stĺpca `forced_start_order`
- **upsertAssignmentMetadataMeta()**: Pridaný nový parameter `?string $forcedStartOrder = null`
- Aktualizované všetky volania funkcie v súbore

### 3. Úprava funkcií v `PWA/functions.php`
- **getAssignmentMetadataMeta()**: Pridané načítanie stĺpca `forced_start_order`
- **upsertAssignmentMetadataMeta()**: Pridaný nový parameter `?string $forcedStartOrder = null`
- Aktualizované volanie funkcie v súbore

### 4. Úprava volania v `dashboard.php`
- Aktualizované volanie `upsertAssignmentMetadataMeta()` s novým parametrom

### 5. Testovanie
- Vytvorený test súbor: `test_metadata_functions.php`
- Test overuje správne ukladanie a načítavanie `forced_start_order`

## Výsledok:
✅ Systém teraz vie čítať a zapisovať manuálne nastavené štartovacie poradie strojníkov do/z databázy.

## Ďalší krok:
Pokračuj s **Krokom 2**: Úprava hlavnej výpočtovej logiky v funkcii `calculateDailyAssignments`.
