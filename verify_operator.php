// Overenie strojnik_order
$stmt = $pdo->prepare("
    SELECT id, name, strojnik_order 
    FROM users 
    WHERE name LIKE '%Kramár%'
");
$stmt->execute();
$operator = $stmt->fetch(PDO::FETCH_ASSOC);

// Overenie predchádzajúcich priradení
$stmt2 = $pdo->prepare("
    SELECT assign_date, machine 
    FROM machine_assignments 
    WHERE user_id = ? 
    AND assign_date < '2025-04-29'
    ORDER BY assign_date DESC 
    LIMIT 5
");
$stmt2->execute([$operator['id']]);
$previousAssignments = $stmt2->fetchAll(PDO::FETCH_ASSOC);