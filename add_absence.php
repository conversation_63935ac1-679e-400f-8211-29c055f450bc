<?php
// add_absence.php
session_start();
require_once 'config.php';

if(!isset($_SESSION['user_id'])){
    header("Location: login.php");
    exit;
}

if(isset($_POST['absence_date']) && isset($_POST['absence_type'])){
    $stmt = $pdo->prepare("INSERT INTO absences (user_id, absence_date, absence_type) VALUES (?, ?, ?)");
    $stmt->execute([$_SESSION['user_id'], $_POST['absence_date'], $_POST['absence_type']]);
}

header("Location: dashboard.php");
exit;
?>