<?php
require_once 'config.php';
require_once 'functions.php';

$today = new DateTimeImmutable('today'); // Dnešný dátum

// *** BLOK PRE AJAX MAZANIE ***
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_GET['action']) && $_GET['action'] === 'delete') {
    requireLogin();
    $pdo = getDbConnection();
    $loggedUser = getUserById($_SESSION['user_id'] ?? 0, $pdo);

    // --- KĽÚČOVÁ ZMENA: Načítanie aktívneho oddelenia ---
    $activeOddelenieId = $_SESSION['selected_oddelenie_id'] ?? null;
    if ($activeOddelenieId === null) {
        header('Content-Type: application/json');
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Nie je vybrané žiadne aktívne oddelenie.']);
        exit;
    }

    // Získanie parametrov
    $date_str = $_POST['date'] ?? '';
    $uid = (int)($_POST['uid'] ?? 0);
    error_log("AJAX mazanie absencie - Dátum: $date_str, UID: $uid, Používateľ: " . ($_SESSION['username'] ?? 'neznámy') . ", Oddelenie: $activeOddelenieId");
    
    try {
        // Kontrola práv na mazanie
        $canEditAll = in_array($loggedUser['role1'] ?? '', ['veduci','schifflieder'], true) || 
                     (isset($_SESSION['username']) && $_SESSION['username'] === 'radis');
                     
        if (!$canEditAll && $uid !== (int)($loggedUser['id'] ?? 0)) { 
            header('Content-Type: application/json');
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Nemáš oprávnenie mazať absenciu iného používateľa.']);
            exit;
        }

        // Validácia dátumu a UID
        if (empty($date_str) || $uid <= 0) {
            header('Content-Type: application/json');
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Chýbajúce alebo neplatné parametre.']);
            exit;
        }
        
        $targetDate = new DateTimeImmutable($date_str);
        $today = new DateTimeImmutable('today');
        
        // Nájdenie absencie pre daný deň a používateľa
        $stmt = $pdo->prepare("
            SELECT id, start_date, end_date 
            FROM absences 
            WHERE user_id = ? 
            AND ? BETWEEN start_date AND end_date
        ");
        $stmt->execute([$uid, $date_str]);
        $absence = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$absence) {
            header('Content-Type: application/json');
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Absencia pre tento deň nebola nájdená.']);
            exit;
        }
        
        $absenceId = $absence['id'];
        
        // Mazanie absencie
        $stmtDelete = $pdo->prepare("DELETE FROM absences WHERE id = ?");
        $stmtDelete->execute([$absenceId]);
        
        // Mazanie priradení od aktuálneho dňa
        $dateToDeleteFrom = $today;
        if ($targetDate > $today) {
            $dateToDeleteFrom = $targetDate;
        }
        
        // Použitie funkcie na mazanie budúcich priradení
        $deleteFutureSuccess = deleteFutureDataFromDate($dateToDeleteFrom, $activeOddelenieId, $pdo);
        if (!$deleteFutureSuccess) {
            throw new Exception("Nepodarilo sa vymazať budúce dáta.");
        }
        
        // Ak používateľ nie je 'radis', musíme explicitne prepočítať.
        // Ak je 'radis', deleteFutureDataFromDate už spustilo prepočítanie.
        if (!isset($_SESSION['username']) || $_SESSION['username'] !== 'radis') {
            if (function_exists('recalculateAssignmentsFromDateToToday')) {
                $recalcSuccess = recalculateAssignmentsFromDateToToday($dateToDeleteFrom, $activeOddelenieId, $pdo);
                if (!$recalcSuccess) {
                    error_log("Nepodarilo sa prepočítať priradenia po zmazaní absencie pre oddelenie $activeOddelenieId.");
                } else {
                    error_log("Priradenia prepočítané od " . $dateToDeleteFrom->format('Y-m-d') . " pre oddelenie $activeOddelenieId po zmazaní absencie.");
                }
            } else {
                error_log("Funkcia recalculateAssignmentsFromDateToToday nie je dostupná.");
            }
        }
        
        header('Content-Type: application/json');
        echo json_encode(['success' => true, 'message' => 'Absencia bola úspešne zmazaná.']);
        exit;
        
    } catch (PDOException $e) {
        error_log("DB Error processing absence deletion: Code=" . $e->getCode() . ", Message=" . $e->getMessage());
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Chyba databázy pri spracovaní požiadavky.']);
        exit;
    } catch (Exception $e) {
        error_log("General Error processing absence deletion: " . $e->getMessage());
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Nastala chyba pri spracovaní: ' . $e->getMessage()]);
        exit;
    }
}
// *** KONIEC BLOKU PRE AJAX MAZANIE ***

// *** NOVÝ BLOK PRE AJAX PRIDANIE ***
if ($_SERVER['REQUEST_METHOD'] === 'POST' && ($_POST['action'] ?? '') === 'add') {
    requireLogin();
    $pdo = getDbConnection();
    $loggedUser = getUserById($_SESSION['user_id'] ?? 0, $pdo);

    // --- KĽÚČOVÁ ZMENA: Načítanie aktívneho oddelenia ---
    $activeOddelenieId = $_SESSION['selected_oddelenie_id'] ?? null;
    if ($activeOddelenieId === null) {
        header('Content-Type: application/json');
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Nie je vybrané žiadne aktívne oddelenie.']);
        exit;
    }

    try {
        $uid = (int)($_POST['user_id'] ?? 0); 
        $from_str = $_POST['start_date'] ?? '';
        $to_str = $_POST['end_date'] ?? '';
        $type = $_POST['absence_type'] ?? 'Celý deň'; 
        error_log("AJAX pridanie absencie - Od: $from_str, Do: $to_str, UID: $uid, Typ: $type, Používateľ: " . ($_SESSION['username'] ?? 'neznámy') . ", Oddelenie: $activeOddelenieId");
        // Špeciálna výnimka pre používateľa 'radis' - môže editovať všetkých
        $canEditAll = in_array($loggedUser['role1'] ?? '', ['veduci','schifflieder'], true) || 
                     (isset($_SESSION['username']) && $_SESSION['username'] === 'radis');
                     
        if (!$canEditAll && $uid !== (int)($loggedUser['id'] ?? 0)) {
            header('Content-Type: application/json');
            http_response_code(403); 
            echo json_encode(['success' => false, 'message' => 'Nemáš oprávnenie pridávať absenciu inému používateľovi.']); 
            exit;
        }
        
        // Validácia vstupov
        if (empty($from_str) || empty($to_str) || empty($type) || $uid <= 0) {
            header('Content-Type: application/json');
            http_response_code(400); 
            echo json_encode(['success' => false, 'message' => 'Chýbajúce alebo neplatné parametre.']); 
            exit;
        }
        
        $from_date = new DateTimeImmutable($from_str);
        $to_date = new DateTimeImmutable($to_str);
        $today = new DateTimeImmutable('today');
        
        // Kontrola, či dátum "do" nie je skôr ako dátum "od"
        if ($to_date < $from_date) {
            header('Content-Type: application/json');
            http_response_code(400); 
            echo json_encode(['success' => false, 'message' => 'Dátum "do" nemôže byť skôr ako dátum "od".']); 
            exit;
        }
        
        // Najprv zmažeme všetky existujúce absencie pre tohto používateľa v danom rozsahu
        // aby sme predišli duplicitným alebo konfliktným záznamom.
        $stmtDeleteExisting = $pdo->prepare("
            DELETE FROM absences
            WHERE user_id = :user_id
            AND start_date <= :end_date AND end_date >= :start_date
        ");
        $stmtDeleteExisting->execute([
            ':user_id' => $uid,
            ':start_date' => $from_date->format('Y-m-d'), // Použijeme už validovaný $from_date
            ':end_date' => $to_date->format('Y-m-d')    // Použijeme už validovaný $to_date
        ]);
        error_log("Zmazaných (AJAX) " . $stmtDeleteExisting->rowCount() . " existujúcich prekrývajúcich sa absencií pre UID: $uid v rozsahu {$from_date->format('Y-m-d')} - {$to_date->format('Y-m-d')} pred pridaním novej.");

        // Vloženie novej absencie
        $stmt = $pdo->prepare("
            INSERT INTO absences (user_id, start_date, end_date, absence_type) 
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([
            $uid, 
            $from_date->format('Y-m-d'), 
            $to_date->format('Y-m-d'), 
            $type
        ]);
        
        // Mazanie priradení od aktuálneho dňa alebo začiatku absencie (čo je neskôr)
        $dateToDeleteFrom = $today;
        if ($from_date > $today) {
            $dateToDeleteFrom = $from_date;
        }
        
        // Použitie funkcie na mazanie budúcich priradení
        $deleteFutureSuccess = deleteFutureDataFromDate($dateToDeleteFrom, $activeOddelenieId, $pdo);
        if (!$deleteFutureSuccess) {
            throw new Exception("Nepodarilo sa vymazať budúce dáta.");
        }
        
        // Ak používateľ nie je 'radis', musíme explicitne prepočítať.
        // Ak je 'radis', deleteFutureDataFromDate už spustilo prepočítanie.
        if (!isset($_SESSION['username']) || $_SESSION['username'] !== 'radis') {
            if (function_exists('recalculateAssignmentsFromDateToToday')) {
                $recalcSuccess = recalculateAssignmentsFromDateToToday($dateToDeleteFrom, $activeOddelenieId, $pdo);
                if (!$recalcSuccess) {
                    error_log("Nepodarilo sa prepočítať priradenia po pridaní absencie pre oddelenie $activeOddelenieId.");
                } else {
                    error_log("Priradenia prepočítané od " . $dateToDeleteFrom->format('Y-m-d') . " pre oddelenie $activeOddelenieId po pridaní absencie.");
                }
            } else {
                error_log("Funkcia recalculateAssignmentsFromDateToToday nie je dostupná.");
            }
        }
        
        header('Content-Type: application/json');
        echo json_encode(['success' => true, 'message' => 'Absencia úspešne pridaná.']); 
        exit;
        
    } catch (PDOException $e) {
        error_log("DB Error adding absence via AJAX: Code=" . $e->getCode() . ", Message=" . $e->getMessage());
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Chyba databázy pri spracovaní požiadavky.']);
        exit;
    } catch (Exception $e) {
        error_log("General Error adding absence via AJAX: " . $e->getMessage());
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Nastala chyba pri spracovaní: ' . $e->getMessage()]);
        exit;
    }
}

// --- Zvyšok súboru pokračuje len ak nejde o AJAX operáciu (pre priame načítanie absences.php) ---

if (!($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && in_array($_POST['action'], ['add', 'delete'])) &&
    !($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_GET['action']) && $_GET['action'] === 'delete') ) {

    requireLogin(); 
    $pdo        = getDbConnection(); 
    $loggedUser = getUserById($_SESSION['user_id'] ?? 0, $pdo); 

    // --- KĽÚČOVÁ ZMENA: Načítanie aktívneho oddelenia pre formulár ---
    $activeOddelenieIdForForm = $_SESSION['selected_oddelenie_id'] ?? $_SESSION['oddelenie_id'] ?? null;
    if ($activeOddelenieIdForForm === null && $_SERVER['REQUEST_METHOD'] !== 'POST') { // Ak nie je POST, ale stránka sa načítava
        $_SESSION['message'] = "Prosím, vyberte oddelenie na Dashboarde pre pridanie absencie.";
        header("Location: dashboard.php"); // Presmerujeme, ak nie je oddelenie
        exit;
    }
    $targetUserIdForForm = (int)($_GET['uid'] ?? $loggedUser['id'] ?? 0);

    
    $prefillDate = $_GET['date']   ?? '';
    $prefillUid  = (int)($_GET['uid']  ?? 0);
    
    $defaultReturnUrl = 'monthly_overview.php';
    $allowedReturnUrls = ['monthly_overview.php', 'dashboard.php', 'absences.php', 'weekly_absences.php'];
    $returnUrlInput = $_GET['return'] ?? $defaultReturnUrl;

    
    $isValidReturn = false;
    foreach ($allowedReturnUrls as $allowed) {
        if (strpos($returnUrlInput, $allowed) === 0) {
            
            $isValidReturn = true;
            break;
        }
    }
    
    if (strpos($returnUrlInput, '../') === 0 && (strlen($returnUrlInput) === 3 || in_array(substr($returnUrlInput, 3), $allowedReturnUrls))) {
        $isValidReturn = true;
    }


    $returnUrl = $isValidReturn ? filter_var($returnUrlInput, FILTER_SANITIZE_URL) : $defaultReturnUrl;


    
    $error_message = '';
    if ($_SERVER['REQUEST_METHOD']==='POST' && !isset($_POST['action'])){ 
        $uid   = (int)$_POST['user_id'];
        $from_str  = $_POST['start_date'];
        $to_str    = $_POST['end_date']; // Default pre priame odoslanie formulára
        $type  = $_POST['absence_type'] ?? 'Celý deň'; // Zmena fallbacku na 'Celý deň' pre konzistenciu
        $back  = $_POST['return_to']    ?? $defaultReturnUrl;
        
        // Použijeme aktívne oddelenie zo session pre operácie
        $formTargetOddelenieId = $_SESSION['selected_oddelenie_id'] ?? null;
        if ($formTargetOddelenieId === null) {
            $error_message = 'Nie je vybrané žiadne aktívne oddelenie pre spracovanie formulára.';
        }

        error_log("FORM pridanie absencie - Od: $from_str, Do: $to_str, UID: $uid, Typ: $type, Používateľ: " . ($_SESSION['username'] ?? 'neznámy') . ", Oddelenie: $formTargetOddelenieId");
        
        
        $isValidBack = false;
        foreach ($allowedReturnUrls as $allowed) { if (strpos($back, $allowed) === 0) { $isValidBack = true; break; } }
        if (strpos($back, '../') === 0 && (strlen($back) === 3 || in_array(substr($back, 3), $allowedReturnUrls))) { $isValidBack = true; }
        $back = $isValidBack ? filter_var($back, FILTER_SANITIZE_URL) : $defaultReturnUrl;

        $canEditAll = in_array($loggedUser['role1'] ?? '', ['veduci','schifflieder'],true) || 
                      (isset($_SESSION['username']) && $_SESSION['username'] === 'radis');
                      
        if (!$canEditAll && $uid !== (int)($loggedUser['id'] ?? 0)){
            $error_message = 'Nemáš oprávnenie pridávať absenciu inému používateľovi.';
        } else if (empty($formTargetOddelenieId)) { // Kontrola, či máme oddelenie
            // Chyba už bola nastavená vyššie
        } else if (empty($from_str) || empty($to_str) || empty($type) || $uid <= 0) {
            $error_message = 'Prosím, vyplň všetky povinné polia (Dátum od, Dátum do, Zamestnanec, Typ absencie).';
        } else {
            $from_date = null;
            $to_date = null;
            try {
                $from_date = new DateTimeImmutable($from_str);
                $to_date = new DateTimeImmutable($to_str);
            } catch (Exception $e) {
                $error_message = 'Neplatný formát zadaného dátumu.';
            }

            if ($from_date && $to_date && $to_date < $from_date) {
                $error_message = 'Dátum "do" nemôže byť skôr ako dátum "od".';
            }
            
            elseif ($from_date && $to_date && ($from_date < $today && $to_date < $today) ) { 
                // Pridaná výnimka pre používateľa 'radis'
                if (isset($_SESSION['username']) && $_SESSION['username'] === 'radis') {
                    // Radis môže pridávať absencie v minulosti - pokračujeme ďalej
                } else {
                    $error_message = 'Nie je možné pridávať absencie, ktoré celé patria do minulosti.';
                }
            }
            elseif ($from_date && $to_date) {
                
                // Úprava dátumu len pre neradis používateľov
                if ($from_date < $today && $to_date >= $today && !(isset($_SESSION['username']) && $_SESSION['username'] === 'radis')) {
                    $from_date = $today;
                }

                $pdo->beginTransaction();
                try {
                    // Najprv zmažeme všetky existujúce absencie pre tohto používateľa v danom rozsahu
                    // aby sme predišli duplicitným alebo konfliktným záznamom.
                    $stmtDeleteExistingForm = $pdo->prepare("
                        DELETE FROM absences
                        WHERE user_id = :user_id
                        AND start_date <= :end_date AND end_date >= :start_date
                    ");
                    $stmtDeleteExistingForm->execute([
                        ':user_id' => $uid,
                        ':start_date' => $from_date->format('Y-m-d'),
                        ':end_date' => $to_date->format('Y-m-d')
                    ]);
                    error_log("Zmazaných (FORM) " . $stmtDeleteExistingForm->rowCount() . " existujúcich prekrývajúcich sa absencií pre UID: $uid v rozsahu {$from_date->format('Y-m-d')} - {$to_date->format('Y-m-d')} pred pridaním novej.");

                    $stmt=$pdo->prepare("
                        INSERT INTO absences (user_id, start_date, end_date, absence_type)
                        VALUES (?, ?, ?, ?)
                    ");
                    $stmt->execute([ $uid, $from_date->format('Y-m-d'), $to_date->format('Y-m-d'), $type ]);
                    $inserted = $stmt->rowCount() > 0;

                    if ($inserted) {
                        // Určíme dátum, od ktorého sa majú mazať a prepočítavať priradenia
                        $dateToDeleteAndRecalculateFrom = $today;
                        if ($from_date > $today) {
                            $dateToDeleteAndRecalculateFrom = $from_date;
                        }
                        $deleteSuccess = deleteFutureDataFromDate($dateToDeleteAndRecalculateFrom, $formTargetOddelenieId, $pdo);
                        if (!$deleteSuccess) {
                             throw new Exception("Záznam bol vložený, ale nepodarilo sa zmazať/aktualizovať budúce priradenia.");
                        }
                        // Ak používateľ nie je 'radis', musíme explicitne prepočítať.
                        if (!isset($_SESSION['username']) || $_SESSION['username'] !== 'radis') {
                            if (function_exists('recalculateAssignmentsFromDateToToday')) {
                                $recalcSuccess = recalculateAssignmentsFromDateToToday($dateToDeleteAndRecalculateFrom, $formTargetOddelenieId, $pdo);
                                if (!$recalcSuccess) {
                                    error_log("Nepodarilo sa prepočítať priradenia po pridaní absencie (form) pre oddelenie $formTargetOddelenieId.");
                                    // Môžeme zvážiť, či tu nastaviť $error_message, alebo len logovať
                                }
                            }
                        }
                    } else {
                        
                        $stmtCheckOverlap = $pdo->prepare("SELECT COUNT(*) FROM absences WHERE user_id = :user_id AND start_date <= :end_date AND end_date >= :start_date");
                        $stmtCheckOverlap->execute([':user_id' => $uid, ':start_date' => $from_date->format('Y-m-d'), ':end_date' => $to_date->format('Y-m-d')]);
                        if ($stmtCheckOverlap->fetchColumn() > 0) {
                             throw new Exception("Záznam sa nepodarilo vložiť, pretože pre tohto používateľa už v danom období existuje iná absencia.");
                        } else {
                             throw new Exception("Záznam sa nepodarilo vložiť z neznámeho dôvodu.");
                        }
                    }

                    $pdo->commit();
                    header("Location: $back");
                    exit;
                } catch (PDOException $e) {
                    if ($pdo->inTransaction()) $pdo->rollBack();
                    $errorCode = $e->getCode(); $errorMessage = $e->getMessage();
                    error_log("DB Error adding absence (form): Code=$errorCode, Message=$errorMessage");
                    if ($errorCode == 23000) { $error_message = "Chyba: Záznam pre tohto používateľa v tomto období už pravdepodobne existuje."; }
                    else { $error_message = "Chyba pri ukladaní do databázy. Skontrolujte log pre detaily."; }
                } catch (Exception $e) {
                     if ($pdo->inTransaction()) $pdo->rollBack();
                     error_log("General Error adding absence (form) process: " . $e->getMessage());
                     $error_message = "Nastala chyba pri spracovaní: " . $e->getMessage();
                }
            }
        }
    }

    
    ?>
    <!DOCTYPE html>
    <html lang="sk">
    <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Správa absencií</title>
    <link rel="stylesheet" href="style.css">
    <style>
      .error-message { color: #dc3545; background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 10px 15px; margin-bottom: 15px; border-radius: 5px; text-align: center; }
    </style>
    </head>
    <body>
    <div class="container" style="max-width:450px">
      <div class="header">Nová absencia</div>

      <?php if (!empty($error_message)): ?>
        <div class="error-message"><?= htmlspecialchars($error_message) ?></div>
      <?php endif; ?>

      <form method="post" action="absences.php">
        <label for="start_date">Dátum od:</label>
        <input type="date" id="start_date" name="start_date"
               value="<?=htmlspecialchars($prefillDate ?: $today->format('Y-m-d'))?>" required>

        <label for="end_date">Dátum do:</label>
        <input type="date" id="end_date" name="end_date"
               value="<?=htmlspecialchars($prefillDate ?: $today->format('Y-m-d'))?>" required>

        <label for="user_id">Zamestnanec:</label>
        <select id="user_id" name="user_id" required
            <?php
            $isEditor = in_array($loggedUser['role1'] ?? '', ['veduci','schifflieder'], true);
            if (!$isEditor) echo ' disabled';
            
            if (!$isEditor) { 
                echo '><input type="hidden" name="user_id" value="' . htmlspecialchars($loggedUser['id'] ?? 0) . '" />'; 
            } else { 
                echo '>'; 
            }            
            // Zobrazíme používateľov len z aktívne zvoleného oddelenia
            $allUsersList = getAllUsers($pdo, $activeOddelenieIdForForm);
            $selectedUserIdForForm = $prefillUid ?: ($loggedUser['id'] ?? 0);

            foreach ($allUsersList as $u):
                
                if ($isEditor || $u['id'] == ($loggedUser['id'] ?? 0)):
                  $isSelected = ($u['id'] == $selectedUserIdForForm);
            ?>
              <option value="<?=htmlspecialchars($u['id'])?>" <?= $isSelected ? 'selected' : '' ?>><?=htmlspecialchars($u['name'])?></option>
          <?php endif; endforeach;?>
        </select>

        <label for="absence_type">Typ absencie:</label>
        <select id="absence_type" name="absence_type" required>
            <?php
            
            $absenceDisplayInfo = [
                'Celý deň'    => [], 'Ráno'        => [], 'Poobede'     => [], 'Iné'         => []
            ];
            foreach (array_keys($absenceDisplayInfo) as $t):
                  $isSelected = ($t === 'Celý deň'); 
            ?>
                <option value="<?=htmlspecialchars($t)?>" <?= $isSelected ? 'selected' : '' ?>><?=htmlspecialchars($t)?></option>
            <?php endforeach;?>
        </select>

        <input type="hidden" name="return_to" value="<?=htmlspecialchars($returnUrl)?>">
        
        <button type="submit">Pridať absenciu</button>
      </form>

      <div class="buttons">
        <button onclick="location.href='<?=htmlspecialchars($returnUrl)?>'" class="back-button">
            Späť na prehľad
        </button>
      </div>
    </div>
    </body>
    </html>
<?php
} 
?>
