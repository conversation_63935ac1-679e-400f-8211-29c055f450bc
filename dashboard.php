<?php
require_once 'config.php';
require_once 'functions.php';

// Overenie prihlásenia
requireLogin();

// Získanie PDO spojenia
$pdo = getDbConnection();

// Načítanie údajov prihláseného používateľa
$loggedInUserId = $_SESSION['user_id'] ?? 0;
$loggedUser = getUserById($loggedInUserId, $pdo); // Načítame info vrátane roly a mena

// Spracovanie výberu oddelenia z dropdownu
if (isset($_GET['select_department_id']) && is_numeric($_GET['select_department_id'])) {
    $new_selected_id = (int)$_GET['select_department_id'];
    $stmt_dept_name = $pdo->prepare("SELECT nazov FROM Oddelenia WHERE oddelenie_id = ?");
    $stmt_dept_name->execute([$new_selected_id]);
    $new_selected_name = $stmt_dept_name->fetchColumn();

    if ($new_selected_name) {
        $_SESSION['selected_oddelenie_id'] = $new_selected_id;
        $_SESSION['selected_oddelenie_nazov'] = $new_selected_name;
        $redirect_url = "dashboard.php";
        if (isset($_GET['date'])) {
            $redirect_url .= "?date=" . urlencode($_GET['date']);
        }
        header("Location: " . $redirect_url);
        exit;
    }
}

// Určenie aktívneho oddelenia pre zobrazenie (zvolené alebo vlastné)
$activeOddelenieId = $_SESSION['selected_oddelenie_id'] ?? $_SESSION['oddelenie_id'] ?? null;
$activeOddelenieNazov = $_SESSION['selected_oddelenie_nazov'] ?? $_SESSION['oddelenie_nazov'] ?? null;

// Načítanie všetkých oddelení pre dropdown
$allDepartmentsForDropdown = [];
try {
    $stmt_all_depts = $pdo->query("SELECT oddelenie_id, nazov FROM Oddelenia ORDER BY nazov ASC");
    if ($stmt_all_depts) {
        $allDepartmentsForDropdown = $stmt_all_depts->fetchAll(PDO::FETCH_ASSOC);
    }
} catch (PDOException $e) {
    error_log("Chyba pri načítavaní oddelení pre dropdown: " . $e->getMessage());
}

$holiday_messages = checkAndLoadHolidays($pdo);
$requested_date_str = $_GET['date'] ?? null;
$current_date = determineTargetDate($requested_date_str, $pdo);
$current_date_str = $current_date->format('Y-m-d');
$display_date_str = $current_date->format('d.m.Y');
$today_date_str = date('Y-m-d');

$prev_date_obj = findPreviousWorkingDay($current_date, $pdo);
$next_date_obj = findNextWorkingDay($current_date, $pdo);
$prev_date_str = $prev_date_obj->format('Y-m-d');
$next_date_str = $next_date_obj->format('Y-m-d');

$session_message = ''; $session_error   = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'reset_all_assignments') {
         if (isset($_SESSION['username']) && $_SESSION['username'] === 'radis') {
            try {
                $pdo->beginTransaction();
                if ($activeOddelenieId) {
                    $stmt = $pdo->prepare("DELETE a FROM assignments a JOIN users u ON a.user_id = u.id WHERE u.oddelenie_id = ?");
                    $stmt->execute([$activeOddelenieId]);
                    $stmt = $pdo->prepare("DELETE FROM assignment_metadata WHERE oddelenie_id = ?");
                    $stmt->execute([$activeOddelenieId]);
                     $_SESSION['message'] = "Všetky priradenia pre oddelenie " . htmlspecialchars($activeOddelenieNazov ?? 'N/A') . " boli úspešne zmazané.";
                } else {
                    $stmt = $pdo->prepare("DELETE FROM assignments");
                    $stmt->execute();
                    $stmt = $pdo->prepare("DELETE FROM assignment_metadata");
                    $stmt->execute();
                    $_SESSION['message'] = "Všetky priradenia (globálne) boli úspešne zmazané.";
                }
                $pdo->commit();
            } catch (Exception $e) {
                if($pdo->inTransaction()) $pdo->rollBack();
                $_SESSION['error'] = "Chyba pri mazaní priradení: " . $e->getMessage();
            }
         } else {
            $_SESSION['error'] = "Nemáte oprávnenie na túto akciu.";
         }
         header("Location: dashboard.php?date=" . $current_date_str); exit;
    } elseif ($_POST['action'] === 'reset_all_absences') {
         if (isset($_SESSION['username']) && $_SESSION['username'] === 'radis') {
            try {
                $pdo->beginTransaction();
                if ($activeOddelenieId) {
                    $stmt = $pdo->prepare("DELETE a FROM absences a JOIN users u ON a.user_id = u.id WHERE u.oddelenie_id = ?");
                    $stmt->execute([$activeOddelenieId]);
                    $todayForMetaDelete = date('Y-m-d');
                    $stmt = $pdo->prepare("DELETE FROM assignment_metadata WHERE oddelenie_id = ? AND assignment_date >= ?");
                    $stmt->execute([$activeOddelenieId, $todayForMetaDelete]);
                    $_SESSION['message'] = "Všetky absencie pre oddelenie " . htmlspecialchars($activeOddelenieNazov ?? 'N/A') . " boli zmazané. Priradenia budú prepočítané.";
                } else {
                    $stmt = $pdo->prepare("DELETE FROM absences");
                    $stmt->execute();
                    $todayForMetaDelete = date('Y-m-d');
                    $stmt = $pdo->prepare("DELETE FROM assignment_metadata WHERE assignment_date >= ?"); 
                    $stmt->execute([$todayForMetaDelete]);
                    $_SESSION['message'] = "Všetky absencie (globálne) boli zmazané. Priradenia budú prepočítané.";
                }
                $pdo->commit();
            } catch (Exception $e) {
                if($pdo->inTransaction()) $pdo->rollBack();
                $_SESSION['error'] = "Chyba pri mazaní absencií: " . $e->getMessage();
            }
         } else {
             $_SESSION['error'] = "Nemáte oprávnenie na túto akciu.";
         }
         header("Location: dashboard.php?date=" . $current_date_str); exit;
    }
}
if (isset($_SESSION['message'])) { $session_message = $_SESSION['message']; unset($_SESSION['message']); }
if (isset($_SESSION['error'])) { $session_error = $_SESSION['error']; unset($_SESSION['error']); }

// --- DOČASNÝ DEBUG VÝPIS PRE PHP PREMENNÉ (ODSTRÁNIŤ PO ODHLADENÍ!) ---
/*
echo "<pre style='background: #eee; color: #000; padding: 10px; border: 1px solid #ccc; position: relative; z-index: 9999;'>";
echo "DEBUG INFO (ODSTRÁNIŤ PO ODHLADENÍ):\n";
echo "-------------------------------------\n";
echo "SESSION username: "; var_dump($_SESSION['username'] ?? 'NIE JE NASTAVENÉ');
echo "Logged User Data (loggedUser): "; var_dump($loggedUser);
echo "Logged User Role (loggedUser['role1']): "; var_dump($loggedUser['role1'] ?? 'NIE JE NASTAVENÉ');
echo "Active Oddelenie ID: "; var_dump($activeOddelenieId);
echo "</pre>";
*/
// --- KONIEC DOČASNÉHO DEBUG VÝPISU ---

$generation_message = ''; $assignments_today = []; $presentUsersToday = [];
$meta = null;
if ($activeOddelenieId) {
    $meta = getAssignmentMetadataMeta($current_date, $activeOddelenieId, $pdo);
} else {
    $generation_message = "Chyba: Oddelenie pre zobrazenie nie je definované. Nie je možné načítať priradenia.";
}

if ($activeOddelenieId && empty($generation_message)) {
    if ($current_date_str < $today_date_str) { 
        $generation_message = "Zobrazujú sa historické dáta pre oddelenie " . htmlspecialchars($activeOddelenieNazov ?? 'N/A') . ".";
        $assignments_today = getAssignmentsForDate($current_date, $activeOddelenieId, $pdo);
        $presentUsersToday = getPresentUsers($current_date, $activeOddelenieId, $pdo);
        if (empty($assignments_today)) $generation_message = "Pre tento minulý deň neboli nájdené žiadne priradenia pre oddelenie " . htmlspecialchars($activeOddelenieNazov ?? 'N/A') . ".";
    } elseif (!isWorkingDay($current_date, $pdo)) {
        $generation_message = "Voľný deň - žiadne priradenia.";
    } elseif ($meta !== null) { 
        $generation_message = "Priradenia pre oddelenie " . htmlspecialchars($activeOddelenieNazov ?? 'N/A') . " načítané z uložených metadát.";
        $assignments_today = getAssignmentsForDate($current_date, $activeOddelenieId, $pdo);
        $presentUsersToday = getPresentUsers($current_date, $activeOddelenieId, $pdo);
    } else {
        try {
            $presentUsersToday = getPresentUsers($current_date, $activeOddelenieId, $pdo);
            $assignments_calculated = calculateDailyAssignments($current_date, $activeOddelenieId, $pdo);
            saveAssignments($current_date, $assignments_calculated, $activeOddelenieId, $pdo);
            $signature = md5(json_encode($assignments_calculated));
            upsertAssignmentMetadataMeta($current_date, $signature, null, null, $activeOddelenieId, $pdo, null);
            $assignments_today = getAssignmentsForDate($current_date, $activeOddelenieId, $pdo);
            $generation_message = "Priradenia pre oddelenie " . htmlspecialchars($activeOddelenieNazov ?? 'N/A') . " boli vypočítané a uložené.";
        } catch (Exception $e) {
            $generation_message = "Chyba pri generovaní/ukladaní priradení pre oddelenie " . htmlspecialchars($activeOddelenieNazov ?? 'N/A') . ": " . $e->getMessage();
            error_log("Error generating/saving assignments for $current_date_str, department $activeOddelenieId: " . $e->getMessage());
            $assignments_today = getAssignmentsForDate($current_date, $activeOddelenieId, $pdo); 
            if(empty($presentUsersToday)) { 
                $presentUsersToday = getPresentUsers($current_date, $activeOddelenieId, $pdo);
            }
        }
    }
}

list($today_schifflieder, $today_skladnik, $today_machines, $today_kontrola) = 
    processAssignmentsForDisplay($assignments_today, $presentUsersToday, $activeOddelenieNazov);

$absenceDisplayInfo = [
    'Celý deň'    => ['shortcut' => 'D', 'color' => '#8bc34a', 'class' => 'absence-d'],
    'Ráno'        => ['shortcut' => 'R', 'color' => '#2196f3', 'class' => 'absence-r'],
    'Poobede'     => ['shortcut' => 'P', 'color' => '#ff9800', 'class' => 'absence-p'],
    'Iné'         => ['shortcut' => 'X', 'color' => '#607d8b', 'class' => 'absence-x']
];
?>
<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Dashboard - Priradenia</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .assignments-table td { background-color: #272727; transition: background-color 0.3s ease; }
        .assignments-table td.role-label { background-color: #3a3a3a; font-weight: bold; color: #ccc; min-width: 100px;}
        .assignments-table tr.role-schifflieder td:not(.role-label) { background-color: #2a3a3a; }
        .assignments-table tr.role-skladnik td:not(.role-label) { background-color: #3a3a2a; }
        .assignments-table tr.role-strojnik td:not(.role-label) { background-color: #3a2a2a; }
        .assignments-table tr.role-kontrola td:not(.role-label) { background-color: #2a2a3a; }
        .assignments-table tr:hover td:not(.role-label) { background-color: #444; }
        .buttons button.danger-button, .buttons a.danger-button { background-color: #dc3545; color: white; border-color: #dc3545; }
        .buttons button.danger-button:hover, .buttons a.danger-button:hover { background-color: #c82333; border-color: #bd2130; }
        .inline-form-button { display: inline-block; margin: 8px; vertical-align: top; }
        .inline-form-button button { margin: 0; }

        .clickable-label {
            cursor: pointer;
            transition: background-color 0.2s;
            user-select: none; 
        }
        .clickable-label:hover {
            background-color: #555 !important;
        }

        .section-heading-style {
            text-align: center;
            font-size: 1.2em;
            color: #666;
            font-weight: bold; 
            margin-bottom: 15px; 
            text-shadow: none; 
            border-bottom: none; 
            padding-bottom: 0; 
            letter-spacing: normal; 
        }

        .assignment-section { margin-bottom: 20px; }
        .assignments-table { width: 100%; border-collapse: collapse; margin-bottom: 15px; }
        .assignments-table th, .assignments-table td { padding: 4px 8px; border: 1px solid #444; text-align: left; vertical-align: top; }
        
        .kontrola-list { list-style: none; margin-bottom: 0; padding-left: 0; text-align: left; }
        .kontrola-list li { 
            margin-bottom: 1px; 
            padding-bottom: 1px; 
            border-bottom: 1px dotted #444; 
            text-align: left; 
            padding-left: 8px; 
        }
        .kontrola-list li:last-child { border-bottom: none; }
        .kontrola-list li.highlight-logged-user {
            background-color: #dc3545;
            color: #ffffff !important;
            font-weight: bold;
            padding: 2px 6px !important; 
            margin: 1px 0 1px -8px; 
            border-radius: 3px;
            display: block !important; 
        }

        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 10px;
        }
        .navigation .date-controls {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px;
        }
        .navigation .date-display {
            font-size: 1.6em;
            font-weight: bold;
            color: #fff;
        }
        .navigation .today-button {
            font-size: 0.8em;
            padding: 4px 12px;
            background-color: #444;
            color: #eee;
            border-radius: 15px;
            text-decoration: none;
            border: 1px solid #555;
            transition: background-color 0.2s;
            margin-top: 8px;
        }
        .navigation .today-button:hover {
            background-color: #555;
        }
        .navigation .nav-arrow {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 45px;
            height: 45px;
            background-color: #d35400;
            color: #ffffff;
            font-weight: bold;
            border: 2px solid #f39c12;
            border-radius: 50%;
            box-shadow: 0 0 8px rgba(211, 84, 0, 0.5);
            text-decoration: none;
            transition: all 0.2s ease-in-out;
            font-size: 1.8em;
        }
        .navigation .nav-arrow:hover {
            background-color: #e67e22;
            transform: scale(1.05);
        }
        .navigation .nav-arrow-hidden {
            visibility: hidden;
            width: 45px; /* Zachováva rovnakú veľkosť pre udržanie rozloženia */
            height: 45px;
        }

        .highlight-logged-user,
        .user-name.highlight-logged-user,
        span.highlight-logged-user {
            background-color: #dc3545 !important; 
            color: #ffffff !important; 
            font-weight: bold !important;
            padding: 2px 6px !important; 
            border-radius: 4px !important;
            display: inline-block !important; 
            margin: 1px 0 !important; 
        }

        .assignments-table td > div {
            margin: 1px 0;
            line-height: 1.3;
            min-height: 18px; 
            display: block; 
        }

        .assignments-table td.role-label > div {
            padding: 2px 0;
        }

        .assignments-table td:not(.role-label) > div:not(.absent-user) {
            padding: 2px 0 2px 8px; 
        }

        .monthly-calendar-section {
            margin-top: 30px;
            margin-bottom: 20px;
            background-color: #1e1e1e;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.5);
            border: 1px solid #333;
        }

        .calendar-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            background-color: #2d2d2d;
            border-radius: 8px;
            padding: 12px 18px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .calendar-navigation a {
            color: #8ab4f8;
            text-decoration: none;
            font-size: 0.95em;
            transition: all 0.3s;
            padding: 8px 12px;
            border-radius: 6px;
            background-color: #333;
            border: 1px solid #444;
        }

        .calendar-navigation a:hover {
            color: #fff;
            background-color: #444;
            transform: translateY(-2px);
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
        }

        .calendar-navigation span {
            font-weight: bold;
            color: #f0f0f0;
            font-size: 1.2em;
            background-color: #333;
            padding: 8px 15px;
            border-radius: 6px;
            border: 1px solid #444;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.2);
        }

        .calendar-container {
            overflow-x: auto;
            border-radius: 10px;
            border: 1px solid #444;
            background-color: #1a1a1a;
            box-shadow: inset 0 0 15px rgba(0,0,0,0.4);
            padding:10px;
        }

        .calendar-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 3px;
            table-layout: fixed;
        }

        .calendar-table th, 
        .calendar-table td {
            text-align: center;
            padding: 10px;
            height: 50px; 
            vertical-align: top;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .calendar-table th {
            background-color: #333;
            color: #f0f0f0;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            letter-spacing: 1px;
            border:none;
        }

        .calendar-table td {
            background-color: #2a2a2a;
            color: #ddd;
            position: relative;
            border: 1px solid #3a3a3a;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        .calendar-table td:hover {
            background-color: #3a3a3a;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .calendar-table td.other-month {
            color: #666;
            background-color: #222;
            border: 1px solid #2a2a2a;
            box-shadow: none;
        }
        
        .calendar-table td.today {
            background-color: #d35400 !important; 
            color: #ffffff !important;
            font-weight: bold !important;
            border: 2px solid #f39c12 !important; 
            box-shadow: 0 0 8px rgba(211, 84, 0, 0.5) !important;
            position: relative;
            z-index: 2; 
        }
        .calendar-table td.today::before {
            content: "DNES";
            position: absolute;
            top: 2px;
            left: 2px;
            font-size: 0.6em;
            background-color: #f39c12;
            color: #333;
            padding: 1px 3px;
            border-radius: 2px;
            font-weight: bold;
            box-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }
      
        .calendar-table td.non-working-day,
        .calendar-table td.holiday { 
            background-color: #3d2a2a; 
            background-image: linear-gradient(45deg, #3d2a2a 25%, #4a3030 25%, #4a3030 50%, #3d2a2a 50%, #3d2a2a 75%, #4a3030 75%, #4a3030 100%);
            background-size: 10px 10px;
            border: 1px solid #5a3a3a;
        }

        .calendar-table td.clickable-day {
            cursor: pointer;
            position: relative;
        }
        .calendar-table td.clickable-day:hover {
            background-color: #3a3a4a; 
        }
        .calendar-table td.clickable-day:not(.has-absence):hover::after { 
            content: "+"; 
            position: absolute;
            top: 5px;
            right: 5px;
            font-weight: bold;
            color: #4CAF50; 
            font-size: 1.2em;
            background-color: rgba(0,0,0,0.3);
            width:20px; height:20px; line-height:20px; text-align:center; border-radius:50%;
        }
        .calendar-table td.clickable-day.has-absence:hover::after { 
            content: "×"; 
            color: #ff5555; 
            position: absolute;
            top: 5px;
            right: 5px;
            font-weight: bold;
            font-size: 1.2em;
            background-color: rgba(0,0,0,0.3);
            width:20px; height:20px; line-height:20px; text-align:center; border-radius:50%;
        }

        .day-number {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 1.2em;
            color: inherit; 
        }
        .holiday-name {
            font-size: 0.7em;
            color: #ffaaaa; 
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .vacation-indicator { 
            position: absolute;
            bottom: 3px; 
            right: 3px;  
            color: white;
            min-width: 20px; 
            height: 20px;
            line-height: 20px; 
            padding: 0 5px; 
            border-radius: 10px; 
            font-size: 0.75em;
            font-weight: bold;
            box-shadow: 0 1px 2px rgba(0,0,0,0.4);
            text-align: center;
            box-sizing: border-box;
        }

        .calendar-legend {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            flex-wrap: wrap;
            background-color: #2d2d2d;
            border-radius: 8px;
            padding: 12px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin: 5px 10px; 
            font-size: 0.85em; 
            color: #ddd;
            padding: 5px 8px; 
            border-radius: 6px;
            transition: background-color 0.2s;
            border: 1px solid transparent;
        }
        .legend-item:hover {
            background-color: #3a3a3a;
            border-color: #444;
        }
        .legend-color {
            display: inline-block;
            width: 16px; 
            height: 16px; 
            margin-right: 7px; 
            border: 1px solid #555;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.3);
        }
        .legend-color.today-legend { 
            background-color: #d35400 !important;
            border: 1px solid #f39c12 !important;
            box-shadow: 0 0 4px rgba(211, 84, 0, 0.5) !important;
        }
         .legend-item.today-legend-text { 
            font-weight: bold !important;
            color: #f0f0f0 !important;
            background-color: rgba(211, 84, 0, 0.1) !important;
            border: 1px solid rgba(211, 84, 0, 0.2) !important;
        }
        .legend-color.non-working-day-legend { 
            background-color: #3d2a2a;
            background-image: linear-gradient(45deg, #3d2a2a 25%, #4a3030 25%, #4a3030 50%, #3d2a2a 50%, #3d2a2a 75%, #4a3030 75%, #4a3030 100%);
            background-size: 10px 10px;
        }
       
        @media (max-width: 768px) {
            .monthly-calendar-section { padding: 15px; }
            .calendar-table th, .calendar-table td { padding: 8px 5px; font-size: 0.9em; height: 45px; }
            .day-number { font-size: 1.1em; }
            .holiday-name { display: none; } 
            .legend-item { margin: 3px 6px; font-size: 0.8em; padding: 4px 6px;}
            .calendar-navigation a { font-size: 0.85em; padding: 6px 8px; }
            .calendar-navigation span { font-size: 1.1em; padding: 6px 10px; }
        }
        @media (max-width: 480px) {
            .monthly-calendar-section { padding: 10px; }
            .calendar-table th, .calendar-table td { padding: 5px 3px; font-size: 0.8em; height: 40px; }
            .day-number { font-size: 1em; margin-bottom: 2px; }
            .calendar-navigation a { font-size: 0.8em; padding: 5px 6px; }
            .calendar-navigation span { font-size: 1em; padding: 5px 8px; }
            .vacation-indicator { font-size: 0.7em; min-width: 18px; height: 18px; line-height: 18px; padding: 0 4px;}
            .legend-item {flex-basis: 100%; justify-content: flex-start;} 
        }

        .button-group { margin: 10px 0; display: flex; justify-content: center; flex-wrap: wrap; }
        .button-group button { margin: 5px 10px; }
        .logout-group { margin-top: 15px; }
        @media (max-width: 768px) {
            .button-group { flex-direction: column; align-items: center; }
            .button-group button { width: 80%; margin: 5px auto; }
        }

        .messages-section { margin-top: 20px; margin-bottom: 15px; }
        .messages-section .message { margin-bottom: 10px; }
        
        .loading-indicator {
            position: fixed; top: 0; left: 0; right: 0; bottom: 0;
            background-color: rgba(0, 0, 0, 0.7); 
            z-index: 10000; 
            display: flex; flex-direction: column;
            justify-content: center; align-items: center;
            color: white; text-align: center;
        }
        .spinner {
            border: 5px solid #f3f3f3; border-top: 5px solid #3498db; 
            border-radius: 50%; width: 50px; height: 50px;
            animation: spin 1s linear infinite; margin-bottom: 15px;
        }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }

        .absence-dropdown {
            background-color: #333; border: 1px solid #555; border-radius: 5px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3); z-index: 10000; 
            min-width: 180px; max-width: 250px; padding: 5px 0; position: absolute; 
        }
        .absence-option { padding: 8px 15px; cursor: pointer; color: #ddd; transition: background-color 0.2s; }
        .absence-option:hover { background-color: #444; color: #fff; }
        .absence-close {
            position: absolute; top: 5px; right: 5px; width: 20px; height: 20px;
            line-height: 20px; text-align: center; cursor: pointer; color: #aaa;
            font-size: 14px; border-radius: 50%;
        }
        .absence-close:hover { background-color: #555; color: #fff; }

        .role-absent td { border-top: 1px dashed #555; background-color: #2a2a2a; }
        .absent-users { color: #999; font-size: 0.9em; }
        .absent-user {
            margin: 1px 0; display: flex; justify-content: space-between;
            align-items: center; padding-left: 8px; line-height: 1.3; min-height: 18px;   
        }
        .absent-name { flex: 1; }
        
        .assignments-table td:not(.role-label) div.highlight-logged-user,
        .assignments-table td:not(.role-label) span.highlight-logged-user { 
            padding: 2px 6px; margin: 1px 0; line-height: 1.3; min-height: 18px; display: inline-block; 
        }
        
        .machine-toggle-cell { cursor: pointer; position: relative; }
        .machine-toggle-cell:hover { background-color: rgba(255,255,255,0.1); }
        .machine-toggle-icon { display: inline-block; margin-left: 8px; font-weight: bold; }
        .machine-toggle-cell[data-active="1"] .machine-toggle-icon { color: #4CAF50; } 
        .machine-toggle-cell[data-active="0"] .machine-toggle-icon { color: #f44336; } 
    </style>
    <style>
        /* Štýly pre modálne okno */
        .modal { display: none; position: fixed; z-index: 10001; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.7); }
        .modal-content { background-color: #2c2c2c; margin: 15% auto; padding: 25px; border: 1px solid #555; width: 80%; max-width: 450px; border-radius: 8px; box-shadow: 0 5px 15px rgba(0,0,0,0.5); }
        .modal-header { padding-bottom: 15px; border-bottom: 1px solid #444; margin-bottom: 20px; } .modal-header h2 { margin: 0; font-size: 1.4em; }
        .modal-body select, .modal-body p { width: 100%; margin-bottom: 15px; }
        .modal-footer { text-align: right; margin-top: 20px; } .modal-footer button { margin-left: 10px; }
    </style>
    <style>
        /* Štýly pre novú pätičku stránky */
        .page-footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #444;
            background-color: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .page-footer .footer-title {
            font-size: 1.3em;
            color: #ccc;
            margin-bottom: 15px;
        }
        .page-footer .footer-controls {
            display: flex; justify-content: center; align-items: center;
            gap: 20px; flex-wrap: wrap; margin-bottom: 15px;
        }
        .page-footer .footer-user-info { font-size: 0.9em; color: #999; }
        .page-footer .footer-user-info a { color: #8ab4f8; text-decoration: none; }
        .page-footer .footer-user-info a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <div id="main-content-swipe-area">
            <div class="navigation">
                <?php if ($current_date_str >= $today_date_str || findPreviousWorkingDay($current_date, $pdo)->format('Y-m-d') < $current_date_str ): // Logika pre zobrazenie šípky zostáva ?>
                    <a href="dashboard.php?date=<?php echo $prev_date_str; ?>&calendar_month=<?php echo $current_date->format("Y-m"); ?>" class="nav-arrow" title="Predošlý deň (šípka vľavo)" id="prev-day-link">&laquo;</a>
                <?php else: ?>
                    <span class="nav-arrow-hidden">&laquo;</span>
                <?php endif; ?>
                <div class="date-controls">
                    <span class="date-display"><?php echo htmlspecialchars($display_date_str); ?></span>
                    <?php if ($current_date_str !== $today_date_str): ?>
                        <a href="dashboard.php" class="today-button">Dnes</a>
                    <?php endif; ?>
                </div>
                <a href="dashboard.php?date=<?php echo $next_date_str; ?>&calendar_month=<?php echo $current_date->format("Y-m"); ?>" class="nav-arrow" title="Nasledujúci deň (šípka vpravo)" id="next-day-link">&raquo;</a>
            </div>

            <div class="assignment-section">
                <h3 class="section-heading-style">
                    Priradenia
                    <?php
                $isAuthorizedForManualRestart = (isset($loggedUser['role1']) && in_array($loggedUser['role1'], ['veduci', 'schifflieder'], true)) || (isset($_SESSION['username']) && $_SESSION['username'] === 'radis');
                if ($isAuthorizedForManualRestart):
                ?>
                    <button id="force-start-trigger" title="Vynútiť štart prideľovania od konkrétneho strojníka" style="margin-left: 15px; font-size: 0.7em; vertical-align: middle;">Manuálny Reštart</button>
                <?php endif; ?>
            </h3>
            <?php
                $loggedInUserName = $loggedUser['name'] ?? null;

                if (empty($assignments_today) && empty($generation_message) && $today_schifflieder === '---' && $today_skladnik === null) {
                    if ($current_date_str < $today_date_str) {} 
                 elseif (!isWorkingDay($current_date, $pdo)) {} 
                 else { echo '<div class="message info">Pre tento deň sa nepodarilo načítať priradenia.</div>'; }
             } else {
            ?>
                <table class="assignments-table">
                     <tbody>
                        <tr class="role-schifflieder">
                            <td class="role-label clickable-label" id="schifflieder-reset-trigger" title="Kliknutím resetujete cyklus Schiffliederov"><div>Schifflieder:</div></td>
                            <td>
                                <?php 
                                $schiffText = strip_tags($today_schifflieder, '<br><strong></strong>'); 
                                $schiffleaders = array_filter(array_map('trim', explode('<br>', $schiffText)));
                                
                                if (empty($schiffleaders) || $today_schifflieder === '---') {
                                    echo '<div><span class="empty-assignment">---</span></div>';
                                } else {
                                    foreach ($schiffleaders as $index => $leaderHtml) {
                                        echo '<div>';
                                        $leaderNameOnly = strip_tags($leaderHtml);
                                        $baseNameForComparison = $leaderNameOnly;
                                        if (substr($leaderNameOnly, -strlen(' (Zástup)')) === ' (Zástup)') {
                                            $baseNameForComparison = trim(substr($leaderNameOnly, 0, strlen($leaderNameOnly) - strlen(' (Zástup)')));
                                        }
                                        $userId = null;
                                        $allUsersForLookup = $presentUsersToday; 
                                        foreach ($allUsersForLookup as $uId => $user) {
                                            if ($user['name'] === $baseNameForComparison) {
                                                $userId = $uId;
                                                break;
                                            }
                                        }
                                        if (!$userId && ($baseNameForComparison === 'Radovan Trstenský' || $baseNameForComparison === 'Dominika Hronská')) {
                                             $userGlob = null;
                                             if ($baseNameForComparison === 'Radovan Trstenský') $userGlob = $pdo->query("SELECT id FROM users WHERE username='radis'")->fetch();
                                             if ($baseNameForComparison === 'Dominika Hronská') $userGlob = $pdo->query("SELECT id FROM users WHERE username='dominika'")->fetch();
                                             if ($userGlob) $userId = $userGlob['id'];
                                        }
                                        $isLoggedUser = ($userId === $loggedInUserId);
                                        $highlightClass = $isLoggedUser ? ' highlight-logged-user' : '';
                                        $displayName = formatUserDisplayName($leaderNameOnly); 
                                        $onClickAttr = ($userId) ? ' onclick="handleUserClick(\'' . htmlspecialchars(strip_tags($displayName)) . '\', ' . $userId . ', false, event)"' : '';
                                        $finalDisplay = (strpos($leaderHtml, '<strong>') !== false) ? '<strong>' . htmlspecialchars($displayName) . '</strong>' : htmlspecialchars($displayName);
                                        if(strpos($leaderNameOnly, ' (Zástup)') !== false) { 
                                            $finalDisplay = htmlspecialchars($displayName);
                                        }
                                        echo '<span class="user-name' . $highlightClass . '"' . $onClickAttr . '>' . $finalDisplay . '</span>';
                                        echo '</div>';
                                    }
                                }
                                ?>
                            </td>
                        </tr>
                        <tr class="role-skladnik">
                            <td class="role-label clickable-label" id="skladnik-reset-trigger" title="Kliknutím resetujete cyklus skladníkov"><div>Skladník:</div></td>
                            <td><div>
                                <?php 
                                if ($today_skladnik && $today_skladnik !== '---') {
                                    $originalSkladnikName = $today_skladnik;
                                    $userId = null;
                                    foreach ($presentUsersToday as $uId => $user) {
                                        if ($user['name'] === $originalSkladnikName) {
                                            $userId = $uId;
                                            break;
                                        }
                                    }
                                    $isLoggedUser = ($userId === $loggedInUserId);
                                    $highlightClass = $isLoggedUser ? ' highlight-logged-user' : '';
                                    $displayName = formatUserDisplayName($originalSkladnikName);
                                    $onClickAttr = ($userId) ? ' onclick="handleUserClick(\'' . htmlspecialchars($displayName) . '\', ' . $userId . ', false, event)"' : '';
                                    echo '<span class="user-name' . $highlightClass . '"' . $onClickAttr . '>' . htmlspecialchars($displayName) . '</span>';
                                } else {
                                    echo '<span class="empty-assignment">---</span>';
                                }
                                ?>
                            </div></td>
                        </tr>
                        <?php foreach ($today_machines as $machineNum => $userName): ?>
                        <tr class="role-strojnik">
                            <td class="role-label machine-toggle-cell" data-machine="<?php echo $machineNum; ?>" data-date="<?php echo $current_date_str; ?>">
                                <div> 
                                Stroj <?php echo $machineNum; ?>:
                                <?php if (isset($_SESSION['username']) && $_SESSION['username'] === 'radis'): ?>
                                    <span class="machine-toggle-icon"><?php echo isActiveMachine($machineNum, $current_date, $activeOddelenieId, $pdo) ? '✓' : '✗'; ?></span>
                                <?php endif; ?>
                                </div>
                            </td>
                            <td><div>
                                <?php 
                                if ($userName && $userName !== '---') {
                                    $originalMachineUserName = $userName;
                                    $userId = null;
                                    foreach ($presentUsersToday as $uId => $user) {
                                        if ($user['name'] === $originalMachineUserName) {
                                            $userId = $uId;
                                            break;
                                        }
                                    }
                                    $isLoggedUser = ($userId === $loggedInUserId);
                                    $highlightClass = $isLoggedUser ? ' highlight-logged-user' : '';
                                    $displayName = formatUserDisplayName($originalMachineUserName);
                                    $onClickAttr = ($userId) ? ' onclick="handleUserClick(\'' . htmlspecialchars($displayName) . '\', ' . $userId . ', false, event)"' : '';
                                    echo '<span class="user-name' . $highlightClass . '"' . $onClickAttr . '>' . htmlspecialchars($displayName) . '</span>';
                                } else {
                                    echo '<span class="empty-assignment">---</span>';
                                }
                                ?>
                            </div></td>
                        </tr>
                        <?php endforeach; ?>
                        <tr class="role-kontrola">
                            <td class="role-label"><div>Kontrola:</div></td>
                            <td> <?php if (!empty($today_kontrola)): ?>
                                    <ul class="kontrola-list">
                                        <?php foreach ($today_kontrola as $kontrolaName): ?>
                                            <?php
                                            $originalKontrolaName = $kontrolaName;
                                            $userId = null;
                                            foreach ($presentUsersToday as $uId => $user) {
                                                if ($user['name'] === $originalKontrolaName) {
                                                    $userId = $uId;
                                                    break;
                                                }
                                            }
                                            $isLoggedUser = ($userId === $loggedInUserId);
                                            $liClass = $isLoggedUser ? 'highlight-logged-user' : ''; 
                                            $displayName = formatUserDisplayName($originalKontrolaName);
                                            $onClickAttr = ($userId) ? ' onclick="handleUserClick(\'' . htmlspecialchars($displayName) . '\', ' . $userId . ', false, event)"' : '';
                                            echo '<li class="' . $liClass . '">'; 
                                            $spanClassForHighlight = ($isLoggedUser && !$liClass) ? ' highlight-logged-user' : '';
                                            echo '<span class="user-name' . $spanClassForHighlight . '"' . $onClickAttr . '>' . htmlspecialchars($displayName) . '</span>';
                                            echo '</li>';
                                            ?>
                                        <?php endforeach; ?>
                                    </ul>
                                <?php else: ?>
                                   <div><span class="empty-assignment">---</span></div> 
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php
                        $absentUsersToday = getAbsentUsersForDate($current_date, $activeOddelenieId, $pdo); 
                        if (!empty($absentUsersToday)) {
                            echo '<tr class="role-absent">';
                            echo '<td class="role-label"><div>Neprítomní:</div></td>';
                            echo '<td class="absent-users">'; 
                            foreach ($absentUsersToday as $absence) {
                                echo '<div class="absent-user">'; 
                                $displayName = formatUserDisplayName($absence['user_name']);
                                $onClickAttr = ($absence['user_id']) ? ' onclick="handleUserClick(\'' . htmlspecialchars($displayName) . '\', ' . $absence['user_id'] . ', true, event)"' : '';
                                echo '<span class="absent-name user-name"' . $onClickAttr . '>' . htmlspecialchars($displayName) . '</span>';
                                echo '</div>';
                            }
                            echo '</td>';
                            echo '</tr>';
                        }
                        ?>
                    </tbody>
                </table>
            <?php } ?>
            </div>
        </div>

        <div class="monthly-calendar-section">
            <h3 class="section-heading-style">Moje absencie</h3>
            <?php
            $calendarMonth = isset($_GET['calendar_month']) ? new DateTime($_GET['calendar_month'] . '-01') : new DateTime($current_date_str);
            $currentMonthNum = $calendarMonth->format('n');
            $currentYearNum = $calendarMonth->format('Y');
            $skMonths = [1=>'Január',2=>'Február',3=>'Marec',4=>'Apríl',5=>'Máj',6=>'Jún',
                         7=>'Júl',8=>'August',9=>'September',10=>'Október',11=>'November',12=>'December'];
            $firstDayOfMonth = new DateTime("$currentYearNum-$currentMonthNum-01");
            $lastDayOfMonth = new DateTime($firstDayOfMonth->format('Y-m-t'));
            $prevMonthObj = clone $firstDayOfMonth; $prevMonthObj->modify('-1 month');
            $nextMonthObj = clone $firstDayOfMonth; $nextMonthObj->modify('+1 month');
            $holidays = getHolidaysForMonth($currentMonthNum, $currentYearNum, $pdo);
            $userAbsencesForMonth = getUserAbsencesForMonthWithType($loggedInUserId, $currentMonthNum, $currentYearNum, $pdo);
            $today = new DateTime(); $todayString = $today->format('Y-m-d');
            ?>
            <div class="calendar-navigation">
                <a href="?date=<?php echo $current_date_str; ?>&calendar_month=<?php echo $prevMonthObj->format('Y-m'); ?>">&laquo; <?php echo $skMonths[$prevMonthObj->format('n')] . ' ' . $prevMonthObj->format('Y'); ?></a>
                <span><?php echo $skMonths[$currentMonthNum] . ' ' . $currentYearNum; ?></span>
                <a href="?date=<?php echo $current_date_str; ?>&calendar_month=<?php echo $nextMonthObj->format('Y-m'); ?>"><?php echo $skMonths[$nextMonthObj->format('n')] . ' ' . $nextMonthObj->format('Y'); ?> &raquo;</a>
            </div>
            <div class="calendar-container">
                <table class="calendar-table">
                    <thead><tr><th>Po</th><th>Ut</th><th>St</th><th>Št</th><th>Pi</th><th>So</th><th>Ne</th></tr></thead>
                    <tbody>
                        <?php
                        $firstDayOfWeek = (int)$firstDayOfMonth->format('N');
                        $startDate = clone $firstDayOfMonth;
                        if ($firstDayOfWeek > 1) { $startDate->modify('-' . ($firstDayOfWeek - 1) . ' days'); }
                        $lastDayOfWeek = (int)$lastDayOfMonth->format('N');
                        $endDate = clone $lastDayOfMonth;
                        if ($lastDayOfWeek < 7) { $endDate->modify('+' . (7 - $lastDayOfWeek) . ' days'); }
                        $currentCalDate = clone $startDate;
                        while ($currentCalDate <= $endDate) {
                            if ($currentCalDate->format('N') == 1) echo '<tr>';
                            $dateString = $currentCalDate->format('Y-m-d');
                            $isCurrentMonth = $currentCalDate->format('m') == $currentMonthNum;
                            $isToday = $dateString == $todayString;
                            $isWorkDayResult = isWorkingDay($currentCalDate, $pdo); 
                            $isHoliday = isset($holidays[$dateString]);
                            $absenceTypeOnDay = $userAbsencesForMonth[$dateString] ?? null; 
                            $cellClasses = [];
                            if (!$isCurrentMonth) $cellClasses[] = 'other-month';
                            if ($isToday) $cellClasses[] = 'today';
                            if (!$isWorkDayResult) $cellClasses[] = 'non-working-day'; 
                            if ($isHoliday && $isWorkDayResult) $cellClasses[] = 'holiday'; 
                            if ($absenceTypeOnDay && isset($absenceDisplayInfo[$absenceTypeOnDay])) {
                                $cellClasses[] = 'has-absence'; 
                                $cellClasses[] = $absenceDisplayInfo[$absenceTypeOnDay]['class']; 
                            }
                            $clickableAttr = '';
                            if ($isCurrentMonth && $isWorkDayResult) { 
                                $cellClasses[] = 'clickable-day';
                                if ($absenceTypeOnDay) { 
                                    $clickableAttr = ' onclick="confirmDeleteVacation(\'' . $dateString . '\', ' . $loggedInUserId . ')"';
                                } else {
                                    $clickableAttr = ' onclick="addVacation(\'' . $dateString . '\', ' . $loggedInUserId . ', \'' . htmlspecialchars($loggedUser['name'] ?? '') . '\', event)"';
                                }
                            }
                            echo '<td class="' . implode(' ', array_unique($cellClasses)) . '"' . $clickableAttr . '>';
                            echo '<div class="day-number">' . $currentCalDate->format('j') . '</div>';
                            if ($absenceTypeOnDay && isset($absenceDisplayInfo[$absenceTypeOnDay]) && $isCurrentMonth) {
                                $displayInfo = $absenceDisplayInfo[$absenceTypeOnDay];
                                echo '<div class="vacation-indicator" style="background-color:' . htmlspecialchars($displayInfo['color']) . ';" title="' . htmlspecialchars($absenceTypeOnDay) . '">' . htmlspecialchars($displayInfo['shortcut']) . '</div>';
                            }
                            echo '</td>';
                            if ($currentCalDate->format('N') == 7) echo '</tr>';
                            $currentCalDate->modify('+1 day');
                        }
                        ?>
                    </tbody>
                </table>
            </div>
            <div class="calendar-legend">
                <div class="legend-item today-legend-text"><span class="legend-color today-legend"></span> Dnešný deň</div>
                <div class="legend-item"><span class="legend-color non-working-day-legend"></span> Víkend/Sviatok</div>
                <?php foreach ($absenceDisplayInfo as $type => $info): ?>
                    <div class="legend-item">
                        <span class="legend-color" style="background-color: <?php echo htmlspecialchars($info['color']); ?>;"></span>
                        <?php echo htmlspecialchars($type) . " (" . htmlspecialchars($info['shortcut']) . ")"; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <div class="buttons">
            <?php if (isset($loggedUser['role1']) && in_array($loggedUser['role1'], ['veduci', 'schifflieder'], true)): ?>
                <button onclick="location.href='machines.php'">Nastavenie strojov</button>
            <?php endif; ?>
            <div class="button-group">
                <button onclick="location.href='weekly_schedule.php'">Týždenný Rozpis</button>
                <button onclick="location.href='weekly_absences.php'">Týždenné Dovolenky</button>
            </div>
            <div class="button-group">
                <button onclick="location.href='schedule_view.php'">Mesačný Rozpis</button>
                <button onclick="location.href='monthly_overview.php'">Mesačné Dovolenky</button>
            </div>
             <div class="button-group">
                <button onclick="location.href='request_status.php'">Zistenie stavu žiadosti</button>
            </div>
            <div class="button-group logout-group">
                <button onclick="location.href='logout.php'">Odhlásiť sa</button>
            </div>
            <hr style="border-color: #444; margin: 20px 0;">
            <?php if (isset($_SESSION['username']) && $_SESSION['username'] === 'radis'): ?>
                <form action="dashboard.php?date=<?php echo $current_date_str; ?>" method="POST" class="inline-form-button reset-form" style="margin-right: 15px;" onsubmit="return confirm('Naozaj chcete natrvalo ZMAZAŤ VŠETKY priradenia pre aktuálne zvolené oddelenie (<?php echo htmlspecialchars($activeOddelenieNazov ?? 'N/A'); ?>)? Táto akcia je nezvratná!');">
                    <input type="hidden" name="action" value="reset_all_assignments">
                    <button type="submit" class="danger-button">Zmazať Priradenia (<?php echo htmlspecialchars($activeOddelenieNazov ?? 'N/A'); ?>)</button>
                </form>
                <form action="dashboard.php?date=<?php echo $current_date_str; ?>" method="POST" class="inline-form-button reset-form" onsubmit="return confirm('Naozaj chcete natrvalo ZMAZAŤ VŠETKY absencie pre aktuálne zvolené oddelenie (<?php echo htmlspecialchars($activeOddelenieNazov ?? 'N/A'); ?>)? Táto akcia je nezvratná a vymaže aj budúce priradenia!');">
                    <input type="hidden" name="action" value="reset_all_absences">
                    <button type="submit" class="danger-button">Zmazať Absencie (<?php echo htmlspecialchars($activeOddelenieNazov ?? 'N/A'); ?>)</button>
                </form>
            <?php endif; ?>
        </div>

        <div class="messages-section">
            <?php if (!empty($session_message)): ?> <div class="message success"><?php echo htmlspecialchars($session_message); ?></div> <?php endif; ?>
            <?php if (!empty($session_error)): ?> <div class="message error"><?php echo htmlspecialchars($session_error); ?></div> <?php endif; ?>
            <?php if (!empty($generation_message) && !str_contains($generation_message, 'Pre tento deň sa nepodarilo načítať priradenia')): ?> 
                <div class="message <?php echo (str_contains($generation_message, 'Chyba')) ? 'error' : 'info'; ?>"><?php echo htmlspecialchars($generation_message); ?></div> 
            <?php endif; ?>
        </div>

        <div class="page-footer">
            <div class="footer-title">Pracovné Priradenia</div>
            <div class="footer-controls">
                <form method="GET" action="dashboard.php" id="departmentSelectForm" style="display: inline-block;">
                    <input type="hidden" name="date" value="<?php echo htmlspecialchars($current_date_str); ?>">
                    <label for="department_select_dropdown" style="font-size: 0.9em; margin-right: 5px;">Zobraziť oddelenie:</label>
                    <select name="select_department_id" id="department_select_dropdown" onchange="this.form.submit();" class="button" style="padding: 5px 8px;">
                        <?php if (empty($allDepartmentsForDropdown)): ?>
                            <option value="">Žiadne oddelenia</option>
                        <?php else: ?>
                            <?php foreach ($allDepartmentsForDropdown as $dept): ?>
                                <option value="<?php echo $dept['oddelenie_id']; ?>" <?php if ($dept['oddelenie_id'] == $activeOddelenieId) echo 'selected'; ?>>
                                    <?php echo htmlspecialchars($dept['nazov']); ?>
                                </option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                </form>
            </div>
            <div class="footer-user-info">
                Prihlásený ako: <strong><?php echo htmlspecialchars($loggedUser['name'] ?? 'Používateľ'); ?></strong>
                (<?php echo htmlspecialchars($activeOddelenieNazov ?? $_SESSION['oddelenie_nazov'] ?? 'Bez oddelenia'); ?>) |
                <a href="logout.php">Odhlásiť sa</a>
            </div>
        </div>
    </div>

    <!-- Modálne okno pre vynútenie štartu -->
    <div id="forceStartModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Vynútiť štart prideľovania</h2>
            </div>
            <div class="modal-body">
                <p>Vyberte strojníka, od ktorého sa má začať prideľovanie strojov pre deň <strong><?php echo htmlspecialchars($display_date_str); ?></strong> a všetky nasledujúce dni.</p>
                <label for="machinist-select">Začať od strojníka:</label>
                <select id="machinist-select" name="machinist_order" class="button" style="width: 100%; padding: 8px;">
                    <option value="">--- Vyberte strojníka ---</option>
                    <?php
                        // Použijeme novú funkciu z functions.php
                        $allMachinists = getAllMachinists($pdo, $activeOddelenieId);
                        foreach ($allMachinists as $machinist) {
                            echo '<option value="' . $machinist['strojnik_order'] . '">' . htmlspecialchars($machinist['name']) . ' (Poradie ' . $machinist['strojnik_order'] . ')</option>';
                        }
                    ?>
                </select>
            </div>
            <div class="modal-footer">
                <button id="modal-cancel-btn" class="back-button">Zrušiť</button>
                <button id="modal-ok-btn" class="button">OK, Prepočítať</button>
            </div>
        </div>
    </div>
    <script src="calendar_swipe.js"></script>
    <script>
        let currentAbsenceDropdown = null;
        let isLoading = false;

        function showLoadingIndicator(message = 'Spracovávam...') {
            const existingIndicator = document.querySelector('.loading-indicator');
            if (existingIndicator) {
                existingIndicator.remove();
            }
            const indicator = document.createElement('div');
            indicator.className = 'loading-indicator';
            indicator.innerHTML = `<div class="spinner"></div><p>${message}</p>`;
            document.body.appendChild(indicator);
            return indicator;
        }

        function hideLoadingIndicator() {
            const indicator = document.querySelector('.loading-indicator');
            if (indicator) {
                indicator.remove();
            }
        }

        async function performAjaxRequest(url, options, loadingMessage) {
            if (isLoading) {
                console.warn('Another request is already in progress.');
                return Promise.reject(new Error('Request in progress'));
            }
            isLoading = true;
            const loadingIndicator = showLoadingIndicator(loadingMessage);
            try {
                const response = await fetch(url, options);
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`Server error: ${response.status} ${response.statusText}. ${errorText}`);
                }
                return response;
            } catch (error) {
                console.error('AJAX request failed:', error);
                alert(`Chyba: ${error.message}`);
                throw error; 
            } finally {
                hideLoadingIndicator();
                isLoading = false;
            }
        }

        function confirmDeleteVacation(date, userId) {
            if (confirm('Chcete odstrániť absenciu pre tento deň?')) { 
                performAjaxRequest('absences.php?action=delete', { method: 'POST', headers: { 'Content-Type': 'application/x-www-form-urlencoded' }, body: `date=${date}&uid=${userId}`}, 'Odstraňujem absenciu...')
                .then(() => window.location.reload())
                .catch(error => console.error('Failed to delete absence:', error));
            }
        }

        function addVacation(date, userId, userName, event) { 
            if (event) event.stopPropagation();
            closeAbsenceDropdown();
            const dropdown = document.createElement('div');
            dropdown.className = 'absence-dropdown';
            dropdown.id = 'absence-dropdown'; // ID môže zostať, ak sa naň neviaže špecifická logika starých typov
            const absenceTypes = ['Celý deň', 'Ráno', 'Poobede', 'Iné'];
            absenceTypes.forEach(type => {
                const option = document.createElement('div');
                option.className = 'absence-option';
                option.textContent = type;
                option.onclick = (e) => { e.stopPropagation(); submitAbsence(date, userId, type, userName); closeAbsenceDropdown(); };
                dropdown.appendChild(option);
            });
            const closeButton = document.createElement('div');
            closeButton.className = 'absence-close';
            closeButton.textContent = '×';
            closeButton.onclick = (e) => { e.stopPropagation(); closeAbsenceDropdown(); };
            dropdown.appendChild(closeButton);
            document.body.appendChild(dropdown);
            currentAbsenceDropdown = dropdown;
            const clickedElement = event.target.closest('td') || event.target;
            const rect = clickedElement.getBoundingClientRect();
            dropdown.style.position = 'absolute';
            dropdown.style.top = `${rect.bottom + window.scrollY}px`;
            dropdown.style.left = `${rect.left + window.scrollX}px`;
            setTimeout(() => { document.addEventListener('click', closeAbsenceDropdownOnClickOutside); }, 0);
        }

        function closeAbsenceDropdown() {
            if (currentAbsenceDropdown) {
                currentAbsenceDropdown.remove();
                currentAbsenceDropdown = null;
                document.removeEventListener('click', closeAbsenceDropdownOnClickOutside);
            }
        }

        function closeAbsenceDropdownOnClickOutside(event) {
            if (currentAbsenceDropdown && !currentAbsenceDropdown.contains(event.target)) {
                const clickedCalendarCell = event.target.closest('.calendar-table td.clickable-day');
                if (!clickedCalendarCell) {
                    closeAbsenceDropdown();
                }
            }
        }

        function submitAbsence(date, userId, absenceType, userName = '') {
             performAjaxRequest('absences.php', { method: 'POST', headers: { 'Content-Type': 'application/x-www-form-urlencoded' }, body: `action=add&user_id=${userId}&start_date=${date}&end_date=${date}&absence_type=${encodeURIComponent(absenceType)}&return_to=dashboard.php`}, `Pridávam absenciu (${absenceType}) pre ${userName || 'používateľa'}...`)
            .then(() => { window.location.reload(); })
            .catch(error => console.error('Failed to submit absence:', error));
        }

        function handleUserClick(userName, userId, isAbsent, event) {
            if (event) event.stopPropagation();
            const currentDateFromPHP = '<?php echo $current_date_str; ?>';
            const activeOddelenieIdFromPHP = <?php echo json_encode($activeOddelenieId); ?>; 
            if (isAbsent) {
                if (confirm(`Chcete zrušiť absenciu pre používateľa ${userName}?`)) {
                    removeAbsenceAndRecalculate(currentDateFromPHP, userId, activeOddelenieIdFromPHP);
                }
            } else {
                closeAbsenceDropdown();
                const dropdown = document.createElement('div');
                dropdown.className = 'absence-dropdown';
                dropdown.id = 'absence-dropdown'; // ID môže zostať
                const absenceTypes = ['Celý deň', 'Ráno', 'Poobede', 'Iné'];
                absenceTypes.forEach(type => {
                    const option = document.createElement('div');
                    option.className = 'absence-option';
                    option.textContent = type;
                    option.onclick = (e) => { e.stopPropagation(); addAbsenceAndRecalculate(currentDateFromPHP, userId, type, userName, activeOddelenieIdFromPHP); closeAbsenceDropdown(); };
                    dropdown.appendChild(option);
                });
                const closeButton = document.createElement('div');
                closeButton.className = 'absence-close';
                closeButton.textContent = '×';
                closeButton.onclick = (e) => { e.stopPropagation(); closeAbsenceDropdown(); };
                dropdown.appendChild(closeButton);
                document.body.appendChild(dropdown);
                currentAbsenceDropdown = dropdown;
                const rect = event.target.getBoundingClientRect();
                dropdown.style.position = 'absolute';
                dropdown.style.top = `${rect.bottom + window.scrollY}px`;
                dropdown.style.left = `${rect.left + window.scrollX}px`;
                setTimeout(() => { document.addEventListener('click', closeAbsenceDropdownOnClickOutside); }, 0);
            }
        }
        
        function addAbsenceAndRecalculate(date, userId, absenceType, userName, oddelenieId) {
            performAjaxRequest('absences.php', { method: 'POST', headers: { 'Content-Type': 'application/x-www-form-urlencoded' }, body: `action=add&user_id=${userId}&start_date=${date}&end_date=${date}&absence_type=${encodeURIComponent(absenceType)}`}, `Pridávam absenciu (${absenceType}) pre ${userName} a prepočítavam...`)
            .then(() => {
                const isRadis = <?php echo json_encode(isset($_SESSION["username"]) && $_SESSION["username"] === "radis"); ?>;
                if (!isRadis) {
                    return performAjaxRequest('recalculate_assignments.php', { method: 'POST', headers: { 'Content-Type': 'application/x-www-form-urlencoded' }, body: `date=${date}&oddelenie_id=${oddelenieId}`}, 'Prepočítavam priradenia...');
                }
                return Promise.resolve();
            })
            .then(() => window.location.reload())
            .catch(error => console.error('Failed to add absence and recalculate:', error));
        }

        function removeAbsenceAndRecalculate(date, userId, oddelenieId) {
            performAjaxRequest('absences.php?action=delete', { method: 'POST', headers: { 'Content-Type': 'application/x-www-form-urlencoded' }, body: `date=${date}&uid=${userId}`}, 'Odstraňujem absenciu a prepočítavam...')
            .then(() => {
                const isRadis = <?php echo json_encode(isset($_SESSION["username"]) && $_SESSION["username"] === "radis"); ?>;
                if (!isRadis) {
                    return performAjaxRequest('recalculate_assignments.php', { method: 'POST', headers: { 'Content-Type': 'application/x-www-form-urlencoded' }, body: `date=${date}&oddelenie_id=${oddelenieId}`}, 'Prepočítavam priradenia...');
                }
                return Promise.resolve();
            })
            .then(() => window.location.reload())
            .catch(error => console.error('Failed to remove absence and recalculate:', error));
        }

        function toggleMachineStatus(machineNumber, date, currentStatus) {
            const isRadis = <?php echo json_encode(isset($_SESSION["username"]) && $_SESSION["username"] === "radis"); ?>;
            if (!isRadis) {
                alert('Len používateľ radis môže meniť stav strojov.');
                return;
            }
            const action = currentStatus ? 'deactivate' : 'activate';
            const confirmMessage = currentStatus ? `Chcete odstaviť stroj ${machineNumber} pre deň ${date}?` : `Chcete aktivovať stroj ${machineNumber} pre deň ${date}?`;
            if (confirm(confirmMessage)) {
                const formData = new FormData();
                formData.append('machine', machineNumber);
                formData.append('date', date);
                formData.append('action', action);
                performAjaxRequest('toggle_machine.php', { method: 'POST', body: formData }, 'Spracovávam zmenu stavu stroja...')
                .then(response => response.json())
                .then(data => {
                    if (data.success) { window.location.reload(); } 
                    else { alert('Chyba: ' + data.message); }
                })
                .catch(error => console.error('Failed to toggle machine status:', error));
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            const machineCells = document.querySelectorAll('.machine-toggle-cell');
            machineCells.forEach(cell => {
                const icon = cell.querySelector('.machine-toggle-icon');
                if (icon) {
                    const isActive = icon.textContent.trim() === '✓';
                    cell.setAttribute('data-active', isActive ? '1' : '0');
                    cell.addEventListener('click', function(event) {
                        if (event.target.classList.contains('user-name')) return;
                        const machineNumber = this.getAttribute('data-machine');
                        const date = this.getAttribute('data-date');
                        const currentIsActive = this.getAttribute('data-active') === '1';
                        toggleMachineStatus(machineNumber, date, currentIsActive);
                    });
                }
            });

            const activeOddelenieId = <?php echo json_encode($activeOddelenieId); ?>;
            const loggedUserRole = <?php echo json_encode($loggedUser['role1'] ?? null); ?>;
            const isUserRadis = <?php echo json_encode(isset($_SESSION['username']) && $_SESSION['username'] === 'radis'); ?>;

            console.log('--- DEBUG START ---');
            console.log('PHP activeOddelenieId:', activeOddelenieId);
            console.log('PHP loggedUserRole:', loggedUserRole);
            console.log('PHP isUserRadis:', isUserRadis);

            const skladnikResetCell = document.getElementById('skladnik-reset-trigger');
            const isAuthorizedForSkladnikReset = (loggedUserRole === 'veduci' || loggedUserRole === 'schifflieder');
            console.log('Skladnik Reset Cell Element:', skladnikResetCell);
            console.log('Is Authorized for Skladnik Reset:', isAuthorizedForSkladnikReset);
            if (skladnikResetCell && isAuthorizedForSkladnikReset) {
                console.log('Attaching CLICK LISTENER to Skladnik cell.');
                skladnikResetCell.addEventListener('click', function() {
                    if (confirm('Naozaj chcete resetovať dvojtýždňový cyklus skladníkov od dnešného dňa?\n\nTáto akcia okamžite priradí druhého skladníka a nastaví dnešok ako nový začiatok cyklu.')) {
                        const formData = new FormData();
                        formData.append('oddelenie_id', activeOddelenieId);
                        performAjaxRequest('api_reset_skladnik_cycle.php', { method: 'POST', body: formData }, 'Resetujem cyklus skladníkov...')
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) { alert(data.message); window.location.reload(); } 
                            else { throw new Error(data.message || 'Neznáma chyba pri resetovaní cyklu skladníkov.'); }
                        })
                        .catch(error => {
                            console.error('Failed to reset storekeeper cycle:', error);
                            alert('Chyba pri resetovaní cyklu skladníkov: ' + error.message);
                        });
                    }
                });
            } else {
                console.log('NOT attaching click listener to Skladnik cell. Reason: Element found=', !!skladnikResetCell, 'Authorized=', isAuthorizedForSkladnikReset);
            }

            const schiffliederResetCell = document.getElementById('schifflieder-reset-trigger');
            const isAuthorizedForSchiffliederReset = (loggedUserRole === 'veduci' || isUserRadis); 
            console.log('Schifflieder Reset Cell Element:', schiffliederResetCell);
            console.log('Is Authorized for Schifflieder Reset:', isAuthorizedForSchiffliederReset);
            if (schiffliederResetCell && isAuthorizedForSchiffliederReset) {
                console.log('Attaching CLICK LISTENER to Schifflieder cell.');
                schiffliederResetCell.addEventListener('click', function() {
                    if (confirm('Naozaj chcete resetovať cyklus Schiffliederov od dnešného dňa?\n\nTáto akcia okamžite priradí druhého Schiffliedera a nastaví dnešok ako nový začiatok cyklu.')) {
                        performAjaxRequest('api_reset_schifflieder_cycle.php', { method: 'POST' }, 'Resetujem cyklus Schiffliederov...')
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) { alert(data.message); window.location.reload(); } 
                            else { throw new Error(data.message || 'Neznáma chyba pri resetovaní cyklu Schiffliederov.'); }
                        })
                        .catch(error => {
                            console.error('Failed to reset Schifflieder cycle:', error);
                            alert('Chyba pri resetovaní cyklu Schiffliederov: ' + error.message);
                        });
                    }
                });
            } else {
                console.log('NOT attaching click listener to Schifflieder cell. Reason: Element found=', !!schiffliederResetCell, 'Authorized=', isAuthorizedForSchiffliederReset);
            }
            console.log('--- DEBUG END ---');
        });

        // Logika pre modálne okno vynúteného štartu
        const modal = document.getElementById('forceStartModal');
        const triggerBtn = document.getElementById('force-start-trigger');
        const cancelBtn = document.getElementById('modal-cancel-btn');
        const okBtn = document.getElementById('modal-ok-btn');
        const machinistSelect = document.getElementById('machinist-select');

        if (triggerBtn) {
            triggerBtn.onclick = function() {
                if (modal) modal.style.display = "block";
            }
        }

        if (cancelBtn) {
            cancelBtn.onclick = function() {
                if (modal) modal.style.display = "none";
            }
        }

        window.onclick = function(event) {
            if (event.target == modal) {
                modal.style.display = "none";
            }
        }

        if (okBtn) {
            okBtn.onclick = function() {
                const selectedOrder = machinistSelect.value;
                const selectedName = machinistSelect.options[machinistSelect.selectedIndex].text;
                const date = '<?php echo $current_date_str; ?>';
                const oddelenieId = <?php echo json_encode($activeOddelenieId); ?>;

                if (!selectedOrder) {
                    alert('Prosím, vyberte strojníka.');
                    return;
                }

                if (confirm(`Naozaj chcete prepočítať všetky priradenia od dňa <?php echo $display_date_str; ?>?\n\nNový cyklus začne od pracovníka:\n${selectedName}\n\nTáto akcia je nezvratná a zmení celý budúci rozpis.`)) {
                    const formData = new FormData();
                    formData.append('date', date);
                    formData.append('strojnik_order', selectedOrder);
                    formData.append('oddelenie_id', oddelenieId);

                    performAjaxRequest('api_force_machinist_start.php', { method: 'POST', body: formData }, 'Prepočítavam priradenia...')
                        .then(() => window.location.reload());
                }
            }
        }

        // --- NOVÁ NAVIGÁCIA ---

        // 1. Ovládanie klávesnicou
        document.addEventListener('keydown', function(event) {
            // Ignorujeme, ak používateľ píše do nejakého poľa
            const activeElement = document.activeElement;
            if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'SELECT' || activeElement.tagName === 'TEXTAREA')) {
                return;
            }

            const prevLink = document.getElementById('prev-day-link');
            const nextLink = document.getElementById('next-day-link');

            if (event.key === 'ArrowLeft' && prevLink) {
                window.location.href = prevLink.href;
            } else if (event.key === 'ArrowRight' && nextLink) {
                window.location.href = nextLink.href;
            }
        });

        // 2. Ovládanie potiahnutím (swipe)
        const swipeArea = document.getElementById('main-content-swipe-area');
        if (swipeArea) {
            let startX, endX;

            swipeArea.addEventListener('touchstart', function(e) {
                startX = e.touches[0].clientX;
            }, { passive: true });

            swipeArea.addEventListener('touchend', function(e) {
                endX = e.changedTouches[0].clientX;
                handleMainSwipe();
            }, { passive: true });

            function handleMainSwipe() {
                const threshold = 75; // Minimálna vzdialenosť pre swipe
                const prevLink = document.getElementById('prev-day-link');
                const nextLink = document.getElementById('next-day-link');

                if (startX - endX > threshold && nextLink) { window.location.href = nextLink.href; }
                if (endX - startX > threshold && prevLink) { window.location.href = prevLink.href; }
            }
        }
    </script>
</body>
</html>