<?php
// Potlačenie varovaní o zastaraných funkciách
error_reporting(E_ALL & ~E_DEPRECATED & ~E_NOTICE);
// Základné nastavenia a funkcie - uistite sa, že tieto súbory existujú a sú správne
require_once 'config.php'; // V<PERSON><PERSON> konfigura<PERSON> (napr. pre DB)
require_once 'functions.php'; // Súbor s vašimi pomocnými funkciami (napr. getUserById, requireLogin, getDbConnection)

// Overenie prihlásenia používateľa
requireLogin();

// Získanie PDO spojenia (ak je potrebné pre getUserById)
$pdo = getDbConnection(); // Predpokladáme, že táto funkcia existuje vo functions.php

// Načítanie údajov prihláseného používateľa
$loggedInUserId = $_SESSION['user_id'] ?? 0;
$loggedUser = null;
if (function_exists('getUserById')) { // Kontrola existencie funkcie
    $loggedUser = getUserById($loggedInUserId, $pdo);
}

// Inicializácia premenných
$externalStatusResult = null; // Výsledok z minv.sk
$errorMessage = null;         // Chybové hlášky pre používateľa
$successMessage = null;       // Úspešné hlášky pre používateľa (napr. o nájdení variácie)
$uploadedImagePath = null;    // Cesta k nahranému obrázku (ak bol nahraný cez PHP)
$originalRequestNumberForDisplay = ''; // Na uchovanie pôvodne zadaného/OCR čísla pre zobrazenie vo formulári

// Spracovanie formulára po odoslaní (POST metóda)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {

    // Uchováme číslo zadané vo formulári pre prípadné opätovné zobrazenie
    $originalRequestNumberForDisplay = isset($_POST['request_number']) ? htmlspecialchars(trim($_POST['request_number']), ENT_QUOTES, 'UTF-8') : '';

    // 1. Spracovanie nahrávania obrázku (ak bol súbor vybraný)
    if (isset($_FILES['request_image']) && $_FILES['request_image']['error'] === UPLOAD_ERR_OK) {
        $uploadDir = 'uploads/'; // Adresár pre nahrávané súbory
        if (!file_exists($uploadDir)) {
            if (!mkdir($uploadDir, 0777, true) && !is_dir($uploadDir)) {
                 $errorMessage = "Chyba: Nepodarilo sa vytvoriť adresár pre nahrávanie súborov: {$uploadDir}";
            }
        }
        if (is_writable($uploadDir)) { // Pokračujeme len ak sa dá zapisovať
            $fileName = basename($_FILES['request_image']['name']); // Bezpečnostné očistenie mena súboru
            $fileType = $_FILES['request_image']['type'];
            $fileTmpName = $_FILES['request_image']['tmp_name'];
            $fileSize = $_FILES['request_image']['size'];
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];

            if (!in_array($fileType, $allowedTypes)) {
                $errorMessage = "Neplatný formát súboru. Povolené formáty sú: JPG, PNG, GIF.";
            } elseif ($fileSize > 5 * 1024 * 1024) { // 5MB limit
                $errorMessage = "Súbor je príliš veľký. Maximálna veľkosť je 5MB.";
            } else {
                // Vytvorenie unikátneho mena súboru
                $newFileName = uniqid('img_', true) . '.' . pathinfo($fileName, PATHINFO_EXTENSION);
                $destination = $uploadDir . $newFileName;
                if (move_uploaded_file($fileTmpName, $destination)) {
                    $uploadedImagePath = $destination; // Cesta k úspešne nahranému súboru
                } else {
                    $errorMessage = "Nastala chyba pri presúvaní nahraného súboru.";
                }
            }
        } else {
             $errorMessage = "Chyba: Do adresára '{$uploadDir}' sa nedá zapisovať. Skontrolujte oprávnenia.";
        }
    } elseif (isset($_FILES['request_image']) && $_FILES['request_image']['error'] !== UPLOAD_ERR_NO_FILE) {
        // Ak nastala iná chyba pri nahrávaní ako "žiadny súbor"
        $errorMessage = "Nastala chyba pri nahrávaní obrázka (kód: {$_FILES['request_image']['error']}).";
    }

    // 2. Spracovanie stlačenia tlačidla "Zisti stav žiadosti"
    if (isset($_POST['check_status'])) {
        $requestNumberToCheck = $originalRequestNumberForDisplay; // Použijeme číslo z formulára

        // Validácia vstupného čísla
        if (empty($requestNumberToCheck)) {
            $errorMessage = "Prosím, zadajte alebo nechajte rozpoznať číslo žiadosti.";
        } else {
            // a) Prvý pokus o získanie stavu s pôvodným číslom
            try {
                $externalStatusResult = getExternalRequestStatus($requestNumberToCheck);

                // b) Logika pre automatickú korekciu 2/Z v posledných 3 znakoch
                // Aktivuje sa len ak prvý pokus vráti "nebola nájdená" a číslo má očakávanú dĺžku
                $expectedLength = 19; // Príklad dĺžky formátu XX-XXXXX-NNNNNN-ZZZ, upravte podľa potreby!
                if (
                    isset($externalStatusResult['status']) &&
                    mb_stripos($externalStatusResult['status'], 'nebola nájdená') !== false &&
                    strlen($requestNumberToCheck) === $expectedLength
                ) {
                    $errorMessage = null; // Vymažeme pôvodnú chybovú hlášku o nenájdení
                    $baseNumber = substr($requestNumberToCheck, 0, -3);
                    $originalSuffix = substr($requestNumberToCheck, -3);
                    $variations = generateSuffixVariations($originalSuffix);

                    $foundWorkingVariation = false;
                    $checkedCount = 0; // Počítadlo pokusov

                    foreach ($variations as $variation) {
                        if ($variation === $originalSuffix) continue; // Pôvodné sme už skúsili

                        $checkedCount++;
                        if ($checkedCount >= 8) break; // Max 2^3 = 8 kombinácií, dáme malú rezervu

                        $newRequestNumber = $baseNumber . $variation;
                        usleep(250000); // Pauza 250ms

                        try {
                            $tempResult = getExternalRequestStatus($newRequestNumber);
                            if (isset($tempResult['status']) && mb_stripos($tempResult['status'], 'nebola nájdená') === false) {
                                $externalStatusResult = $tempResult; // Našli sme funkčnú variáciu!
                                $successMessage = "Stav bol nájdený pre upravené číslo: " . htmlspecialchars($newRequestNumber);
                                $originalRequestNumberForDisplay = $newRequestNumber; // Aktualizujeme aj číslo pre zobrazenie vo formulári
                                $foundWorkingVariation = true;
                                break; // Končíme hľadanie variácií
                            }
                        } catch (Exception $e) {
                            error_log("Chyba pri skúšaní variácie '$newRequestNumber': " . $e->getMessage());
                            continue; // Ignorujeme chybu pri variácii a pokračujeme
                        }
                    }

                    if (!$foundWorkingVariation && empty($errorMessage)) {
                        // Ak sa nenašla funkčná variácia, zobrazíme pôvodný výsledok alebo novú hlášku
                        $errorMessage = "Žiadosť nebola nájdená ani po skontrolovaní 2/Z variácií.";
                        // Ponecháme pôvodný $externalStatusResult['status'] = "Žiadosť ... nebola nájdená."
                    }
                }

            } catch (Exception $e) {
                // Chyba pri prvotnom volaní getExternalRequestStatus
                $errorMessage = "Nastala chyba pri zisťovaní stavu žiadosti na minv.sk: " . $e->getMessage();
            }
        }
    }
}

// --- Definície PHP funkcií ---

/**
 * Získa stav žiadosti z externého systému minv.sk
 * @param string $requestNumber Číslo žiadosti
 * @return array Asociatívne pole s kľúčmi 'status' a voliteľne 'details'
 * @throws Exception Ak nastane chyba komunikácie alebo server vráti chybu
 */
function getExternalRequestStatus($requestNumber) {
    $url = 'https://www.minv.sk/?zistenie-stavu-spracovania-ziadosti';
    $ch = curl_init();
    // Nastavenia cURL
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, "cislo=" . urlencode($requestNumber)); // Názov poľa je "cislo"
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // Sledovať presmerovania
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'); // Rozumný User Agent
    curl_setopt($ch, CURLOPT_TIMEOUT, 20); // Zvýšený timeout na 20 sekúnd
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true); // Overenie SSL certifikátu (malo by byť zapnuté)
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);

    $response = curl_exec($ch);
    $curlErrorNum = curl_errno($ch);
    $curlErrorMsg = curl_error($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    // Kontrola chýb cURL
    if ($curlErrorNum) {
        throw new Exception("Chyba pri komunikácii so serverom minv.sk (cURL Error {$curlErrorNum}): " . $curlErrorMsg);
    }
    // Kontrola HTTP kódu
    if ($httpCode !== 200) {
         throw new Exception("Server minv.sk vrátil neočakávaný stavový kód: {$httpCode}");
    }
    // Kontrola prázdnej odpovede
    if (empty($response)) {
         throw new Exception("Server minv.sk vrátil prázdnu odpoveď.");
    }

    // Extrakcia výsledku z HTML odpovede
    $result = ['status' => null, 'details' => null];
    $statusFound = false;

    // Hľadanie stavu v <h4>
    if (preg_match('/<h4[^>]*>(.*?)<\/h4>/is', $response, $matches)) {
        $result['status'] = trim(strip_tags($matches[1]));
        $statusFound = true;
    }

    // Ak sa stav nenašiel v <h4>, skúsime detekovať známe chybové hlášky
    if (!$statusFound) {
         if (mb_stripos($response, 'nebola nájdená') !== false || mb_stripos($response, 'nie je v systéme evidovaná') !== false) {
              $result['status'] = "Žiadosť s daným číslom nebola nájdená.";
         } elseif (mb_stripos($response, 'nesprávny formát') !== false) {
              $result['status'] = "Zadané číslo žiadosti má nesprávny formát.";
         } else {
            // Ak nenájdeme ani známu chybu, je to neznáma odpoveď
            $result['status'] = "Nepodarilo sa zistiť stav žiadosti (neznáma odpoveď zo servera).";
            // Môžete zalogovať časť odpovede pre budúcu analýzu
            // error_log("MINV Response (Unknown): " . substr(strip_tags($response), 0, 500));
         }
    }

    // Hľadanie detailov v <div class="text"> (ak existujú)
    if (preg_match('/<div class="text"[^>]*>(.*?)<\/div>/is', $response, $matches_details)) {
        // Odstránime nadbytočné HTML a biele znaky
        $details = trim(strip_tags($matches_details[1], '<br><p>')); // Ponecháme základné formátovanie
        $details = preg_replace('/\s+/', ' ', $details); // Nahradíme viacnásobné medzery jednou
        $result['details'] = $details;
    }

    return $result;
}

/**
 * Generuje všetky možné variácie posledných 3 znakov zámenou '2' za 'Z' a 'Z' za '2'.
 * @param string $suffix Pôvodné posledné 3 znaky.
 * @return array Pole unikátnych variácií (vrátane pôvodnej).
 */
function generateSuffixVariations($suffix) {
    if (strlen($suffix) !== 3) return [$suffix];
    $variations = [];
    $queue = [$suffix];
    $processed = [$suffix => true];
    while (!empty($queue)) {
        $current = array_shift($queue);
        $variations[] = $current;
        for ($i = 0; $i < 3; $i++) {
            $char = $current[$i];
            $swappedChar = null;
            if ($char === '2') $swappedChar = 'Z';
            elseif ($char === 'Z') $swappedChar = '2';
            if ($swappedChar !== null) {
                $nextVariation = substr_replace($current, $swappedChar, $i, 1);
                if (!isset($processed[$nextVariation])) {
                    $queue[] = $nextVariation;
                    $processed[$nextVariation] = true;
                }
            }
        }
    }
    return array_unique($variations);
}

// Určenie návratovej URL
$returnUrl = $_GET['return'] ?? 'dashboard.php';
if (!preg_match('/^(dashboard\.php|monthly_overview\.php|absences\.php|weekly_absences\.php)/', $returnUrl) && !filter_var($returnUrl, FILTER_VALIDATE_URL)) {
    $returnUrl = 'dashboard.php'; // Bezpečnostná predvolená hodnota
}

?>
<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Zistenie stavu žiadosti</title>
    <link rel="stylesheet" href="style.css"> <script src="https://cdn.jsdelivr.net/npm/tesseract.js@4/dist/tesseract.min.js"></script>
    <style>
        /* Vložené základné CSS štýly - môžete ich presunúť do style.css */
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; background-color: #1e1e1e; color: #f0f0f0; margin: 0; padding: 20px; line-height: 1.6; }
        .container { max-width: 600px; margin: 0 auto; }
        .header { padding: 15px; background-color: #333; color: #f0f0f0; text-align: center; margin-bottom: 20px; border-radius: 8px; font-size: 1.2em;}
        .request-form { max-width: 500px; margin: 0 auto; padding: 20px; background-color: #2a2a2a; border-radius: 8px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3); margin-bottom: 20px; }
        .external-status-result { max-width: 500px; margin: 20px auto; padding: 0 10px; } /* Zmenšený padding pre mobil */
        .error-message { color: #ff6b6b; background-color: rgba(255, 107, 107, 0.1); border: 1px solid rgba(255, 107, 107, 0.3); padding: 10px 15px; border-radius: 4px; margin-bottom: 15px; text-align: center; }
        .success-message { color: #4CAF50; background-color: rgba(76, 175, 80, 0.1); border: 1px solid rgba(76, 175, 80, 0.3); padding: 10px 15px; border-radius: 4px; margin-bottom: 15px; text-align: center; }
        .status-success-display { color: #66bb6a; background-color: rgba(102, 187, 106, 0.15); border: 1px solid rgba(102, 187, 106, 0.4); padding: 15px 20px; border-radius: 8px; margin-bottom: 20px; text-align: center; font-size: 1.2em; font-weight: bold; }
        .status-other-display { color: #f0f0f0; background-color: #3a3a3a; border: 1px solid #555; padding: 15px 20px; border-radius: 8px; margin-bottom: 20px; text-align: center; font-size: 1.1em; }
        .status-details { background-color: #2a2a2a; padding: 15px; border-radius: 8px; margin-top: 15px; border: 1px solid #444; }
        .status-details strong { color: #8ab4f8; display: block; margin-bottom: 8px; font-size: 1em; }
        .status-details p { color: #e0e0e0; margin: 0; line-height: 1.6; font-size: 0.95em; }
        .buttons { display: flex; flex-wrap: wrap; justify-content: space-between; margin-top: 20px; gap: 10px;} /* Gap pre medzery */
        .back-button, .check-button { flex-grow: 1; text-align: center; background-color: #555; color: #fff; border: none; padding: 12px 15px; border-radius: 4px; cursor: pointer; transition: background-color 0.3s; text-decoration: none; display: inline-block; font-size: 1em; }
        .check-button { background-color: #3a6ea5; }
        .back-button:hover { background-color: #666; }
        .check-button:hover { background-color: #4a8ec5; }
        .file-upload { margin-bottom: 15px; }
        .file-upload label { display: block; margin-bottom: 5px; color: #f0f0f0; }
        .file-upload-wrapper { position: relative; margin-bottom: 10px; }
        .file-upload input[type="file"] { width: 100%; padding: 10px; background-color: #333; border: 1px dashed #555; border-radius: 4px; color: #e0e0e0; cursor: pointer; box-sizing: border-box; }
        .file-info { color: #bbb; font-size: 0.9em; margin-top: 5px; }
        .image-preview { margin-top: 15px; text-align: center; }
        .image-preview img { max-width: 100%; max-height: 200px; border: 1px solid #444; border-radius: 4px; background-color: #333; display: block; margin: 5px auto 0;} /* Centrovanie obrázku */
        .image-preview p { margin-bottom: 5px; color: #f0f0f0; }
        .loading-indicator { display: none; text-align: center; margin-top: 10px; color: #8ab4f8; }
        .loading-spinner { display: inline-block; width: 20px; height: 20px; border: 3px solid rgba(138, 180, 248, 0.3); border-radius: 50%; border-top-color: #8ab4f8; animation: spin 1s ease-in-out infinite; margin-right: 10px; vertical-align: middle; }
        @keyframes spin { to { transform: rotate(360deg); } }
        .ocr-debug { margin-top: 15px; padding: 10px; background-color: #333; border: 1px solid #555; border-radius: 4px; color: #f0f0f0; font-family: monospace; white-space: pre-wrap; word-break: break-word; max-height: 150px; /* Zmenšená výška */ overflow-y: auto; font-size: 0.85em; }
        .ocr-debug h4 { margin-top: 0; margin-bottom: 5px; color: #8ab4f8; border-bottom: 1px solid #444; padding-bottom: 5px; font-size: 1em; }
        .ocr-error { color: #ff6b6b; }
        .ocr-matches { color: #66bb6a; }
        .ocr-matches span { display: block; margin-bottom: 3px; } /* Každá zhoda na nový riadok */
        .ocr-matches span:hover { background-color: #444; }
        label { color: #f0f0f0; margin-bottom: 5px; display: block; }
        input[type="text"] { width: 100%; padding: 10px; margin-bottom: 15px; background-color: #333; border: 1px solid #555; border-radius: 4px; color: #f0f0f0; box-sizing: border-box; font-size: 1em;}
        .preprocessing-controls { border: 1px solid #444; padding: 15px; margin-top: 20px; border-radius: 8px; background-color: #303030; }
        .control-group { margin-bottom: 15px; padding-bottom: 15px; border-bottom: 1px solid #444; }
        .control-group:last-child { margin-bottom: 0; padding-bottom: 0; border-bottom: none; }
        .control-header { display: flex; align-items: center; margin-bottom: 10px; }
        .control-header label { margin-bottom: 0; margin-left: 10px; cursor: pointer; flex-grow: 1; } /* Label zaberie zvyšný priestor */
        .control-header input[type="checkbox"] { width: 18px; height: 18px; cursor: pointer; flex-shrink: 0; } /* Checkbox sa nezmenšuje */
        .slider-container { padding-left: 28px; } /* Odsadenie slidera kvôli checkboxu */
        .slider-container label { font-size: 0.9em; color: #bbb; margin-bottom: 3px; }
        .slider-container input[type="range"] { width: 100%; cursor: pointer; margin-top: 5px; height: 5px;} /* Zvýraznenie slidera */
        .slider-container span { font-weight: bold; color: #8ab4f8; margin-left: 5px;}
        .disabled-slider { opacity: 0.5; cursor: not-allowed; } /* Štýl pre vypnutý slider */
    </style>
</head>
<body>
    <div class="container">
        <div class="header">Zistenie stavu žiadosti</div>

         <?php if ($externalStatusResult): ?>
            <div class="external-status-result">
                 <?php
                 // Zobrazíme najprv úspešnú hlášku, ak existuje (napr. o nájdení variácie)
                 if ($successMessage): ?>
                     <div class="success-message"><?= htmlspecialchars($successMessage) ?></div>
                 <?php endif;

                 $statusText = $externalStatusResult['status'] ?? 'Neznámy stav';
                 // Podmienka pre zelený štýl: ak obsahuje 'prijatá' ALEBO ak máme successMessage (našla sa variácia) A zároveň status už NIE JE "nebola nájdená"
                 $isSuccess = (mb_stripos($statusText, 'prijatá') !== false) || ($successMessage && mb_stripos($statusText, 'nebola nájdená') === false);
                 $statusClass = $isSuccess ? 'status-success-display' : 'status-other-display';
                 ?>
                 <div class="<?= $statusClass ?>">
                      <?= htmlspecialchars($statusText) ?>
                 </div>

                 <?php if (!empty($externalStatusResult['details'])): ?>
                     <div class="status-details">
                         <strong>Detaily:</strong>
                         <p><?= nl2br(htmlspecialchars($externalStatusResult['details'])) ?></p>
                     </div>
                 <?php endif; ?>
             </div>
         <?php endif; ?>

        <?php if ($errorMessage && !$successMessage): ?>
            <div class="error-message"><?= htmlspecialchars($errorMessage) ?></div>
        <?php endif; ?>


        <div class="request-form">
             <form method="post" action="<?= htmlspecialchars($_SERVER['PHP_SELF']) ?><?= !empty($returnUrl) ? '?return=' . urlencode($returnUrl) : '' ?>" enctype="multipart/form-data">
                <label for="request_number">Číslo žiadosti:</label>
                <input type="text" id="request_number" name="request_number" required
                       value="<?= htmlspecialchars($originalRequestNumberForDisplay) ?>" placeholder="Napr. DS-JPPO1-123456-ABC">

                <div class="file-upload">
                    <label for="request_image">Nahrať obrázok žiadosti (voliteľné):</label>
                    <div class="file-upload-wrapper">
                        <input type="file" id="request_image" name="request_image" accept="image/jpeg,image/png,image/gif"> </div>
                    <div class="file-info">Povolené: JPG, PNG, GIF. Max: 5MB</div>

                    <div class="preprocessing-controls">
                        <div class="control-group">
                            <div class="control-header">
                                <input type="checkbox" id="enableThreshold" name="enableThreshold" checked>
                                <label for="enableThreshold">Thresholding (Binarizácia)</label>
                            </div>
                            <div class="slider-container" id="thresholdSliderContainer">
                                <label for="thresholdSlider">Prahová hodnota: <span id="thresholdValueDisplay">128</span></label>
                                <input type="range" id="thresholdSlider" name="thresholdSlider" min="0" max="255" value="128">
                            </div>
                        </div>
                        <div class="control-group">
                            <div class="control-header">
                                <input type="checkbox" id="enableGradient" name="enableGradient">
                                <label for="enableGradient">Gradient Map (Sobel - Experimentálne)</label>
                            </div>
                            <div class="slider-container" id="gradientSliderContainer">
                                <label for="gradientThresholdSlider">Prah gradientu: <span id="gradientThresholdValueDisplay">50</span></label>
                                <input type="range" id="gradientThresholdSlider" name="gradientThresholdSlider" min="0" max="255" value="50">
                            </div>
                        </div>
                    </div>

                    <div class="loading-indicator" id="ocr-loading">
                        <div class="loading-spinner"></div>
                        <span>Spracúvam obrázok...</span>
                    </div>

                    <div class="image-preview" id="js-image-preview" style="display: none;"><p>Náhľad:</p><img id="js-preview-img" src="#" alt="Náhľad obrázka"></div>
                    <?php if ($uploadedImagePath): /* Zobrazenie obrázka nahraného cez PHP, ak JS náhľad nie je aktívny */ ?>
                    <div class="image-preview" id="uploaded-image-preview" style="<?= (isset($_FILES['request_image']) && $_FILES['request_image']['error'] === UPLOAD_ERR_OK) ? 'display:none;' : '' ?>">
                        <p>Naposledy nahraný obrázok:</p>
                        <img src="<?= htmlspecialchars($uploadedImagePath) ?>" alt="Nahraný obrázok">
                    </div>
                    <?php endif; ?>


                    <div id="ocr-debug-container" style="display: none;">
                        <div id="ocr-recognized-text" class="ocr-debug"><h4>Rozpoznaný text:</h4><div id="ocr-text-content"></div></div>
                        <div id="ocr-matches-container" class="ocr-debug"><h4>Nájdené čísla (kliknite pre vloženie):</h4><div id="ocr-matches-content"></div></div>
                        <div id="ocr-error-container" class="ocr-debug" style="display: none;"><h4>Chyba OCR:</h4><div id="ocr-error-content" class="ocr-error"></div></div>
                    </div>
                </div>

                <div class="buttons">
                    <a href="<?= htmlspecialchars($returnUrl) ?>" class="back-button">Späť</a>
                    <button type="submit" name="check_status" class="check-button">Zistiť Stav</button>
                </div>
            </form>
        </div>

        <div style="text-align: center; margin-top: 20px; font-size: 0.9em; color: #777;">
            Prihlásený ako: <?php echo htmlspecialchars($loggedUser['name'] ?? $_SESSION['user_name'] ?? 'Neznámy'); ?>
        </div>
    </div>

    <script>
        // --- Plný JavaScript Kód ---
        (function() { // Uzavretie do anonymnej funkcie na ochranu scope
            // Získanie referencií na DOM elementy
            const requestImageInput = document.getElementById('request_image');
            const jsPreviewContainer = document.getElementById('js-image-preview');
            const jsPreviewImage = document.getElementById('js-preview-img');
            const uploadedPreviewContainer = document.getElementById('uploaded-image-preview');
            const enableThresholdCheckbox = document.getElementById('enableThreshold');
            const thresholdSlider = document.getElementById('thresholdSlider');
            const thresholdValueDisplay = document.getElementById('thresholdValueDisplay');
            const thresholdSliderContainer = document.getElementById('thresholdSliderContainer'); // Pre disabled štýl
            const enableGradientCheckbox = document.getElementById('enableGradient');
            const gradientThresholdSlider = document.getElementById('gradientThresholdSlider');
            const gradientThresholdValueDisplay = document.getElementById('gradientThresholdValueDisplay');
            const gradientSliderContainer = document.getElementById('gradientSliderContainer'); // Pre disabled štýl
            const loadingIndicator = document.getElementById('ocr-loading');
            const debugContainer = document.getElementById('ocr-debug-container');
            const textContent = document.getElementById('ocr-text-content');
            const matchesContent = document.getElementById('ocr-matches-content');
            const errorContainer = document.getElementById('ocr-error-container');
            const errorContent = document.getElementById('ocr-error-content');
            const requestNumberInput = document.getElementById('request_number');

            // --- Event Listeners ---

            // Listener pre výber súboru
            requestImageInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    if (!validateFile(file)) {
                        this.value = ''; // Reset inputu ak je neplatný
                        jsPreviewContainer.style.display = 'none';
                        return;
                    }
                    displayAndProcessImage(file);
                } else {
                    // Ak používateľ zruší výber súboru
                    jsPreviewContainer.style.display = 'none';
                    if (uploadedPreviewContainer) {
                         uploadedPreviewContainer.style.display = 'block'; // Zobrazíme späť PHP náhľad
                    }
                }
            });

            // Listener pre Threshold slider a checkbox
            if (thresholdSlider && thresholdValueDisplay && enableThresholdCheckbox && thresholdSliderContainer) {
                thresholdValueDisplay.textContent = thresholdSlider.value; // Init value
                thresholdSlider.addEventListener('input', () => {
                    thresholdValueDisplay.textContent = thresholdSlider.value;
                });
                enableThresholdCheckbox.addEventListener('change', () => {
                    thresholdSlider.disabled = !enableThresholdCheckbox.checked;
                    thresholdSliderContainer.classList.toggle('disabled-slider', !enableThresholdCheckbox.checked);
                });
                // Počiatočné nastavenie disabled stavu
                thresholdSlider.disabled = !enableThresholdCheckbox.checked;
                thresholdSliderContainer.classList.toggle('disabled-slider', !enableThresholdCheckbox.checked);

            }

            // Listener pre Gradient Threshold slider a checkbox
            if (gradientThresholdSlider && gradientThresholdValueDisplay && enableGradientCheckbox && gradientSliderContainer) {
                 gradientThresholdValueDisplay.textContent = gradientThresholdSlider.value; // Init value
                 gradientThresholdSlider.addEventListener('input', () => {
                     gradientThresholdValueDisplay.textContent = gradientThresholdSlider.value;
                 });
                 enableGradientCheckbox.addEventListener('change', () => {
                     gradientThresholdSlider.disabled = !enableGradientCheckbox.checked;
                     gradientSliderContainer.classList.toggle('disabled-slider', !enableGradientCheckbox.checked);
                 });
                 // Počiatočné nastavenie disabled stavu
                 gradientThresholdSlider.disabled = !enableGradientCheckbox.checked;
                 gradientSliderContainer.classList.toggle('disabled-slider', !enableGradientCheckbox.checked);
            }

             // --- Pomocné Funkcie ---

            function validateFile(file) {
                const fileType = file.type;
                const validImageTypes = ['image/jpeg', 'image/png', 'image/gif'];
                if (!validImageTypes.includes(fileType)) {
                    alert('Neplatný formát súboru. Povolené formáty sú: JPG, PNG, GIF.');
                    return false;
                }
                if (file.size > 5 * 1024 * 1024) { // 5MB limit
                    alert('Súbor je príliš veľký. Maximálna veľkosť je 5MB.');
                    return false;
                }
                return true;
            }

            function displayAndProcessImage(file) {
                 const reader = new FileReader();
                 reader.onload = function(event) {
                      if (uploadedPreviewContainer) uploadedPreviewContainer.style.display = 'none'; // Skryť PHP náhľad
                      jsPreviewImage.src = event.target.result;
                      jsPreviewContainer.style.display = 'block'; // Zobraziť JS náhľad
                      resetOcrDebug();
                      recognizeTextFromImage(event.target.result); // Spustiť OCR
                 };
                 reader.readAsDataURL(file);
            }

            function resetOcrDebug() {
                debugContainer.style.display = 'none';
                textContent.textContent = '';
                matchesContent.innerHTML = ''; // Používame innerHTML
                errorContainer.style.display = 'none';
                errorContent.textContent = '';
            }

            // --- Funkcie pre predspracovanie obrazu ---

            function preprocessImage(imageDataUrl, enableThreshold, thresholdValue, enableGradient, gradientThresholdValue) {
                return new Promise((resolve, reject) => {
                    const img = new Image();
                    img.onload = () => {
                        const canvas = document.createElement('canvas');
                        const width = img.width;
                        const height = img.height;
                        canvas.width = width;
                        canvas.height = height;
                        // Pridanie { willReadFrequently: true } môže (ale nemusí) zlepšiť výkon getImageData/putImageData
                        const ctx = canvas.getContext('2d', { willReadFrequently: true });
                        if (!ctx) return reject(new Error('Canvas 2D context is not supported.'));

                        ctx.drawImage(img, 0, 0);
                        let imageDataObj;
                        try {
                           imageDataObj = ctx.getImageData(0, 0, width, height);
                        } catch (e) {
                           // Chyba môže nastať kvôli tainted canvas (CORS), ak obrázok nie je z rovnakej domény
                           console.error("Error getting ImageData:", e);
                           return reject(new Error('Could not get image data, possibly due to CORS policy.'));
                        }
                        let data = imageDataObj.data;

                        // 1. Konverzia na stupne šedi
                        let grayData = new Uint8ClampedArray(width * height);
                        for (let i = 0, j = 0; i < data.length; i += 4, j++) {
                            const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
                            grayData[j] = gray;
                        }

                        // 2. Aplikácia efektov
                        let finalPixelDataUsed = false; // Flag či sme menili 'data'
                        if (enableGradient) {
                            console.log(`Applying Gradient Map with threshold: ${gradientThresholdValue}`);
                            const sobelData = applySobelFilter(grayData, width, height);
                            for (let i = 0, j = 0; i < data.length; i += 4, j++) {
                                const newValue = sobelData[j] > gradientThresholdValue ? 255 : 0;
                                data[i] = data[i + 1] = data[i + 2] = newValue;
                                data[i + 3] = 255;
                            }
                             finalPixelDataUsed = true;
                        } else if (enableThreshold) {
                            console.log(`Applying standard Thresholding with value: ${thresholdValue}`);
                            for (let i = 0, j = 0; i < data.length; i += 4, j++) {
                                const newValue = grayData[j] > thresholdValue ? 255 : 0;
                                data[i] = data[i + 1] = data[i + 2] = newValue;
                                data[i + 3] = 255;
                            }
                             finalPixelDataUsed = true;
                        }

                        // Ak nebol aplikovaný žiadny efekt, vrátime aspoň grayscale verziu
                        if (!finalPixelDataUsed) {
                             console.log("Applying Grayscale only (no effects enabled)");
                             for (let i = 0, j = 0; i < data.length; i += 4, j++) {
                                  data[i] = data[i + 1] = data[i + 2] = grayData[j];
                                  data[i + 3] = 255;
                             }
                        }

                        // 3. Vloženie upravených dát späť
                        ctx.putImageData(imageDataObj, 0, 0);
                        resolve(canvas.toDataURL('image/png')); // Export ako PNG pre lepšiu kvalitu binárneho obrazu
                    };
                    img.onerror = (err) => reject(new Error('Image could not be loaded for preprocessing.'));
                    img.src = imageDataUrl;
                });
            }

            // Pomocná funkcia pre Sobel Filter
            function applySobelFilter(grayData, width, height) {
                const kernelX = [[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]];
                const kernelY = [[-1, -2, -1], [0, 0, 0], [1, 2, 1]];
                // Použijeme Float32Array pre medzivýpočty, aby sme predišli orezaniu hodnôt
                const magnitudeData = new Float32Array(width * height);
                let maxMagnitude = 0;

                for (let y = 1; y < height - 1; y++) {
                    for (let x = 1; x < width - 1; x++) {
                        let pixelX = 0;
                        let pixelY = 0;
                        const centerIndex = y * width + x;

                        for (let ky = -1; ky <= 1; ky++) {
                            for (let kx = -1; kx <= 1; kx++) {
                                const currentPixelIndex = centerIndex + ky * width + kx;
                                const grayValue = grayData[currentPixelIndex];
                                pixelX += grayValue * kernelX[ky + 1][kx + 1];
                                pixelY += grayValue * kernelY[ky + 1][kx + 1];
                            }
                        }
                        // Výpočet magnitúdy
                        const magnitude = Math.sqrt(pixelX * pixelX + pixelY * pixelY);
                        magnitudeData[centerIndex] = magnitude;
                        if (magnitude > maxMagnitude) {
                            maxMagnitude = magnitude;
                        }
                    }
                }

                // Normalizácia a prevod na Uint8ClampedArray
                const normalizedMagnitudeData = new Uint8ClampedArray(width * height);
                if (maxMagnitude > 0) {
                    const factor = 255.0 / maxMagnitude;
                    for (let i = 0; i < magnitudeData.length; i++) {
                         // Ignorujeme okraje (y=0, y=height-1, x=0, x=width-1), kde sme nepočítali
                         let x = i % width;
                         let y = Math.floor(i / width);
                         if (x > 0 && x < width -1 && y > 0 && y < height -1) {
                              normalizedMagnitudeData[i] = Math.round(magnitudeData[i] * factor);
                         } else {
                              normalizedMagnitudeData[i] = 0; // Okraje nastavíme na čiernu
                         }
                    }
                }
                return normalizedMagnitudeData;
            }

            // --- Hlavná funkcia pre OCR ---
            async function recognizeTextFromImage(imageData) {
                loadingIndicator.style.display = 'block';
                resetOcrDebug();
                debugContainer.style.display = 'block';

                // Získanie aktuálnych nastavení predspracovania
                const useThreshold = enableThresholdCheckbox.checked;
                const thresholdVal = parseInt(thresholdSlider.value, 10);
                const useGradient = enableGradientCheckbox.checked;
                const gradientThresholdVal = parseInt(gradientThresholdSlider.value, 10);

                try {
                    // 1. Predspracovanie
                    const processedImageDataUrl = await preprocessImage(imageData, useThreshold, thresholdVal, useGradient, gradientThresholdVal);

                    // Zobrazenie predspracovaného obrázka v náhľade (nahradí pôvodný)
                    jsPreviewImage.src = processedImageDataUrl;

                    // 2. OCR volanie
                    const { data: { text } } = await Tesseract.recognize(
                        processedImageDataUrl, // Použijeme spracovaný obrázok
                        'slk+eng', // Jazyky
                        { /* logger: m => console.log(m.status, m.progress) */ } // Voliteľný logger
                    );

                    textContent.textContent = text || '(žiadny text nerozpoznaný)';

                    // 3. Hľadanie a korekcia čísla žiadosti v texte
                    // Vzor: 2 písmená, oddelovač, 4-5 písmen/číslic, oddelovač, 6 číslic (aj O ako 0), oddelovač, 3 písmená/číslice
                    const separatorPattern = '\\s*(?:[-–—‐‑]|\\s+)\\s*'; // Flexibilný oddeľovač
                    const pattern = new RegExp(
                        `([A-Z]{2})${separatorPattern}([A-Z0-9]{4,5})${separatorPattern}([0-9O]{6})${separatorPattern}([A-Z0-9]{3})`,
                        'igm' // insensitive, global, multiline
                    );

                    let match;
                    let correctedMatches = [];
                    // Použijeme metódu matchAll pre jednoduchšie získanie všetkých zhôd
                    const allMatchesIterator = text.matchAll(pattern);

                    for (const match of allMatchesIterator) {
                         // Indexy zachytených skupín: match[1], match[2], match[3], match[4]
                         if (match.length >= 5) { // Uistenie sa, že máme všetky skupiny
                             let part1 = match[1].toUpperCase();
                             // Korekcia 0/O v druhej časti (napr. JPPO1 -> 0 sa mení na O)
                             let part2 = match[2].toUpperCase().replace(/0/g, 'O');
                             // Korekcia O/0 v tretej časti (napr. 123O45 -> O sa mení na 0)
                             let part3 = match[3].toUpperCase().replace(/O/g, '0');
                             let part4 = match[4].toUpperCase();
                             // Spojíme vždy štandardnou pomlčkou
                             let corrected = `${part1}-${part2}-${part3}-${part4}`;
                             correctedMatches.push(corrected);
                         }
                    }

                    // Zobrazenie výsledkov
                    if (correctedMatches.length > 0) {
                        matchesContent.innerHTML = correctedMatches.map(cm =>
                             `<span class="ocr-matches" style="cursor:pointer; text-decoration: underline;" onclick="setRequestNumber('${cm.replace(/'/g, "\\'")}')" title="Vložiť toto číslo">${cm}</span>`
                         ).join(''); // Bez <br>, použije sa display: block zo CSS
                        requestNumberInput.value = correctedMatches[0]; // Automaticky vyplníme prvé nájdené
                    } else {
                        matchesContent.innerHTML = '<span style="color: #aaa;">Neboli nájdené žiadne čísla v očakávanom formáte.</span>';
                    }

                } catch (err) {
                    console.error("Chyba pri OCR alebo predspracovaní:", err);
                    errorContent.textContent = `Chyba: ${err.message || err}`;
                    errorContainer.style.display = 'block';
                } finally {
                    loadingIndicator.style.display = 'none';
                }
            }

            // Pomocná funkcia na vloženie čísla do inputu (globálna pre onclick)
            window.setRequestNumber = function(reqNum) {
                 requestNumberInput.value = reqNum;
            }

        })(); // Koniec anonymnej funkcie (IIFE)
    </script>
</body>
</html>