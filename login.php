<?php
// login.php
require_once 'config.php'; // Načíta konfiguráciu a spustí session_start()
require_once 'functions.php'; // Načíta pomocné funkcie (vr<PERSON><PERSON>e tých pre Remember Me)

// Získanie PDO spojenia hneď na začiatku, ak ho potrebujeme pre checkRememberMeCookie
$pdo = getDbConnection();

// Ak je používateľ už prihlásený cez session, presmeruj na dashboard
if (isset($_SESSION['user_id'])) {
    header("Location: dashboard.php");
    exit;
} 
// Ak nie je prihlásený cez session, skúsime ho prihlásiť cez Remember Me cookie
// checkRememberMeCookie presmeruje na dashboard.php ak je úspešné
elseif (checkRememberMeCookie($pdo)) { 
    // Ak checkRememberMeCookie vráti true, session je nastavená a používateľ je prihlásený.
    // Funkcia checkRememberMeCookie by mala v prípade úspechu presmerovať,
    // ale pre istotu tu môžeme pridať exit, aby sa zvyšok skriptu nevykonal.
    // Ak by checkRememberMeCookie nerobilo presmerovanie, tak by sme ho urobili tu:
    // header("Location: dashboard.php");
    exit; 
}

$error = null; // Inicializácia chybovej premennej

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['username']) && isset($_POST['password'])) {
        $username = trim($_POST['username']);
        $password = $_POST['password'];

        if (empty($username) || empty($password)) {
            $error = "Musíte zadať meno aj heslo.";
        } else {
            try {
                // PDO už máme zhora
                $stmt = $pdo->prepare("SELECT u.id, u.username, u.password, u.name, o.oddelenie_id, o.nazov AS oddelenie_nazov
                                       FROM users u
                                       LEFT JOIN Oddelenia o ON u.oddelenie_id = o.oddelenie_id
                                       WHERE u.username = ? AND u.is_active = 1");
                $stmt->execute([$username]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);

                // Overenie používateľa a hesla
                if ($user && password_verify($password, $user['password'])) {
                    // Heslo je správne, ulož ID do session
                    $_SESSION['user_id'] = $user['id'];
                    
                    // Načítame ostatné údaje používateľa do session pomocou novej funkcie
                    loadUserDataIntoSession($user['id'], $pdo);

                    // Spracovanie "Zapamätať si prihlásenie"
                    if (isset($_POST['remember_me']) && $_POST['remember_me'] === 'yes') {
                        try {
                            // Vygenerujeme unikátnu sériu a token
                            $series = generateToken(16); // Kratšia séria (napr. 16 bajtov -> 32 hex znakov)
                            $token = generateToken(32);  // Dlhší token (napr. 32 bajtov -> 64 hex znakov)
                            
                            // Uložíme do DB a nastavíme cookie
                            if (storePersistentLogin($user['id'], $series, $token, $pdo)) {
                                setPersistentLoginCookie($user['id'], $series, $token);
                            } else {
                                error_log("Nepodarilo sa uložiť persistentný login pre používateľa {$user['id']}.");
                            }
                        } catch (Exception $e) {
                            error_log("Chyba pri nastavovaní persistentného prihlásenia pre používateľa {$user['id']}: " . $e->getMessage());
                            // Pokračujeme v prihlásení aj keď sa persistent login nepodaril
                        }
                    }

                    // Regenerácia session ID po prihlásení pre vyššiu bezpečnosť
                    session_regenerate_id(true);

                    header("Location: dashboard.php");
                    exit;
                } else {
                    // Nesprávne meno alebo heslo, alebo neaktívny používateľ
                    $error = "Nesprávne prihlasovacie meno alebo heslo.";
                }
            } catch (PDOException $e) {
                error_log("Login failed for user $username: " . $e->getMessage());
                $error = "Nastala chyba pri prihlasovaní. Skúste znova.";
            }
        }
    } else {
        $error = "Chýbajú prihlasovacie údaje.";
    }
}
?>
<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Prihlásenie</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container" style="max-width: 400px;">
        <div class="header">Prihlásenie do systému</div>

        <?php if ($error): ?>
            <div class="message error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <form method="POST" action="login.php">
            <label for="username">Prihlasovacie meno:</label>
            <input type="text" id="username" name="username" required autofocus value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>">

            <label for="password">Heslo:</label>
            <input type="password" id="password" name="password" required>

            <div class="form-group" style="margin-top: 10px; margin-bottom: 15px;">
                <input type="checkbox" id="remember_me" name="remember_me" value="yes" <?php echo (isset($_POST['remember_me']) && $_POST['remember_me'] === 'yes') ? 'checked' : ''; ?>>
                <label for="remember_me" style="display: inline-block; margin-left: 5px; font-weight: normal; font-size: 0.9em;">Zapamätať si prihlásenie</label>
            </div>

            <button type="submit">Prihlásiť sa</button>
        </form>
    </div>
</body>
</html>
