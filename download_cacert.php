<?php
// Skript na stiahnutie aktuálneho CA certifikátu
$url = 'https://curl.se/ca/cacert.pem';
$destination = __DIR__ . '/cacert.pem';

echo "Sťahujem aktuálny CA certifikát z $url...\n";

// Vytvoríme kontext s vypnutým overovaním SSL
$context = stream_context_create([
    'ssl' => [
        'verify_peer' => false,
        'verify_peer_name' => false,
    ]
]);

// Použijeme file_get_contents s kontextom na stiahnutie súboru
$certContent = @file_get_contents($url, false, $context);

if ($certContent === false) {
    echo "Chyba pri sťahovaní certifikátu pomocou file_get_contents: " . error_get_last()['message'] . "\n";
    
    // Skúsime alternatívny spôsob pomocou cURL
    echo "Skúšam alternatívny spôsob stiahnutia pomocou cURL...\n";
    
    if (function_exists('curl_init')) {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        $certContent = curl_exec($ch);
        
        if (curl_errno($ch)) {
            echo "Chyba pri sťahovaní certifikátu pomocou cURL: " . curl_error($ch) . "\n";
            curl_close($ch);
            exit(1);
        }
        
        curl_close($ch);
    } else {
        echo "cURL nie je dostupný. Skúste manuálne stiahnuť certifikát z: $url\n";
        exit(1);
    }
}

// Uložíme obsah do súboru
if (file_put_contents($destination, $certContent) === false) {
    echo "Chyba pri ukladaní certifikátu do $destination\n";
    exit(1);
}

echo "Certifikát bol úspešne stiahnutý a uložený do $destination\n";
echo "Veľkosť súboru: " . filesize($destination) . " bajtov\n";

// Overíme, či je súbor platný CA certifikát
if (filesize($destination) < 10000) {
    echo "VAROVANIE: Stiahnutý súbor je príliš malý na to, aby bol platným CA certifikátom.\n";
    echo "Obsah súboru:\n";
    echo file_get_contents($destination) . "\n";
    echo "Skúste manuálne stiahnuť certifikát z prehliadača: $url\n";
}
?>
