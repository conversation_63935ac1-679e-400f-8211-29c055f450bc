<?php
// test_krok4_regenerate_fix.php - Test pre Krok 4: Oprava deštruktívneho prepočtu

require_once 'config.php';
require_once 'functions.php';

try {
    echo "=== Test Krok 4: Oprava deštruktívneho prepočtu ===\n\n";
    
    $testOddelenieId = 1; // Predpokladáme, že existuje oddelenie s ID 1
    $testDate = new DateTimeImmutable('2025-07-01');
    $testForcedStartOrder = 3; // Manuálne nastavené štartovacie poradie
    
    echo "1. Príprava: Vytvorenie priradení s manuálnym forced_start_order...\n";
    
    // Vytvoríme počiatočné priradenia s forced_start_order
    $initialAssignments = calculateDailyAssignments($testDate, $testOddelenieId, $pdo, (int)$testForcedStartOrder);
    saveAssignments($testDate, $initialAssignments, $testOddelenieId, $pdo);
    
    $initialSignature = md5(json_encode($initialAssignments));
    upsertAssignmentMetadataMeta($testDate, $initialSignature, null, null, $testOddelenieId, $pdo, $testForcedStartOrder);
    
    echo "   ✓ Počiatočné priradenia a metadáta vytvorené\n";
    echo "   ✓ forced_start_order uložené: $testForcedStartOrder\n\n";
    
    echo "2. Overenie počiatočného stavu metadát...\n";
    $metaBeforeRegenerate = getAssignmentMetadataMeta($testDate, $testOddelenieId, $pdo);
    if ($metaBeforeRegenerate && $metaBeforeRegenerate['forced_start_order'] === $testForcedStartOrder) {
        echo "   ✓ forced_start_order správne uložené: " . $metaBeforeRegenerate['forced_start_order'] . "\n";
        echo "   ✓ Pôvodná signature: " . $metaBeforeRegenerate['signature'] . "\n";
    } else {
        echo "   ✗ CHYBA: forced_start_order nebolo správne uložené!\n";
        exit(1);
    }
    
    echo "\n3. Test regenerateAssignmentsForDates (simulácia zmeny absencie)...\n";
    echo "   Spúšťam regenerateAssignmentsForDates...\n";
    
    // Toto simuluje situáciu, keď sa zmení absencia a systém prepočíta priradenia
    regenerateAssignmentsForDates($testDate, $testOddelenieId, $pdo);
    
    echo "   ✓ Regenerácia dokončená\n\n";
    
    echo "4. Overenie zachovania forced_start_order po regenerácii...\n";
    $metaAfterRegenerate = getAssignmentMetadataMeta($testDate, $testOddelenieId, $pdo);
    
    if ($metaAfterRegenerate && $metaAfterRegenerate['forced_start_order'] === $testForcedStartOrder) {
        echo "   ✅ ÚSPECH: forced_start_order bolo zachované: " . $metaAfterRegenerate['forced_start_order'] . "\n";
    } else {
        echo "   ❌ CHYBA: forced_start_order nebolo zachované!\n";
        echo "     Očakávané: $testForcedStartOrder\n";
        echo "     Skutočné: " . ($metaAfterRegenerate['forced_start_order'] ?? 'NULL') . "\n";
        exit(1);
    }
    
    echo "5. Overenie aktualizácie signature...\n";
    if ($metaAfterRegenerate['signature'] !== $metaBeforeRegenerate['signature']) {
        echo "   ✓ Signature bola správne aktualizovaná\n";
        echo "     Pôvodná: " . $metaBeforeRegenerate['signature'] . "\n";
        echo "     Nová: " . $metaAfterRegenerate['signature'] . "\n";
    } else {
        echo "   ⚠️  Signature sa nezmenila (môže byť v poriadku ak sa priradenia nezmenili)\n";
    }
    
    echo "\n6. Test scenára bez existujúcich metadát...\n";
    $testDate2 = new DateTimeImmutable('2025-07-02');
    
    // Vytvoríme len priradenia bez metadát
    $assignments2 = calculateDailyAssignments($testDate2, $testOddelenieId, $pdo);
    saveAssignments($testDate2, $assignments2, $testOddelenieId, $pdo);
    
    echo "   ✓ Priradenia vytvorené bez metadát\n";
    
    // Spustíme regeneráciu
    regenerateAssignmentsForDates($testDate2, $testOddelenieId, $pdo);
    
    $metaAfterRegenerate2 = getAssignmentMetadataMeta($testDate2, $testOddelenieId, $pdo);
    if ($metaAfterRegenerate2 && $metaAfterRegenerate2['forced_start_order'] === null) {
        echo "   ✓ Nové metadáta vytvorené bez forced_start_order\n";
    } else {
        echo "   ✗ CHYBA: Neočakávané metadáta pre deň bez pôvodných metadát\n";
    }
    
    echo "\n7. Test viacerých dní naraz...\n";
    $testDate3 = new DateTimeImmutable('2025-07-03');
    $testDate4 = new DateTimeImmutable('2025-07-04');
    $testForcedStartOrder3 = 2;
    
    // Vytvoríme metadáta s forced_start_order pre tretí deň
    $assignments3 = calculateDailyAssignments($testDate3, $testOddelenieId, $pdo);
    saveAssignments($testDate3, $assignments3, $testOddelenieId, $pdo);
    upsertAssignmentMetadataMeta($testDate3, md5(json_encode($assignments3)), null, null, $testOddelenieId, $pdo, $testForcedStartOrder3);
    
    // Vytvoríme priradenia bez metadát pre štvrtý deň
    $assignments4 = calculateDailyAssignments($testDate4, $testOddelenieId, $pdo);
    saveAssignments($testDate4, $assignments4, $testOddelenieId, $pdo);
    
    // Regenerácia viacerých dní
    regenerateAssignmentsForDates([$testDate3, $testDate4], $testOddelenieId, $pdo);
    
    $meta3After = getAssignmentMetadataMeta($testDate3, $testOddelenieId, $pdo);
    $meta4After = getAssignmentMetadataMeta($testDate4, $testOddelenieId, $pdo);
    
    if ($meta3After && $meta3After['forced_start_order'] === $testForcedStartOrder3) {
        echo "   ✓ Tretí deň: forced_start_order zachované\n";
    } else {
        echo "   ✗ Tretí deň: forced_start_order nebolo zachované\n";
    }
    
    if ($meta4After && $meta4After['forced_start_order'] === null) {
        echo "   ✓ Štvrtý deň: nové metadáta bez forced_start_order\n";
    } else {
        echo "   ✗ Štvrtý deň: neočakávané metadáta\n";
    }
    
    // Vyčistenie testovacích dát
    echo "\n8. Čistenie testovacích dát...\n";
    $testDates = [$testDate, $testDate2, $testDate3, $testDate4];
    foreach ($testDates as $date) {
        $cleanupStmt = $pdo->prepare("DELETE FROM assignment_metadata WHERE assignment_date = ? AND oddelenie_id = ?");
        $cleanupStmt->execute([$date->format('Y-m-d'), $testOddelenieId]);
        
        $cleanupAssignments = $pdo->prepare("DELETE a FROM assignments a JOIN users u ON a.user_id = u.id WHERE a.assignment_date = ? AND u.oddelenie_id = ?");
        $cleanupAssignments->execute([$date->format('Y-m-d'), $testOddelenieId]);
    }
    echo "   ✓ Testovacie dáta vyčistené\n";
    
    echo "\n=== Test Krok 4 dokončený úspešne! ===\n";
    echo "✅ regenerateAssignmentsForDates teraz zachováva forced_start_order\n";
    echo "✅ Manuálne nastavenia sa už nestrácajú pri bežných prepočtoch\n";
    
} catch (Exception $e) {
    echo "CHYBA pri teste: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
