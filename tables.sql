-- ============================================================================
-- POZOR! TENTO SKRIPT ZMAŽE VŠETKY DÁTA V NASLEDU<PERSON><PERSON>CICH TABUĽKÁCH:
-- assignments, absences, machine_downtime, holidays, users
-- POUŽITE HO LEN AK CHCETE ÚPLNE NOVÚ INŠTALÁCIU DATABÁZY!
-- ============================================================================

-- Nastavenie pre správne fungovanie foreign keys pri mazaní
SET FOREIGN_KEY_CHECKS=0;

-- Sekcia 1: Zmazanie existujúcich tabuliek (v správnom poradí)
-- Najprv tabuľky, ktoré referencujú iné
DROP TABLE IF EXISTS `assignments`;
DROP TABLE IF EXISTS `absences`;
-- <PERSON><PERSON> ostatné tabuľky
DROP TABLE IF EXISTS `machine_downtime`;
DROP TABLE IF EXISTS `holidays`;
DROP TABLE IF EXISTS `users`; -- Users sa maže ako jedna z posledných, lebo je referencovaná

-- Znovu zapnutie kontroly foreign keys
SET FOREIGN_KEY_CHECKS=1;

-- Sekcia 2: Vytvorenie tabuliek

-- Tabuľka užívateľov
CREATE TABLE IF NOT EXISTS `users` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(255) NOT NULL,
  `username` VARCHAR(100) NOT NULL UNIQUE,
  `password` VARCHAR(255) NOT NULL, -- Store hashed passwords!
  `role1` VARCHAR(50) NOT NULL,
  `role2` VARCHAR(50) NULL,
  `role3` VARCHAR(50) NULL,
  `strojnik_order` INT NULL UNIQUE COMMENT 'Order for machine assignment cycle (1-9)',
  `last_machine_assigned` INT NULL COMMENT 'Last machine number assigned (1, 2, or 3)',
  `last_assignment_date` DATE NULL COMMENT 'Date of last assignment (esp. for strojnik)',
  `is_active` BOOLEAN NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabuľka absencií
CREATE TABLE IF NOT EXISTS `absences` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT NOT NULL,
  `start_date` DATE NOT NULL,
  `end_date` DATE NOT NULL,
  `absence_type` VARCHAR(100) NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabuľka odstávok strojov
CREATE TABLE IF NOT EXISTS `machine_downtime` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `machine_number` INT NOT NULL COMMENT '1, 2, or 3',
  `start_date` DATE NOT NULL,
  `end_date` DATE NOT NULL,
  `reason` VARCHAR(255) NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE INDEX `idx_machine_date` ON `machine_downtime` (`machine_number`, `start_date`, `end_date`);

-- Tabuľka denných priradení
CREATE TABLE IF NOT EXISTS `assignments` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `assignment_date` DATE NOT NULL,
  `user_id` INT NOT NULL,
  `assigned_role` VARCHAR(50) NOT NULL COMMENT 'veduci, schifflieder, skladnik, strojnik, kontrola',
  `machine_number` INT NULL COMMENT '1, 2, or 3 if assigned_role is strojnik',
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  UNIQUE KEY `date_user` (`assignment_date`, `user_id`),
  INDEX `idx_assignment_date` (`assignment_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabuľka sviatkov (podľa holidays.php)
CREATE TABLE IF NOT EXISTS `holidays` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `year` INT NOT NULL,
  `date` DATE NOT NULL,
  `localName` VARCHAR(255) NOT NULL,
  `name` VARCHAR(255) NOT NULL,
  `countryCode` VARCHAR(10) NOT NULL,
  UNIQUE KEY `unique_date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
CREATE INDEX `idx_holiday_year` ON `holidays` (`year`);
CREATE INDEX `idx_holiday_date` ON `holidays` (`date`);


-- Sekcia 3: Vloženie užívateľov
-- POZOR: Nahraďte '$2y$10$...' skutočnými HASHAMI hesiel!
-- Pre generovanie použite v PHP: echo password_hash('SkutocneHeslo', PASSWORD_DEFAULT);

INSERT INTO `users` (`name`, `username`, `password`, `role1`, `role2`, `role3`, `strojnik_order`, `is_active`) VALUES
('Guniš Bohumil', 'bohus', '$2y$10$NahradTotoHashomHesla1', 'veduci', 'shifflieder', 'skladnik', NULL, 1),
('Brúder Radomír', 'radis', '$2y$10$NahradTotoHashomHesla2', 'shifflieder', 'kontrola', NULL, NULL, 1),
('Uhrínová Dominika', 'dominika', '$2y$10$NahradTotoHashomHesla3', 'shifflieder', 'kontrola', NULL, NULL, 1),
('Kotesová Martina', 'mata', '$2y$10$NahradTotoHashomHesla4', 'skladnik', 'strojnik', 'kontrola', 7, 1),
('Morárová Linda', 'linda', '$2y$10$NahradTotoHashomHesla5', 'skladnik', 'strojnik', 'kontrola', 4, 1),
('Kučerová Miriama', 'mirka', '$2y$10$NahradTotoHashomHesla6', 'strojnik', 'kontrola', NULL, 2, 1),
('Sesták Lukáš', 'lukas', '$2y$10$NahradTotoHashomHesla7', 'strojnik', 'kontrola', NULL, 1, 1),
('Mrázková Jana', 'jankam', '$2y$10$NahradTotoHashomHesla8', 'strojnik', 'kontrola', NULL, 3, 1),
('Konrád Oto', 'oto', '$2y$10$NahradTotoHashomHesla9', 'strojnik', 'kontrola', NULL, 5, 1),
('Martanovičová Darina', 'darinka', '$2y$10$NahradTotoHashomHesla10', 'strojnik', 'kontrola', NULL, 6, 1),
('Andrášková Jana', 'jankaa', '$2y$10$NahradTotoHashomHesla11', 'strojnik', 'kontrola', NULL, 8, 1),
('Kramár Marián', 'marosko', '$2y$10$NahradTotoHashomHesla12', 'strojnik', 'kontrola', NULL, 9, 1);

-- Koniec skriptu
-- Môžete sem pridať SHOW TABLES; ak chcete vidieť výsledok hneď
-- SHOW TABLES;