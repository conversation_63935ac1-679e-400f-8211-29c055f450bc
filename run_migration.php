<?php
// run_migration.php - Spustenie migrácie pre pridanie forced_start_order stĺpca

require_once 'config.php';

try {
    echo "Spúšťam migráciu pre pridanie stĺpca forced_start_order...\n";
    
    // Kontrola, či stĺpec už existuje
    $checkStmt = $pdo->prepare("SHOW COLUMNS FROM assignment_metadata LIKE 'forced_start_order'");
    $checkStmt->execute();
    $columnExists = $checkStmt->fetch();
    
    if ($columnExists) {
        echo "Stĺpec forced_start_order už existuje v tabuľke assignment_metadata.\n";
    } else {
        // Pridanie stĺpca
        $alterStmt = $pdo->prepare("ALTER TABLE assignment_metadata ADD COLUMN forced_start_order VARCHAR(255) NULL COMMENT 'Manuálne nastavené štartovacie poradie strojníkov (napr. \"1,2,3\")' AFTER last_downtime");
        $alterStmt->execute();
        echo "Stĺpec forced_start_order bol úspešne pridaný do tabuľky assignment_metadata.\n";
    }
    
    // Zobrazenie aktuálnej štruktúry tabuľky
    echo "\nAktuálna štruktúra tabuľky assignment_metadata:\n";
    $descStmt = $pdo->prepare("DESCRIBE assignment_metadata");
    $descStmt->execute();
    $columns = $descStmt->fetchAll();
    
    foreach ($columns as $column) {
        echo "- {$column['Field']} ({$column['Type']}) - {$column['Null']} - {$column['Key']}\n";
    }
    
    echo "\nMigrácia dokončená úspešne!\n";
    
} catch (PDOException $e) {
    echo "Chyba pri migrácii: " . $e->getMessage() . "\n";
    exit(1);
}
?>
