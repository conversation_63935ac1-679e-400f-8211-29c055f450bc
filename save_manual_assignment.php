<?php
require_once 'config.php';
require_once 'functions.php'; // Možno budete potrebovať getDbConnection()

header('Content-Type: application/json'); // Nastavíme typ odpovede

// Kontrola prihlásenia - odstránime kontrolu na konkrétneho používateľa
if (!isset($_SESSION['user_id']) || !isset($_SESSION['username'])) {
    echo json_encode(['success' => false, 'error' => 'Nedostatočné oprávnenia.']);
    exit;
}

// Kontrola, či ide o POST požiadavku a či existujú potrebné dáta
// Odstránime kontrolu na parameter 'action'
if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['date']) || !isset($_POST['user_id']) || !isset($_POST['machine_number'])) {
    echo json_encode(['success' => false, 'error' => 'Neplatná požiadavka.']);
    exit;
}

$date = $_POST['date'];
$userId = filter_input(INPUT_POST, 'user_id', FILTER_VALIDATE_INT);
$machineNumberInput = $_POST['machine_number']; // Môže byť 'null', '0', '1', '2', '3'

// Validácia dátumu
$d = DateTime::createFromFormat('Y-m-d', $date);
if (!$d || $d->format('Y-m-d') !== $date) {
    echo json_encode(['success' => false, 'error' => 'Neplatný formát dátumu.']);
    exit;
}

// Validácia User ID
if ($userId === false || $userId <= 0) {
    echo json_encode(['success' => false, 'error' => 'Neplatné ID používateľa.']);
    exit;
}

// Získanie oddelenia používateľa, ktorému sa priraďuje
$pdo = getDbConnection(); // Získame PDO spojenie skôr
$stmtUser = $pdo->prepare("SELECT u.oddelenie_id, o.nazov AS oddelenie_nazov FROM users u JOIN Oddelenia o ON u.oddelenie_id = o.oddelenie_id WHERE u.id = ?");
$stmtUser->execute([$userId]);
$user_data = $stmtUser->fetch(PDO::FETCH_ASSOC);

if (!$user_data || $user_data['oddelenie_id'] === null) {
    echo json_encode(['success' => false, 'error' => 'Používateľ neexistuje alebo nemá priradené oddelenie.']);
    exit;
}
$targetUserOddelenieId = (int)$user_data['oddelenie_id'];
$targetUserOddelenieNazov = $user_data['oddelenie_nazov'];

if (!$targetUserOddelenieNazov) {
    // Toto by nemalo nastať, ak je DB konzistentná
    echo json_encode(['success' => false, 'error' => 'Nepodarilo sa načítať názov oddelenia pre používateľa.']);
    exit;
}

// Určenie maximálneho počtu strojov pre dané oddelenie
$max_machines_for_department = 3; // Predvolený počet (napr. pre ID1)
if ($targetUserOddelenieNazov === 'ID3') {
    $max_machines_for_department = 2;
}
// Tu môžete pridať ďalšie `else if` bloky pre iné oddelenia s rôznym počtom strojov

$valid_machine_numbers_for_assignment = range(1, $max_machines_for_department);

// Validácia čísla stroja
$machineNumberToSave = null;
if ($machineNumberInput === 'null') {
    $machineNumberToSave = null;
} elseif ($machineNumberInput === '0') { // Stroj 0 je vždy platný pre "žiadny stroj"
    $machineNumberToSave = 0;
} elseif (in_array($machineNumberInput, array_map('strval', $valid_machine_numbers_for_assignment), true)) {
    // Kontrolujeme voči dynamicky určeným platným číslam strojov pre dané oddelenie
    $machineNumberToSave = (int)$machineNumberInput;
} else {
    echo json_encode(['success' => false, 'error' => "Neplatné číslo stroja '{$machineNumberInput}'. Pre oddelenie {$targetUserOddelenieNazov} sú povolené stroje 0-" . $max_machines_for_department . " alebo zrušenie priradenia."]);
    exit;
}

try {
    $pdo->beginTransaction();

    // Najprv vymaže existujúce priradenie
    $stmtDelete = $pdo->prepare("DELETE FROM assignments WHERE assignment_date = ? AND user_id = ?");
    $stmtDelete->execute([$date, $userId]);

    // Vloží nové priradenie, ak nejde o zrušenie (null)
    if ($machineNumberInput !== 'null') {
        $stmtInsert = $pdo->prepare("
            INSERT INTO assignments (assignment_date, user_id, assigned_role, machine_number)
            VALUES (?, ?, 'strojnik', ?)
        ");
        $stmtInsert->execute([$date, $userId, $machineNumberToSave]);

        // Aktualizuje posledný priradený stroj
        $stmtUpdateUser = $pdo->prepare("UPDATE users SET last_machine_assigned = ?, last_assignment_date = ? WHERE id = ?");
        $stmtUpdateUser->execute([$machineNumberToSave, $date, $userId]);
    }

    $pdo->commit();
    echo json_encode(['success' => true]);
} catch (PDOException $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    error_log("DB Chyba pri manuálnom ukladaní priradenia: " . $e->getMessage());
    echo json_encode(['success' => false, 'error' => 'Databázová chyba pri ukladaní.']);
} catch (Exception $e) {
     if ($pdo->inTransaction()) {
         $pdo->rollBack();
     }
    error_log("Všeobecná chyba pri manuálnom ukladaní priradenia: " . $e->getMessage());
    echo json_encode(['success' => false, 'error' => 'Nastala neočakávaná chyba.']);
}

?>
