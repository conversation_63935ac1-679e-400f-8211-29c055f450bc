<?php
require_once 'config.php';
require_once 'functions.php';

// Overenie prihlásenia
requireLogin();

// Získanie PDO spojenia
$pdo = getDbConnection();

// Načítanie údajov prihláseného používateľa
$loggedInUserId = $_SESSION['user_id'] ?? 0;
$loggedUser = getUserById($loggedInUserId, $pdo);

// --- KĽÚČOVÁ ZMENA: Načítanie aktívneho oddelenia ---
// Prioritu má oddelenie zvolené v dropdowne na dashboarde, potom defaultné oddelenie používateľa.
$activeOddelenieId = $_SESSION['selected_oddelenie_id'] ?? $_SESSION['oddelenie_id'] ?? null;
$activeOddelenieNazov = $_SESSION['selected_oddelenie_nazov'] ?? $_SESSION['oddelenie_nazov'] ?? null;

// Ko<PERSON><PERSON><PERSON>, či je oddelenie zvolené
if ($activeOddelenieId === null) {
    // Uložíme správu, ktorá sa zobrazí na dashboarde
    $_SESSION['message'] = "Prosím, vyberte oddelenie na Dashboarde pre zobrazenie týždenného prehľadu absencií.";
    // Voliteľne môžeme pridať typ správy pre štýlovanie
    // $_SESSION['message_type'] = "error"; // alebo "info", "warning"
    header("Location: dashboard.php");
    exit;
}

// Pridáme kontrolu oprávnení používateľa
$canEditAll = in_array($loggedUser['role1'] ?? '', ['veduci', 'schifflieder'], true);

// Funkcia na kontrolu, či má používateľ oprávnenie upravovať dovolenku pre daného používateľa
function canEditAbsence($targetUserId, $loggedUserId, $canEditAll) {
    return $canEditAll || $targetUserId == $loggedUserId;
}

// Definícia informácií pre zobrazenie typov absencií (rovnaká ako v dashboard.php)
$absenceDisplayInfo = [
    'Celý deň'    => ['shortcut' => 'D', 'color' => '#8bc34a', 'class' => 'absence-d'],
    'Ráno'        => ['shortcut' => 'R', 'color' => '#2196f3', 'class' => 'absence-r'],
    'Poobede'     => ['shortcut' => 'P', 'color' => '#ff9800', 'class' => 'absence-p'],
    'Iné'         => ['shortcut' => 'X', 'color' => '#607d8b', 'class' => 'absence-x']
];


// --- Získanie dátumu pre zobrazenie ---
$requested_date_str = $_GET['date'] ?? date('Y-m-d');
try {
    $current_date_immutable = new DateTimeImmutable($requested_date_str);
} catch (Exception $e) {
    $current_date_immutable = new DateTimeImmutable();
}

$startOfWeek = $current_date_immutable->modify('monday this week');
$endOfWeekDisplay = $startOfWeek->modify('+4 days'); // Po-Pia pre zobrazenie
$endOfWeekQuery = $startOfWeek->modify('+6 days'); // Po-Ne pre DB query

$display_week_str = $startOfWeek->format('d.m.Y') . ' - ' . $endOfWeekDisplay->format('d.m.Y');
if ($activeOddelenieNazov) { // Použijeme aktívny názov oddelenia
    $display_week_str .= ' (Oddelenie: ' . htmlspecialchars($activeOddelenieNazov) . ')';
}

$prev_week = $startOfWeek->modify('-7 days');
$next_week = $startOfWeek->modify('+7 days');
$prev_week_str = $prev_week->format('Y-m-d');
$next_week_str = $next_week->format('Y-m-d');

// Načítame používateľov len pre aktuálne oddelenie
$users = [];
// Kontrola $activeOddelenieId už prebehla vyššie, takže môžeme predpokladať, že je nastavené.
// Ak by nebolo, skript by sa ukončil presmerovaním.
$users = getAllUsers($pdo, $activeOddelenieId);
// Prípadné zoradenie používateľov, ak to funkcia getAllUsers nerobí alebo ak je potrebné iné
// usort($users, function($a, $b) { return strcmp($a['name'], $b['name']); });

$queryStartDate = $startOfWeek->format('Y-m-d');
$queryEndDate = $endOfWeekQuery->format('Y-m-d');

$allAbsencesInPeriod = [];
$stmtAbsences = $pdo->prepare("
    SELECT a.user_id, a.start_date, a.end_date, a.absence_type, u.name as user_name
    FROM absences a
    JOIN users u ON a.user_id = u.id
    WHERE u.oddelenie_id = :oddelenie_id AND (a.start_date <= :end_date AND a.end_date >= :start_date)
    ORDER BY u.name, a.start_date
");
$stmtAbsences->execute([':oddelenie_id' => $activeOddelenieId, ':start_date' => $queryStartDate, ':end_date' => $queryEndDate]);
$allAbsencesInPeriod = $stmtAbsences->fetchAll(PDO::FETCH_ASSOC);

$processedAbsences = [];
foreach ($allAbsencesInPeriod as $absence) {
    $current = new DateTimeImmutable($absence['start_date']);
    $end = new DateTimeImmutable($absence['end_date']);
    $userId = $absence['user_id'];

    while ($current <= $end) {
        $dateStr = $current->format('Y-m-d');
        if ($current >= $startOfWeek && $current <= $endOfWeekDisplay) {
            if (!isset($processedAbsences[$userId])) {
                $processedAbsences[$userId] = [];
            }
            $processedAbsences[$userId][$dateStr] = $absence['absence_type'];
        }
        $current = $current->modify('+1 day');
    }
}

$today_date_str = date('Y-m-d');
?>
<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Týždenný Prehľad Absencií</title>
    <link rel="stylesheet" href="style.css">
    <style>
        /* Základné štýly tabuľky */
        .weekly-table { width: 100%; border-collapse: collapse; margin-bottom: 5px; table-layout: fixed; }
        .weekly-table th, .weekly-table td { border: 1px solid #444; padding: 6px 4px; text-align: center; font-size: 0.9em; height: 34px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
        .weekly-table th { background-color: #333; color: #fff; }
        .weekly-table th.day-header { width: 16%; }
        .weekly-table td.user-name { text-align: left; font-weight: bold; background-color: #3a3a3a; width: 20%; font-size: 1.1em; }
        
        /* Zvýraznenia */
        .weekly-table th.today, .weekly-table td.today { background-color: #2a5a3a; }
        .weekly-table tr.logged-user td { background-color: #3a3a4a; }
        .weekly-table tr.logged-user td.today { background-color: #3a5a5a; }

        /* Triedy pre typy absencií */
        .weekly-table td.absence-d { background-color: #8bc34a; color:white; }
        .weekly-table td.absence-pn { background-color: #f44336; color:white; }
        .weekly-table td.absence-l { background-color: #2196f3; color:white; }
        .weekly-table td.absence-ocr { background-color: #ff9800; color:white; }
        .weekly-table td.absence-sp { background-color: #9c27b0; color:white; }
        .weekly-table td.absence-nv { background-color: #009688; color:white; }
        .weekly-table td.absence-sk { background-color: #795548; color:white; }
        .weekly-table td.absence-x { background-color: #607d8b; color:white; }
        /* Nové typy pre konzistenciu, ak by sa používali špecifické triedy */
        .weekly-table td.absence-r { background-color: #2196f3; color:white; } /* Ráno - modrá */
        .weekly-table td.absence-p { background-color: #ff9800; color:white; } /* Poobede - oranžová */

        
        .weekly-table td.non-working-day { background-color: #2a2a2a; color: #777; cursor: not-allowed; }
        
        /* Ostatné štýly */
        .swipe-area { position: relative; overflow-x: auto; -webkit-overflow-scrolling: touch; }
        .buttons { text-align: center; margin-top: 15px; padding-top: 10px; border-top: 1px solid #333; }
        .table-responsive { position: relative; overflow: auto; height: auto; max-height: none; }

        .absence-legend { margin: 15px 0; text-align: center; font-size: 0.9em; border-top: 1px solid #333; padding-top: 10px; }
        .absence-legend .legend-item { display: inline-flex; align-items: center; margin: 0 5px; padding: 2px 5px; border-radius: 3px; }
        .absence-legend .legend-color { display: inline-block; width: 12px; height: 12px; margin-right: 5px; border: 1px solid #555; border-radius: 3px; }

        .weekly-table td.clickable-day { cursor: pointer; position: relative; }
        .weekly-table td.clickable-day:hover:not(.non-working-day) { background-color: #4e4e4e !important; outline: 1px dashed #8ab4f8; outline-offset: -1px; }
        
        .absence-dropdown { background-color: #333; border: 1px solid #555; border-radius: 5px; box-shadow: 0 4px 8px rgba(0,0,0,0.3); z-index: 10000; min-width: 180px; max-width: 250px; padding: 5px 0; position: absolute; }
        .absence-option { padding: 8px 15px; cursor: pointer; color: #ddd; transition: background-color 0.2s; }
        .absence-option:hover { background-color: #444; color: #fff; }
        .absence-close { position: absolute; top: 5px; right: 5px; width: 20px; height: 20px; line-height: 20px; text-align: center; cursor: pointer; color: #aaa; font-size: 14px; border-radius: 50%; }
        .absence-close:hover { background-color: #555; color: #fff; }
        
        /* Loading indicator pre AJAX volania na absences.php (nie pre celú stránku) */
        body.ajax-loading { cursor: wait; }
        .loading-overlay { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 9999; justify-content: center; align-items: center; }
        .loading-overlay.active { display: flex; }
        .loading-overlay::before { content: ''; width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }

    </style>
</head>
<body>
    <div class="loading-overlay" id="loading-indicator-overlay"></div>
    <div class="container container-wide">
        <div class="header">Týždenný Prehľad Absencií</div>

        <div class="navigation">
            <a href="weekly_absences.php?date=<?php echo $prev_week_str; ?>" id="prev-week-link">&laquo; Predošlý týždeň</a>
            <span id="current-week-display">Týždeň: <?php echo htmlspecialchars($display_week_str); ?></span>
            <a href="weekly_absences.php?date=<?php echo $next_week_str; ?>" id="next-week-link">Nasledujúci týždeň &raquo;</a>
        </div>

        <div id="weekly-absences-content">
            <div class="swipe-area">
                <div class="table-responsive">
                    <table class="weekly-table">
                        <thead>
                            <tr>
                                <th>Meno</th>
                                <?php
                                $dayNames = ['Po', 'Ut', 'St', 'Št', 'Pi'];
                                $currentDayHeader = clone $startOfWeek;
                                for ($i = 0; $i < 5; $i++) {
                                    $dateStrHeader = $currentDayHeader->format('Y-m-d');
                                    $isWorkDayHeader = isWorkingDay($currentDayHeader, $pdo);
                                    $headerClass = 'day-header';
                                    if ($dateStrHeader === $today_date_str) {
                                        $headerClass .= ' today';
                                    }
                                    if (!$isWorkDayHeader) {
                                        $headerClass .= ' non-working-day';
                                    }
                                    echo '<th class="' . $headerClass . '">' . $dayNames[$i] . '<br>' . $currentDayHeader->format('d') . '</th>';
                                    $currentDayHeader = $currentDayHeader->modify('+1 day');
                                }
                                ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): 
                                $isLoggedUserRow = ($user['id'] == $loggedInUserId);
                                $canEditUserAbsences = canEditAbsence((int)$user['id'], (int)$loggedInUserId, $canEditAll);
                            ?>
                                <tr <?php echo $isLoggedUserRow ? 'class="logged-user"' : ''; ?>>
                                    <td class="user-name">
                                    <?php 
                                        $fullName = $user['name'] ?? '';
                                        $nameParts = explode(' ', $fullName);
                                        echo htmlspecialchars($nameParts[0] . (!empty($nameParts[1]) ? ' ' . mb_substr($nameParts[1], 0, 1) . '.' : ''));
                                    ?>
                                    </td>
                                    <?php
                                    $currentDayCell = clone $startOfWeek;
                                    for ($j = 0; $j < 5; $j++) {
                                        $dateStrCell = $currentDayCell->format('Y-m-d');
                                        $isWorkDayCell = isWorkingDay($currentDayCell, $pdo);
                                        
                                        $cellClasses = [];
                                        if ($dateStrCell === $today_date_str) {
                                            $cellClasses[] = 'today';
                                        }
                                        
                                        $absenceTypeOnDay = $processedAbsences[$user['id']][$dateStrCell] ?? null;
                                        $absenceDisplay = '-';
                                        $hasAbsence = false;

                                        if (!$isWorkDayCell) {
                                            $cellClasses[] = 'non-working-day';
                                        } else {
                                            if ($absenceTypeOnDay && isset($absenceDisplayInfo[$absenceTypeOnDay])) {
                                                $hasAbsence = true;
                                                $cellClasses[] = 'has-absence';
                                                $cellClasses[] = $absenceDisplayInfo[$absenceTypeOnDay]['class'];
                                                $absenceDisplay = htmlspecialchars($absenceDisplayInfo[$absenceTypeOnDay]['shortcut']);
                                            }
                                            if ($canEditUserAbsences) {
                                                $cellClasses[] = 'clickable-day';
                                            }
                                        }
                                        
                                        $clickableAttrs = '';
                                        if ($canEditUserAbsences && $isWorkDayCell) {
                                            $clickableAttrs = ' data-date="' . $dateStrCell . '" data-user-id="' . $user['id'] . '" data-user-name="' . htmlspecialchars($user['name']) . '" data-has-absence="' . ($hasAbsence ? 'true' : 'false') . '"';
                                        }

                                        echo '<td class="' . trim(implode(' ', $cellClasses)) . '"' . $clickableAttrs . '>' . $absenceDisplay . '</td>';
                                        $currentDayCell = $currentDayCell->modify('+1 day');
                                    }
                                    ?>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="absence-legend">
                <?php foreach ($absenceDisplayInfo as $type => $info): ?>
                    <div class="legend-item">
                        <span class="legend-color" style="background-color: <?php echo htmlspecialchars($info['color']); ?>;"></span>
                        <?php echo htmlspecialchars($type) . " (" . htmlspecialchars($info['shortcut']) . ")"; ?>
                    </div>
                <?php endforeach; ?>
                 <div class="legend-item"><span class="legend-color non-working-day" style="background-color: #2a2a2a;"></span> Víkend/Sviatok</div>
            </div>
        </div> <div class="buttons">
            <button onclick="location.href='dashboard.php?date=<?php echo $startOfWeek->format('Y-m-d'); ?>'" class="back-button">Späť na Dashboard</button>
        </div>
    </div>

    <script>
        let currentAbsenceDropdown = null;
        // isLoadingWeeklyAbsences už nie je potrebný pre načítanie obsahu, ale môže byť užitočný pre AJAX volania na absences.php
        let isProcessingAbsence = false; 
        const absenceDisplayInfo = <?php echo json_encode($absenceDisplayInfo); ?>;
        // currentDisplayedWeekStart sa nastaví pri načítaní stránky a použije sa na reload
        const currentDisplayedWeekStart = '<?php echo $startOfWeek->format("Y-m-d"); ?>'; 

        document.addEventListener('DOMContentLoaded', function() {
            const weeklyContent = document.getElementById('weekly-absences-content');
            if (weeklyContent) {
                attachWeeklyAbsenceListeners(weeklyContent);
            }
            
            const swipeArea = document.querySelector('.swipe-area'); 
            if (swipeArea) {
                let startX;
                swipeArea.addEventListener('touchstart', function(e) {
                    startX = e.touches[0].clientX;
                }, { passive: true });
                
                swipeArea.addEventListener('touchend', function(e) {
                    const endX = e.changedTouches[0].clientX;
                    const threshold = 100; 
                    const prevLink = document.getElementById('prev-week-link');
                    const nextLink = document.getElementById('next-week-link');
                    
                    if (startX - endX > threshold && nextLink) {
                        window.location.href = nextLink.href; // Použijeme href priamo
                    } else if (endX - startX > threshold && prevLink) {
                        window.location.href = prevLink.href; // Použijeme href priamo
                    }
                });
            }
        });

        function attachWeeklyAbsenceListeners(parentElement) {
            parentElement.addEventListener('click', function(event) {
                const targetCell = event.target.closest('td.clickable-day');
                if (targetCell && !targetCell.classList.contains('non-working-day')) {
                    handleWeeklyCellClick(targetCell);
                }
            });
        }

        function handleWeeklyCellClick(cell) {
            const date = cell.dataset.date;
            const userId = cell.dataset.userId;
            const userName = cell.dataset.userName;
            const hasAbsence = cell.dataset.hasAbsence === 'true';

            console.log(`Cell click: date=${date}, userId=${userId}, userName=${userName}, hasAbsence=${hasAbsence}`);

            if (hasAbsence) {
                confirmDeleteWeeklyAbsence(date, userId, userName);
            } else {
                showAbsenceTypeDropdown(date, userId, userName, cell);
            }
        }

        function showAbsenceTypeDropdown(date, userId, userName, targetElement) {
            closeAbsenceDropdown(); 

            const dropdown = document.createElement('div');
            dropdown.className = 'absence-dropdown';
            
            Object.keys(absenceDisplayInfo).forEach(type => {
                const option = document.createElement('div');
                option.className = 'absence-option';
                option.textContent = type;
                option.onclick = (e) => {
                    e.stopPropagation();
                    submitWeeklyAbsence(date, userId, type, userName);
                    closeAbsenceDropdown();
                };
                dropdown.appendChild(option);
            });
            
            const closeButton = document.createElement('div');
            closeButton.className = 'absence-close';
            closeButton.textContent = '×';
            closeButton.onclick = (e) => { e.stopPropagation(); closeAbsenceDropdown(); };
            dropdown.appendChild(closeButton);
            
            document.body.appendChild(dropdown);
            currentAbsenceDropdown = dropdown;
            
            const rect = targetElement.getBoundingClientRect();
            let topPosition = rect.bottom + window.scrollY;
            let leftPosition = rect.left + window.scrollX;

            setTimeout(() => { 
                if (!currentAbsenceDropdown) return; 
                if (leftPosition + currentAbsenceDropdown.offsetWidth > window.innerWidth) {
                    leftPosition = window.innerWidth - currentAbsenceDropdown.offsetWidth - 5; 
                }
                if (topPosition + currentAbsenceDropdown.offsetHeight > (window.innerHeight + window.scrollY)) {
                    topPosition = rect.top + window.scrollY - currentAbsenceDropdown.offsetHeight;
                }
                if (leftPosition < 0) leftPosition = 5;
                if (topPosition < 0) topPosition = 5;

                currentAbsenceDropdown.style.top = `${topPosition}px`;
                currentAbsenceDropdown.style.left = `${leftPosition}px`;
            }, 0);
            
            setTimeout(() => { 
                document.addEventListener('click', closeAbsenceDropdownOnClickOutside, true);
            }, 0);
        }

        function closeAbsenceDropdown() {
            if (currentAbsenceDropdown) {
                currentAbsenceDropdown.remove();
                currentAbsenceDropdown = null;
                document.removeEventListener('click', closeAbsenceDropdownOnClickOutside, true);
            }
        }

        function closeAbsenceDropdownOnClickOutside(event) {
            if (currentAbsenceDropdown && !currentAbsenceDropdown.contains(event.target)) {
                if (!event.target.closest('td.clickable-day')) {
                    closeAbsenceDropdown();
                }
            }
        }
        
        function showLoadingOverlay() {
            document.getElementById('loading-indicator-overlay').classList.add('active');
            document.body.classList.add('ajax-loading');
        }

        function hideLoadingOverlay() {
            document.getElementById('loading-indicator-overlay').classList.remove('active');
            document.body.classList.remove('ajax-loading');
        }

        async function submitWeeklyAbsence(date, userId, absenceType, userName) {
            if (isProcessingAbsence) return;
            isProcessingAbsence = true;
            showLoadingOverlay();
            let rawResponseText = ''; 

            console.log(`Submitting absence: date=${date}, userId=${userId}, type=${absenceType}, userName=${userName}`);

            try {
                const response = await fetch('absences.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `action=add&user_id=${userId}&start_date=${date}&end_date=${date}&absence_type=${encodeURIComponent(absenceType)}`
                });
                
                rawResponseText = await response.text(); 
                console.log('Raw response from submitWeeklyAbsence:', rawResponseText);

                if (!response.ok) { 
                    throw new Error(`Server error: ${response.status} ${response.statusText}. Response: ${rawResponseText.substring(0, 200)}`);
                }
                
                const result = JSON.parse(rawResponseText); 

                if (result.success) {
                    console.log('Absence submitted successfully, reloading page for week:', currentDisplayedWeekStart);
                    // Presmerujeme na aktuálne zobrazený týždeň, aby sa dáta načítali nanovo
                    window.location.href = `weekly_absences.php?date=${currentDisplayedWeekStart}`;
                } else {
                    alert('Chyba pri pridávaní absencie: ' + (result.message || 'Neznáma chyba servera.'));
                    console.error('Server response (not success):', result);
                }
            } catch (error) { 
                console.error('Failed to submit weekly absence. Raw server response:', rawResponseText);
                console.error('Error details:', error);
                let alertMessage = `Chyba pri spracovaní požiadavky. Skúste to znova.\nDetail: ${error.message}`;
                if (error instanceof SyntaxError) { 
                    alertMessage += `\n\nChyba pri parsovaní odpovede zo servera. Server pravdepodobne nevrátil platný JSON. Skontrolujte skript "absences.php". Surová odpoveď bola: "${rawResponseText.substring(0,200)}..."`;
                }
                alert(alertMessage);
            } finally {
                isProcessingAbsence = false;
                hideLoadingOverlay();
            }
        }

        async function confirmDeleteWeeklyAbsence(date, userId, userName) {
            if (confirm(`Naozaj chcete odstrániť absenciu pre používateľa ${userName} na deň ${new Date(date+'T00:00:00').toLocaleDateString('sk-SK')}?`)) {
                if (isProcessingAbsence) return;
                isProcessingAbsence = true;
                showLoadingOverlay();
                let rawResponseText = '';
                console.log(`Confirming delete: date=${date}, userId=${userId}, userName=${userName}`);

                try {
                    const response = await fetch('absences.php?action=delete', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                        body: `date=${date}&uid=${userId}`
                    });

                    rawResponseText = await response.text(); 
                    console.log('Raw response from confirmDeleteWeeklyAbsence:', rawResponseText);

                    if (!response.ok) { 
                        throw new Error(`Server error: ${response.status} ${response.statusText}. Response: ${rawResponseText.substring(0,200)}`);
                    }
                    
                    const result = JSON.parse(rawResponseText);

                    if (result.success) {
                        console.log('Absence deleted successfully, reloading page for week:', currentDisplayedWeekStart);
                        window.location.href = `weekly_absences.php?date=${currentDisplayedWeekStart}`;
                    } else {
                        alert('Chyba pri odstraňovaní absencie: ' + (result.message || 'Neznáma chyba servera.'));
                        console.error('Server response (not success):', result);
                    }
                } catch (error) { 
                    console.error('Failed to delete weekly absence. Raw server response:', rawResponseText);
                    console.error('Error details:', error);
                    let alertMessage = `Chyba pri spracovaní požiadavky. Skúste to znova.\nDetail: ${error.message}`;
                     if (error instanceof SyntaxError) { 
                        alertMessage += `\n\nChyba pri parsovaní odpovede zo servera. Server pravdepodobne nevrátil platný JSON. Skontrolujte skript "absences.php". Surová odpoveď bola: "${rawResponseText.substring(0,200)}..."`;
                    }
                    alert(alertMessage);
                } finally {
                    isProcessingAbsence = false;
                    hideLoadingOverlay();
                }
            }
        }
        // Odstránená funkcia loadWeeklyAbsences a listener pre popstate, keďže už nepoužívame AJAX na obnovu obsahu.
    </script>
</body>
</html>
