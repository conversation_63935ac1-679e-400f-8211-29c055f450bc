<?php
// Potlačenie varovaní o zastaraných funkciách
error_reporting(E_ALL & ~E_DEPRECATED & ~E_NOTICE);
require_once 'config.php';
require_once 'functions.php';

// Overenie prihlásenia
requireLogin();

// Získanie PDO spojenia
$pdo = getDbConnection();

// Načítanie údajov prihláseného používateľa
$loggedInUserId = $_SESSION['user_id'] ?? 0;
$loggedUser = getUserById($loggedInUserId, $pdo);

// Inicializácia premenných pre výsledok
$externalStatusResult = null; // Premenná pre výsledok z minv.sk
$errorMessage = null;
$successMessage = null;
$uploadedImagePath = null;

// Spracovanie formulára
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Spracovanie nahrávania obrázku
    if (isset($_FILES['request_image']) && $_FILES['request_image']['error'] === UPLOAD_ERR_OK) {
        $uploadDir = 'uploads/';
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }
        $fileName = $_FILES['request_image']['name'];
        $fileType = $_FILES['request_image']['type'];
        $fileTmpName = $_FILES['request_image']['tmp_name'];
        // ... (zvyšok validácie a presunu súboru - bez zmeny) ...
         $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
         if (!in_array($fileType, $allowedTypes)) {
             $errorMessage = "Neplatný formát súboru. Povolené formáty sú: JPG, PNG, GIF.";
         } elseif ($_FILES['request_image']['size'] > 5 * 1024 * 1024) {
             $errorMessage = "Súbor je príliš veľký. Maximálna veľkosť je 5MB.";
         } else {
             $newFileName = uniqid() . '_' . $fileName;
             $destination = $uploadDir . $newFileName;
             if (move_uploaded_file($fileTmpName, $destination)) {
                 $uploadedImagePath = $destination;
             } else {
                 $errorMessage = "Nastala chyba pri nahrávaní súboru.";
             }
         }
    }

    // Spracovanie kontroly stavu žiadosti
    if (isset($_POST['check_status'])) {
        $requestNumber = isset($_POST['request_number']) ? htmlspecialchars(trim($_POST['request_number']), ENT_QUOTES, 'UTF-8') : '';

        if (empty($requestNumber)) {
            $errorMessage = "Prosím, zadajte číslo žiadosti.";
        } else {
            try {
                $externalStatusResult = getExternalRequestStatus($requestNumber);
            } catch (Exception $e) {
                $errorMessage = "Nastala chyba pri zisťovaní stavu žiadosti na minv.sk: " . $e->getMessage();
            }
        }
    }
}

// Funkcia na získanie stavu žiadosti z externého systému (bez zmeny)
function getExternalRequestStatus($requestNumber) {
    $url = 'https://www.minv.sk/?zistenie-stavu-spracovania-ziadosti';
    $ch = curl_init();
    // ... (nastavenia cURL - bez zmeny) ...
     curl_setopt($ch, CURLOPT_URL, $url);
     curl_setopt($ch, CURLOPT_POST, 1);
     curl_setopt($ch, CURLOPT_POSTFIELDS, "cislo=" . urlencode($requestNumber));
     curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
     curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
     curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
     curl_setopt($ch, CURLOPT_TIMEOUT, 15);

    $response = curl_exec($ch);

    if (curl_errno($ch)) {
        $error_msg = curl_error($ch);
        curl_close($ch);
        throw new Exception('Chyba pri komunikácii so serverom minv.sk (cURL Error): ' . $error_msg);
    }
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($http_code !== 200) {
         throw new Exception("Server minv.sk vrátil neočakávaný stavový kód: {$http_code}");
    }
    if (empty($response)) {
         throw new Exception("Server minv.sk vrátil prázdnu odpoveď.");
    }

    // Extrakcia výsledku z odpovede (bez zmeny)
    $result = [];
    if (preg_match('/<h4[^>]*>(.*?)<\/h4>/is', $response, $matches)) {
        $result['status'] = trim(strip_tags($matches[1]));
    } else {
         if (strpos($response, 'nebola nájdená') !== false) {
              $result['status'] = "Žiadosť s daným číslom nebola nájdená.";
         } elseif (strpos($response, 'nesprávny formát') !== false) {
              $result['status'] = "Zadané číslo žiadosti má nesprávny formát.";
         } else {
            $result['status'] = "Nepodarilo sa zistiť stav žiadosti (neznáma odpoveď zo servera).";
         }
    }
    if (preg_match('/<div class="text"[^>]*>(.*?)<\/div>/is', $response, $matches)) {
        $result['details'] = trim(strip_tags($matches[1]));
    }
    return $result;
}

// Určenie návratovej URL (bez zmeny)
$returnUrl = $_GET['return'] ?? 'dashboard.php';
if (!preg_match('/^(dashboard\.php|monthly_overview\.php|absences\.php|weekly_absences\.php)/', $returnUrl) && !filter_var($returnUrl, FILTER_VALIDATE_URL)) {
    $returnUrl = 'dashboard.php';
}
?>

<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Zistenie stavu žiadosti</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdn.jsdelivr.net/npm/tesseract.js@4/dist/tesseract.min.js"></script>
    <style>
        /* CSS štýly zostávajú nezmenené, pridáme len pre nové prvky */
        body { font-family: sans-serif; background-color: #1e1e1e; color: #f0f0f0; margin: 0; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .header { padding: 15px; background-color: #333; color: #f0f0f0; text-align: center; margin-bottom: 20px; border-radius: 8px; }
        .request-form { max-width: 500px; margin: 0 auto; padding: 20px; background-color: #2a2a2a; border-radius: 8px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3); margin-bottom: 20px; }
        .external-status-result { max-width: 500px; margin: 20px auto; }
        .request-result { max-width: 500px; margin: 0 auto; padding: 20px; background-color: #2a2a2a; border-radius: 8px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3); margin-bottom: 20px; }
        .error-message { color: #ff6b6b; background-color: rgba(255, 107, 107, 0.1); border: 1px solid rgba(255, 107, 107, 0.3); padding: 10px 15px; border-radius: 4px; margin-bottom: 15px; text-align: center; }
        .success-message { color: #4CAF50; background-color: rgba(76, 175, 80, 0.1); border: 1px solid rgba(76, 175, 80, 0.3); padding: 10px 15px; border-radius: 4px; margin-bottom: 15px; text-align: center; }
        .status-success-display { color: #4CAF50; background-color: rgba(76, 175, 80, 0.15); border: 1px solid rgba(76, 175, 80, 0.4); padding: 15px 20px; border-radius: 8px; margin-bottom: 20px; text-align: center; font-size: 1.3em; font-weight: bold; }
        .status-other-display { color: #f0f0f0; background-color: #3a3a3a; border: 1px solid #555; padding: 15px 20px; border-radius: 8px; margin-bottom: 20px; text-align: center; font-size: 1.2em; }
        .status-details { background-color: #2a2a2a; padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #444; }
        .status-details strong { color: #8ab4f8; display: block; margin-bottom: 8px; }
        .status-details p { color: #f0f0f0; margin: 0; line-height: 1.6; }
        .buttons { display: flex; justify-content: space-between; margin-top: 20px; }
        .back-button { background-color: #555; color: #fff; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer; transition: background-color 0.3s; text-decoration: none; display: inline-block; }
        .check-button { background-color: #3a6ea5; color: #fff; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer; transition: background-color 0.3s; }
        .file-upload { margin-bottom: 15px; }
        .file-upload label { display: block; margin-bottom: 5px; color: #f0f0f0; }
        .file-upload-wrapper { position: relative; margin-bottom: 10px; }
        .file-upload input[type="file"] { width: 100%; padding: 10px; background-color: #333; border: 1px dashed #555; border-radius: 4px; color: #e0e0e0; cursor: pointer; box-sizing: border-box; }
        .file-info { color: #bbb; font-size: 0.9em; margin-top: 5px; }
        .image-preview { margin-top: 15px; text-align: center; }
        .image-preview img { max-width: 100%; max-height: 200px; border: 1px solid #444; border-radius: 4px; background-color: #333; }
        .image-preview p { margin-bottom: 5px; color: #f0f0f0; }
        .loading-indicator { display: none; text-align: center; margin-top: 10px; color: #8ab4f8; }
        .loading-spinner { display: inline-block; width: 20px; height: 20px; border: 3px solid rgba(138, 180, 248, 0.3); border-radius: 50%; border-top-color: #8ab4f8; animation: spin 1s ease-in-out infinite; margin-right: 10px; vertical-align: middle; }
        @keyframes spin { to { transform: rotate(360deg); } }
        .ocr-debug { margin-top: 15px; padding: 10px; background-color: #333; border: 1px solid #555; border-radius: 4px; color: #f0f0f0; font-family: monospace; white-space: pre-wrap; word-break: break-word; max-height: 200px; overflow-y: auto; }
        .ocr-debug h4 { margin-top: 0; color: #8ab4f8; border-bottom: 1px solid #444; padding-bottom: 5px; }
        .ocr-error { color: #ff6b6b; }
        .ocr-matches { color: #4CAF50; }
        label { color: #f0f0f0; margin-bottom: 5px; display: block; }
        input[type="text"] { width: 100%; padding: 10px; margin-bottom: 15px; background-color: #333; border: 1px solid #555; border-radius: 4px; color: #f0f0f0; box-sizing: border-box; }

        /* --- ŠTÝLY PRE NOVÉ OVLÁDACIE PRVKY --- */
        .preprocessing-controls { border: 1px solid #444; padding: 15px; margin-top: 20px; border-radius: 8px; background-color: #303030; }
        .control-group { margin-bottom: 15px; padding-bottom: 15px; border-bottom: 1px solid #444; }
        .control-group:last-child { margin-bottom: 0; padding-bottom: 0; border-bottom: none; }
        .control-header { display: flex; align-items: center; margin-bottom: 10px; }
        .control-header label { margin-bottom: 0; margin-left: 10px; cursor: pointer; }
        .control-header input[type="checkbox"] { width: 18px; height: 18px; cursor: pointer; }
        .slider-container label { font-size: 0.9em; color: #bbb; margin-bottom: 3px; }
        .slider-container input[type="range"] { width: 100%; cursor: pointer; margin-top: 5px;}
        .slider-container span { font-weight: bold; color: #8ab4f8; margin-left: 5px;}
        /* --------------------------------------- */

    </style>
</head>
<body>
    <div class="container">
        <div class="header">Zistenie stavu žiadosti</div>

         <?php if ($externalStatusResult): ?>
        <div class="external-status-result">
            <?php
            $statusText = $externalStatusResult['status'] ?? 'Neznámy stav';
            $statusClass = (mb_stripos($statusText, 'prijatá') !== false) ? 'status-success-display' : 'status-other-display';
            ?>
            <div class="<?= $statusClass ?>">
                 <?= htmlspecialchars($statusText) ?>
            </div>
            <?php if (!empty($externalStatusResult['details'])): ?>
                <div class="status-details">
                    <strong>Detaily:</strong>
                    <p><?= nl2br(htmlspecialchars($externalStatusResult['details'])) ?></p>
                </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <?php if ($errorMessage): ?> <div class="error-message"><?= htmlspecialchars($errorMessage) ?></div> <?php endif; ?>
        <?php if ($successMessage): ?> <div class="success-message"><?= htmlspecialchars($successMessage) ?></div> <?php endif; ?>

        <div class="request-form">
             <form method="post" action="request_status.php<?= !empty($returnUrl) ? '?return=' . urlencode($returnUrl) : '' ?>" enctype="multipart/form-data">
                <label for="request_number">Zadajte číslo žiadosti:</label>
                <input type="text" id="request_number" name="request_number" required
                       value="<?= htmlspecialchars($_POST['request_number'] ?? '') ?>"
                       placeholder="Napr. DS-JPPO1-123456-ABC">

                <div class="file-upload">
                    <label for="request_image">Nahrajte obrázok (voliteľné, pre OCR):</label>
                    <div class="file-upload-wrapper">
                        <input type="file" id="request_image" name="request_image" accept="image/jpeg, image/png, image/gif">
                    </div>
                    <div class="file-info">Povolené formáty: JPG, PNG, GIF. Max. veľkosť: 5MB</div>

                    <div class="preprocessing-controls">
                        <div class="control-group">
                            <div class="control-header">
                                <input type="checkbox" id="enableThreshold" name="enableThreshold" checked>
                                <label for="enableThreshold">Zapnúť Thresholding (Binarizácia)</label>
                            </div>
                            <div class="slider-container">
                                <label for="thresholdSlider">Prahová hodnota: <span id="thresholdValueDisplay">128</span></label>
                                <input type="range" id="thresholdSlider" name="thresholdSlider" min="0" max="255" value="128">
                            </div>
                        </div>
                        <div class="control-group">
                            <div class="control-header">
                                <input type="checkbox" id="enableGradient" name="enableGradient">
                                <label for="enableGradient">Zapnúť Gradient Map (Experimentálne)</label>
                            </div>
                            <div class="slider-container">
                                <label for="gradientThresholdSlider">Prah gradientu: <span id="gradientThresholdValueDisplay">50</span></label>
                                <input type="range" id="gradientThresholdSlider" name="gradientThresholdSlider" min="0" max="255" value="50">
                            </div>
                        </div>
                    </div>
                    <div class="loading-indicator" id="ocr-loading">
                        <div class="loading-spinner"></div>
                        <span>Rozpoznávanie textu z obrázka...</span>
                    </div>

                    <div id="ocr-debug-container" style="display: none;">
                        <div id="ocr-recognized-text" class="ocr-debug"><h4>Rozpoznaný text:</h4><div id="ocr-text-content"></div></div>
                        <div id="ocr-matches-container" class="ocr-debug"><h4>Nájdené zhody:</h4><div id="ocr-matches-content"></div></div>
                        <div id="ocr-error-container" class="ocr-debug" style="display: none;"><h4>Chyba OCR:</h4><div id="ocr-error-content" class="ocr-error"></div></div>
                    </div>
                     <?php if ($uploadedImagePath): ?>
                    <div class="image-preview" id="uploaded-image-preview"><p>Nahraný obrázok:</p><img src="<?= htmlspecialchars($uploadedImagePath) ?>" alt="Nahraný obrázok"></div>
                    <?php endif; ?>
                     <div class="image-preview" id="js-image-preview" style="display: none;"><p>Náhľad obrázka:</p><img id="js-preview-img" src="#" alt="Náhľad obrázka"></div>
                </div>

                <div class="buttons">
                    <a href="<?= htmlspecialchars($returnUrl) ?>" class="back-button">Späť</a>
                    <button type="submit" name="check_status" class="check-button">Zisti stav žiadosti</button>
                </div>
            </form>
        </div>

        <div style="text-align: center; margin-top: 20px; font-size: 0.9em; color: #777;">
            Prihlásený ako: <?php echo htmlspecialchars($loggedUser['name'] ?? $_SESSION['user_name'] ?? 'Neznámy'); ?>
        </div>
    </div>

    <script>
        // --- JavaScript Kód ---

        // Získanie referencií na DOM elementy
        const requestImageInput = document.getElementById('request_image');
        const jsPreviewContainer = document.getElementById('js-image-preview');
        const jsPreviewImage = document.getElementById('js-preview-img');
        const uploadedPreviewContainer = document.getElementById('uploaded-image-preview');
        // Nové elementy
        const enableThresholdCheckbox = document.getElementById('enableThreshold');
        const thresholdSlider = document.getElementById('thresholdSlider');
        const thresholdValueDisplay = document.getElementById('thresholdValueDisplay');
        const enableGradientCheckbox = document.getElementById('enableGradient');
        const gradientThresholdSlider = document.getElementById('gradientThresholdSlider');
        const gradientThresholdValueDisplay = document.getElementById('gradientThresholdValueDisplay');
        // Ostatné
        const loadingIndicator = document.getElementById('ocr-loading');
        const debugContainer = document.getElementById('ocr-debug-container');
        const textContent = document.getElementById('ocr-text-content');
        const matchesContent = document.getElementById('ocr-matches-content');
        const errorContainer = document.getElementById('ocr-error-container');
        const errorContent = document.getElementById('ocr-error-content');
        const requestNumberInput = document.getElementById('request_number');


        // Listener pre výber súboru
        requestImageInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // Validácia
                const fileType = file.type;
                const validImageTypes = ['image/jpeg', 'image/png', 'image/gif'];
                if (!validImageTypes.includes(fileType)) {
                    alert('Neplatný formát súboru. Povolené formáty sú: JPG, PNG, GIF.');
                    this.value = ''; jsPreviewContainer.style.display = 'none'; return;
                }
                if (file.size > 5 * 1024 * 1024) {
                    alert('Súbor je príliš veľký. Maximálna veľkosť je 5MB.');
                    this.value = ''; jsPreviewContainer.style.display = 'none'; return;
                }

                // Zobrazenie náhľadu a spustenie OCR
                const reader = new FileReader();
                reader.onload = function(event) {
                     if (uploadedPreviewContainer) uploadedPreviewContainer.style.display = 'none';
                    jsPreviewImage.src = event.target.result;
                    jsPreviewContainer.style.display = 'block';
                    resetOcrDebug();
                    recognizeTextFromImage(event.target.result); // Spustí OCR
                };
                reader.readAsDataURL(file);
            } else {
                 jsPreviewContainer.style.display = 'none';
                  if (uploadedPreviewContainer) uploadedPreviewContainer.style.display = 'block';
            }
        });

        // Listener pre Threshold slider
        if (thresholdSlider && thresholdValueDisplay) {
            thresholdValueDisplay.textContent = thresholdSlider.value;
            thresholdSlider.addEventListener('input', () => {
                thresholdValueDisplay.textContent = thresholdSlider.value;
            });
            // Bonus: Ak sa zmení checkbox, povolíme/zakážeme slider
            enableThresholdCheckbox.addEventListener('change', () => {
                thresholdSlider.disabled = !enableThresholdCheckbox.checked;
            });
             thresholdSlider.disabled = !enableThresholdCheckbox.checked; // Počiatočné nastavenie
        }

        // Listener pre Gradient Threshold slider
        if (gradientThresholdSlider && gradientThresholdValueDisplay) {
             gradientThresholdValueDisplay.textContent = gradientThresholdSlider.value;
             gradientThresholdSlider.addEventListener('input', () => {
                 gradientThresholdValueDisplay.textContent = gradientThresholdSlider.value;
             });
             // Bonus: Ak sa zmení checkbox, povolíme/zakážeme slider
            enableGradientCheckbox.addEventListener('change', () => {
                gradientThresholdSlider.disabled = !enableGradientCheckbox.checked;
            });
             gradientThresholdSlider.disabled = !enableGradientCheckbox.checked; // Počiatočné nastavenie
        }

        // Reset OCR Debug výstupov
        function resetOcrDebug() {
            debugContainer.style.display = 'none';
            textContent.textContent = '';
            matchesContent.textContent = '';
            errorContainer.style.display = 'none';
            errorContent.textContent = '';
        }

        // --- Funkcia na predspracovanie obrázka (Thresholding + Gradient Map) ---
        function preprocessImage(imageDataUrl, enableThreshold, thresholdValue, enableGradient, gradientThresholdValue) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = () => {
                    const canvas = document.createElement('canvas');
                    const width = img.width;
                    const height = img.height;
                    canvas.width = width;
                    canvas.height = height;
                    const ctx = canvas.getContext('2d', { willReadFrequently: true }); // Optimalizácia pre getImageData
                    if (!ctx) return reject(new Error('Canvas context error.'));

                    ctx.drawImage(img, 0, 0);
                    let imageData = ctx.getImageData(0, 0, width, height);
                    let data = imageData.data;

                    // 1. Konverzia na stupne šedi (vždy potrebná pre ďalšie kroky)
                    let grayData = new Uint8ClampedArray(width * height);
                    for (let i = 0, j = 0; i < data.length; i += 4, j++) {
                        const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
                        grayData[j] = gray;
                    }

                    // 2. Aplikácia Gradient Map (Sobel Filter + Threshold) - AK JE ZAPNUTÁ
                    if (enableGradient) {
                        console.log(`Applying Gradient Map with threshold: ${gradientThresholdValue}`);
                        const sobelData = applySobelFilter(grayData, width, height);
                        // Aplikujeme threshold na výsledok Sobel filtra
                        for (let i = 0, j = 0; i < data.length; i += 4, j++) {
                             const newValue = sobelData[j] > gradientThresholdValue ? 255 : 0;
                             data[i] = data[i + 1] = data[i + 2] = newValue;
                             data[i + 3] = 255; // Alpha
                        }
                    }
                    // 3. Aplikácia štandardného Thresholdingu - AK NIE JE Gradient ZAPNUTÝ a Thresholding JE ZAPNUTÝ
                    else if (enableThreshold) {
                        console.log(`Applying standard Thresholding: ${thresholdValue}`);
                        for (let i = 0, j = 0; i < data.length; i += 4, j++) {
                            const newValue = grayData[j] > thresholdValue ? 255 : 0;
                            data[i] = data[i + 1] = data[i + 2] = newValue;
                            data[i + 3] = 255; // Alpha
                        }
                    }
                    // 4. Ak nie je zapnuté nič, vrátime pôvodný obrázok (alebo len grayscale?)
                    // Pre Tesseract je najlepšie binárny, takže ak nič nie je zapnuté, môžeme napr. použiť default threshold
                    // Alebo vrátiť pôvodný, ale to nemusí byť dobré. Zatiaľ ponecháme tak, že ak nič nie je zapnuté,
                    // zostane pôvodný farebný (pretože sme nemenili 'data' v tomto prípade).
                    // ÚPRAVA: Vrátime aspoň grayscale, ak nič nie je zapnuté
                     else {
                          console.log("Applying Grayscale only (no effects enabled)");
                          for (let i = 0, j = 0; i < data.length; i += 4, j++) {
                               data[i] = data[i + 1] = data[i + 2] = grayData[j];
                               data[i + 3] = 255;
                          }
                     }


                    // Vloženie upravených dát späť na canvas
                    ctx.putImageData(imageData, 0, 0);
                    resolve(canvas.toDataURL('image/png')); // Export ako PNG
                };
                img.onerror = (err) => reject(new Error('Image loading failed for preprocessing.'));
                img.src = imageDataUrl;
            });
        }

        // --- Pomocná funkcia pre Sobel Filter (Výpočet magnitúdy gradientu) ---
        function applySobelFilter(grayData, width, height) {
            const kernelX = [
                [-1, 0, 1],
                [-2, 0, 2],
                [-1, 0, 1]
            ];
            const kernelY = [
                [-1, -2, -1],
                [ 0,  0,  0],
                [ 1,  2,  1]
            ];
            const magnitudeData = new Uint8ClampedArray(width * height);
            let maxMagnitude = 0; // Na normalizáciu

            for (let y = 1; y < height - 1; y++) {
                for (let x = 1; x < width - 1; x++) {
                    let pixelX = 0;
                    let pixelY = 0;
                    const centerIndex = y * width + x;

                    for (let ky = -1; ky <= 1; ky++) {
                        for (let kx = -1; kx <= 1; kx++) {
                             const currentPixelIndex = centerIndex + ky * width + kx;
                             const grayValue = grayData[currentPixelIndex];
                             pixelX += grayValue * kernelX[ky + 1][kx + 1];
                             pixelY += grayValue * kernelY[ky + 1][kx + 1];
                        }
                    }

                    const magnitude = Math.sqrt(pixelX * pixelX + pixelY * pixelY);
                    magnitudeData[centerIndex] = magnitude; // Uložíme zatiaľ nenormalizované
                    if(magnitude > maxMagnitude) {
                        maxMagnitude = magnitude;
                    }
                }
            }

            // Normalizácia magnitúdy na rozsah 0-255
            if (maxMagnitude > 0) {
                 const factor = 255.0 / maxMagnitude;
                 for(let i = 0; i < magnitudeData.length; i++) {
                      magnitudeData[i] = Math.round(magnitudeData[i] * factor);
                 }
            }

            return magnitudeData;
        }


        // --- Funkcia na rozpoznanie textu (číta hodnoty z checkboxov a sliderov) ---
        async function recognizeTextFromImage(imageData) {
            loadingIndicator.style.display = 'block';
            resetOcrDebug(); // Presunutý reset sem
            debugContainer.style.display = 'block';

            // Načítanie stavu ovládacích prvkov
            const useThreshold = enableThresholdCheckbox.checked;
            const thresholdVal = parseInt(thresholdSlider.value, 10);
            const useGradient = enableGradientCheckbox.checked;
            const gradientThresholdVal = parseInt(gradientThresholdSlider.value, 10);

            try {
                // Krok 1: Predspracovanie s aktuálnymi nastaveniami
                const processedImageDataUrl = await preprocessImage(imageData, useThreshold, thresholdVal, useGradient, gradientThresholdVal);

                // Krok 2: OCR
                const { data: { text } } = await Tesseract.recognize(
                    processedImageDataUrl,
                    'slk+eng',
                    { /* logger: m => console.log(m) */ }
                );

                textContent.textContent = text || '(žiadny text nerozpoznaný)';

                // Krok 3: Hľadanie a korekcia čísla žiadosti
                const separatorPattern = '\\s*(?:[-–—‐‑]|\\s+)\\s*';
                const pattern = new RegExp(
                    `([A-Z]{2})${separatorPattern}([A-Z0-9]{4,5})${separatorPattern}([0-9O]{6})${separatorPattern}([A-Z0-9]{3})`,
                    'ig'
                );
                let match;
                let correctedMatches = [];
                while ((match = pattern.exec(text)) !== null) {
                    let part1 = match[1].toUpperCase();
                    let part2 = match[2].toUpperCase().replace(/0/g, 'O');
                    let part3 = match[3].toUpperCase().replace(/O/g, '0');
                    let part4 = match[4].toUpperCase();
                    let corrected = `${part1}-${part2}-${part3}-${part4}`;
                    correctedMatches.push(corrected);
                }
                if (correctedMatches.length > 0) {
                    matchesContent.innerHTML =
                        '<span class="ocr-matches">Nájdené potenciálne čísla (kliknite pre vloženie):</span><br>' +
                        correctedMatches.map(cm => `<span class="ocr-matches" style="cursor:pointer; text-decoration: underline;" onclick="setRequestNumber('${cm}')">${cm}</span>`).join('<br>');
                    requestNumberInput.value = correctedMatches[0]; // Auto-vyplnenie
                } else {
                    matchesContent.innerHTML = 'Neboli nájdené žiadne zhody pre formát čísla žiadosti.';
                }

            } catch (err) {
                console.error("Chyba pri OCR alebo predspracovaní:", err);
                errorContent.textContent = `Chyba: ${err.message || err}`;
                errorContainer.style.display = 'block';
            } finally {
                loadingIndicator.style.display = 'none';
            }
        }

        // Pomocná funkcia na vloženie čísla do inputu
        function setRequestNumber(reqNum) {
             requestNumberInput.value = reqNum;
        }

    </script>
</body>
</html>