<?php
// generate_hashes.php
// Tento skript vygeneruje SQL príkazy na aktualizáciu hesiel v tabuľke users.
// Uložte tento súbor a spustite ho (napr. cez prehliadač alebo príkazový riadok: php generate_hashes.php).
// Výstup skopírujte a vložte do SQL konzoly (napr. v phpMyAdmin).

header('Content-Type: text/plain; charset=utf-8'); // Zabezpečí správne zobrazenie výstupu

echo "-- SQL UPDATE commands to set hashed passwords for users --\n";
echo "-- Copy and paste the output below into your SQL client --\n\n";
echo "START TRANSACTION;\n\n"; // Použijeme transakciu pre istotu

// Zoznam používateľov a ich pôvodných hesiel
$users = [
    ['username' => 'bohus', 'password' => 'heslo1'],
    ['username' => 'radis', 'password' => 'heslo2'],
    ['username' => 'dominika', 'password' => 'heslo3'],
    ['username' => 'mata', 'password' => 'heslo4'],
    ['username' => 'linda', 'password' => 'heslo5'],
    ['username' => 'mirka', 'password' => 'heslo6'],
    ['username' => 'lukas', 'password' => 'heslo7'],
    ['username' => 'jankam', 'password' => 'heslo8'],
    ['username' => 'oto', 'password' => 'heslo9'],
    ['username' => 'darinka', 'password' => 'heslo10'],
    ['username' => 'janka', 'password' => 'heslo11'], // Používam 'janka' ako username z INSERT príkazu
    ['username' => 'marosko', 'password' => 'heslo12'],
];

// Načítanie PDO pre escapovanie reťazcov (aj keď pri hashoch by nemal byť problém)
require_once 'config.php';
$pdo = getDbConnection();

foreach ($users as $user) {
    $username = $user['username'];
    $plainPassword = $user['password'];

    // Vygenerovanie hashu
    $hashedPassword = password_hash($plainPassword, PASSWORD_DEFAULT);

    // Príprava SQL príkazu s escapovaním pre istotu
    // Použitie quote je bezpečnejšie ako addslashes pre SQL
    $sql = sprintf("UPDATE `users` SET `password` = %s WHERE `username` = %s;",
           $pdo->quote($hashedPassword),
           $pdo->quote($username)
    );
    echo $sql . "\n";
}

echo "\nCOMMIT;\n"; // Ukončenie transakcie
echo "\n-- End of commands --\n";
?>