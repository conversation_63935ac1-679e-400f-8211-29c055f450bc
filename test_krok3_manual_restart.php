<?php
// test_krok3_manual_restart.php - Test pre Krok 3: Úprava funkcie manuálneho reštartu

require_once 'config.php';
require_once 'functions.php';

try {
    echo "=== Test Krok 3: Úprava funkcie manuálneho reštartu ===\n\n";
    
    $testOddelenieId = 1; // Predpokladáme, že existuje oddelenie s ID 1
    $testDate1 = new DateTimeImmutable('2025-07-01'); // Prvý deň
    $testDate2 = new DateTimeImmutable('2025-07-02'); // Druhý deň
    $testDate3 = new DateTimeImmutable('2025-07-03'); // Tretí de<PERSON>
    
    // Príprava: Uložíme existujúce forced_start_order pre druhý a tretí deň (aby sme otestovali, že sa prepíšu)
    echo "1. Príprava: Uloženie existujúcich forced_start_order pre testovanie prepisovania...\n";
    $existingForcedOrder2 = '5'; // Pre druhý deň (má sa prepísať na NULL)
    $existingForcedOrder3 = '7'; // Pre tretí de<PERSON> (má sa prepísať na NULL)

    upsertAssignmentMetadataMeta($testDate2, 'test_sig_2', null, null, $testOddelenieId, $pdo, $existingForcedOrder2);
    upsertAssignmentMetadataMeta($testDate3, 'test_sig_3', null, null, $testOddelenieId, $pdo, $existingForcedOrder3);
    echo "   ✓ Existujúce forced_start_order uložené (budú prepísané na automatický cyklus)\n\n";
    
    echo "2. Test recalculateAssignmentsWithForcedStart...\n";
    $forcedStartOrder = 3; // Nové vynútené poradie pre prvý deň
    
    // Spustíme manuálny reštart od prvého dňa
    recalculateAssignmentsWithForcedStart($testDate1, $forcedStartOrder, $testOddelenieId, $pdo);
    echo "   ✓ Manuálny reštart dokončený\n\n";
    
    echo "3. Overenie uloženia forced_start_order pre prvý deň...\n";
    $meta1 = getAssignmentMetadataMeta($testDate1, $testOddelenieId, $pdo);
    if ($meta1 && $meta1['forced_start_order'] === (string)$forcedStartOrder) {
        echo "   ✓ forced_start_order správne uložené pre prvý deň: " . $meta1['forced_start_order'] . "\n";
    } else {
        echo "   ✗ CHYBA: forced_start_order nebolo správne uložené pre prvý deň!\n";
        echo "     Očakávané: " . (string)$forcedStartOrder . "\n";
        echo "     Skutočné: " . ($meta1['forced_start_order'] ?? 'NULL') . "\n";
    }
    
    echo "\n4. Overenie, že druhý deň má nový automatický cyklus (bez forced_start_order)...\n";
    $meta2 = getAssignmentMetadataMeta($testDate2, $testOddelenieId, $pdo);
    if ($meta2 && $meta2['forced_start_order'] === null) {
        echo "   ✓ Druhý deň má nový automatický cyklus (forced_start_order: NULL)\n";
    } else {
        echo "   ✗ CHYBA: Druhý deň nemá správne nastavený automatický cyklus!\n";
        echo "     Očakávané: NULL (automatický cyklus)\n";
        echo "     Skutočné: " . ($meta2['forced_start_order'] ?? 'NULL') . "\n";
    }

    echo "\n5. Overenie, že tretí deň má nový automatický cyklus (bez forced_start_order)...\n";
    $meta3 = getAssignmentMetadataMeta($testDate3, $testOddelenieId, $pdo);
    if ($meta3 && $meta3['forced_start_order'] === null) {
        echo "   ✓ Tretí deň má nový automatický cyklus (forced_start_order: NULL)\n";
    } else {
        echo "   ✗ CHYBA: Tretí deň nemá správne nastavený automatický cyklus!\n";
        echo "     Očakávané: NULL (automatický cyklus)\n";
        echo "     Skutočné: " . ($meta3['forced_start_order'] ?? 'NULL') . "\n";
    }
    
    echo "\n6. Test scenára bez existujúcich forced_start_order...\n";
    $testDate4 = new DateTimeImmutable('2025-07-08');
    $testDate5 = new DateTimeImmutable('2025-07-09');
    $forcedStartOrder4 = 2;
    
    // Spustíme manuálny reštart od štvrtého dňa (bez existujúcich forced_start_order)
    recalculateAssignmentsWithForcedStart($testDate4, $forcedStartOrder4, $testOddelenieId, $pdo);
    
    $meta4 = getAssignmentMetadataMeta($testDate4, $testOddelenieId, $pdo);
    $meta5 = getAssignmentMetadataMeta($testDate5, $testOddelenieId, $pdo);
    
    if ($meta4 && $meta4['forced_start_order'] === (string)$forcedStartOrder4) {
        echo "   ✓ forced_start_order správne uložené pre štvrtý deň: " . $meta4['forced_start_order'] . "\n";
    } else {
        echo "   ✗ CHYBA: forced_start_order nebolo správne uložené pre štvrtý deň!\n";
    }
    
    if ($meta5 && $meta5['forced_start_order'] === null) {
        echo "   ✓ Piaty deň nemá forced_start_order (automatický výpočet)\n";
    } else {
        echo "   ✗ CHYBA: Piaty deň má neočakávané forced_start_order: " . ($meta5['forced_start_order'] ?? 'NULL') . "\n";
    }
    
    // Vyčistenie testovacích dát
    echo "\n7. Čistenie testovacích dát...\n";
    $testDates = [$testDate1, $testDate2, $testDate3, $testDate4, $testDate5];
    foreach ($testDates as $date) {
        $cleanupStmt = $pdo->prepare("DELETE FROM assignment_metadata WHERE assignment_date = ? AND oddelenie_id = ?");
        $cleanupStmt->execute([$date->format('Y-m-d'), $testOddelenieId]);
        
        $cleanupAssignments = $pdo->prepare("DELETE a FROM assignments a JOIN users u ON a.user_id = u.id WHERE a.assignment_date = ? AND u.oddelenie_id = ?");
        $cleanupAssignments->execute([$date->format('Y-m-d'), $testOddelenieId]);
    }
    echo "   ✓ Testovacie dáta vyčistené\n";
    
    echo "\n=== Test Krok 3 dokončený úspešne! ===\n";
    
} catch (Exception $e) {
    echo "CHYBA pri teste: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
