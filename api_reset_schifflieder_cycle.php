<?php
// Súbor: api_reset_schifflieder_cycle.php
require_once 'config.php';
require_once 'functions.php'; // Predpokladá, že funkcia getUserById() a countWorkingDaysBetween() sú tu definované

header('Content-Type: application/json');
$pdo = getDbConnection();

// Overenie prihlásenia - toto zostáva, zabezpečí, že používateľ musí byť prihlásený
requireLogin(); 

// $loggedUser sa stále môže načítať, ak by ste ho potrebovali na iné účely,
// ale pre oprávnenia ho už nebudeme používať takto.
// $loggedUser = null;
// if (isset($_SESSION['user_id'])) {
//     $loggedUser = getUserById($_SESSION['user_id'], $pdo);
// }

// --- ODSTRÁNENÉ ŠPECIFICKÉ OVERENIE OPRÁVNENÍ PODĽA ROLY ---
// Akcia je teraz povolená pre každého prihláseného používateľa.
// --- KONIEC ODSTRÁNENIA OVERENIA OPRÁVNENÍ ---

try {
    $pdo->beginTransaction();

    // 1. Načítame užívateľov 'radis' a 'dominika'
    $bruder = null; $domi = null;
    $stmtAllSchiffUsers = $pdo->query("SELECT id, username, name FROM users WHERE username IN ('radis', 'dominika') AND is_active = 1");
    while ($u = $stmtAllSchiffUsers->fetch(PDO::FETCH_ASSOC)) {
        if ($u['username'] === 'radis') $bruder = $u;
        if ($u['username'] === 'dominika') $domi = $u;
    }

    if (!$bruder || !$domi) {
        throw new Exception('Nepodarilo sa načítať oboch Schiffliederov (radis, dominika) z DB.');
    }

    // 2. Získame aktuálne referenčné dáta pre Schiffliederov
    $stmtSchiffRef = $pdo->prepare("SELECT reference_date, starts_with_radis FROM schifflieder_reference WHERE id = 1");
    $stmtSchiffRef->execute();
    $schiffRefData = $stmtSchiffRef->fetch(PDO::FETCH_ASSOC);

    if (!$schiffRefData) {
        $defaultRefDate = '2025-01-01'; 
        $defaultStartsRadis = true; 
        $insertRefStmt = $pdo->prepare("INSERT INTO schifflieder_reference (id, reference_date, starts_with_radis) VALUES (1, ?, ?)");
        $insertRefStmt->execute([$defaultRefDate, $defaultStartsRadis]);
        $refDate = new DateTimeImmutable($defaultRefDate);
        $startsRadis = $defaultStartsRadis;
        error_log("Vytvorený predvolený záznam v schifflieder_reference.");
    } else {
        $refDate = new DateTimeImmutable($schiffRefData['reference_date']);
        $startsRadis = (bool)$schiffRefData['starts_with_radis'];
    }
    
    // 3. Vypočítame, kto je priradený DNES podľa STAREJ logiky
    $today = new DateTimeImmutable('today');
    $wDiff = countWorkingDaysBetween($refDate, $today, $pdo); 

    $isRadisTurnToday = false;
    if ($startsRadis) { 
        $isRadisTurnToday = ($wDiff % 2 === 0); 
    } else { 
        $isRadisTurnToday = ($wDiff % 2 !== 0); 
    }
    
    // 4. Určíme, kto začne NOVÝ cyklus (ten druhý)
    $newCycleStartsWithRadis = !$isRadisTurnToday;
    
    // 5. Pripravíme nové dáta pre DB
    $new_ref_date = $today->format('Y-m-d');
    
    // 6. Aktualizujeme DB (tabuľku schifflieder_reference)
    $updateStmt = $pdo->prepare("UPDATE schifflieder_reference SET reference_date = ?, starts_with_radis = ? WHERE id = 1");
    $updateStmt->execute([$new_ref_date, $newCycleStartsWithRadis]);

    // 7. Vymažeme budúce metadáta (a pre dnešok) pre VŠETKY oddelenia
    $deleteMetaStmt = $pdo->prepare("DELETE FROM assignment_metadata WHERE assignment_date >= ?");
    $deleteMetaStmt->execute([$today->format('Y-m-d')]);

    $pdo->commit();
    
    echo json_encode(['success' => true, 'message' => 'Cyklus Schiffliederov bol úspešne resetovaný.']);

} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    http_response_code(500); 
    error_log("API Chyba pri resetovaní cyklu Schiffliederov: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Chyba pri resetovaní cyklu Schiffliederov: ' . $e->getMessage()]);
}
?>
