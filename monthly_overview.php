<?php
require_once 'config.php';
require_once 'functions.php';

requireLogin();
$pdo            = getDbConnection();
$loggedInUserId = $_SESSION['user_id'] ?? null;

// --- KĽÚČOVÁ ZMENA: Načítanie aktívneho oddelenia ---
// Prioritu má oddelenie zvolené v dropdowne na dashboarde, potom defaultné oddelenie používateľa.
$activeOddelenieId = $_SESSION['selected_oddelenie_id'] ?? $_SESSION['oddelenie_id'] ?? null;
$activeOddelenieNazov = $_SESSION['selected_oddelenie_nazov'] ?? $_SESSION['oddelenie_nazov'] ?? null;

// Kontrola, či je oddelenie zvolené
if ($activeOddelenieId === null) {
    $_SESSION['message'] = "Prosím, vyberte oddelenie na Dashboarde pre zobrazenie mesačného prehľadu absencií.";
    header("Location: dashboard.php");
    exit;
}


$loggedUser     = getUserById($loggedInUserId, $pdo);

// --- DÁTUMY ---
$requested = $_GET['date'] ?? date('Y-m-d');
try { $ref = new DateTimeImmutable($requested); }
catch (Exception $e) { $ref = new DateTimeImmutable(); }

$startOfMonth     = $ref->modify('first day of this month');
$endOfMonth       = $ref->modify('last day of this month');
$prevMonthDateStr = $startOfMonth->modify('-1 month')->format('Y-m-d');
$nextMonthDateStr = $startOfMonth->modify('+1 month')->format('Y-m-d');

$skMonths = [1=>'Január',2=>'Február',3=>'Marec',4=>'Apríl',5=>'Máj',6=>'Jún',7=>'Júl',8=>'August',9=>'September',10=>'Október',11=>'November',12=>'December'];
$displayMonthStr = $skMonths[(int)$ref->format('n')] . ' ' . $ref->format('Y') . ' (Oddelenie: ' . htmlspecialchars($activeOddelenieNazov) . ')';
$today_date_str_for_compare = date('Y-m-d');


// --- POUŽÍVATELIA & ABSENCIE ---
// Načítame používateľov len pre aktuálne oddelenie
$usersForDepartment = [];
// Kontrola $activeOddelenieId už prebehla vyššie, takže môžeme predpokladať, že je nastavené.
// Ak by nebolo, skript by sa ukončil presmerovaním.
$usersForDepartment = getAllUsers($pdo, $activeOddelenieId);

// Ak by sme chceli zobraziť chybovú hlášku, ak sa nepodarí načítať používateľov pre zvolené oddelenie:
// if (empty($usersForDepartment) && $activeOddelenieId) { error_log("Monthly Overview: No users found for department ID: " . $activeOddelenieId); }

$roleOrd  = ['veduci'=>1,'schifflieder'=>2,'skladnik'=>3,'strojnik'=>4];
usort($usersForDepartment, function($a,$b)use($roleOrd){ $pa=$roleOrd[$a['role1']]??99; $pb=$roleOrd[$b['role1']]??99; if($pa!==$pb) return $pa-$pb; return strcmp($a['name'],$b['name']); });
$orderedUsers = $usersForDepartment; // Použijeme vyfiltrovaných a zoradených používateľov

$short = ['Celý deň'=>'D', 'Ráno'=>'R', 'Poobede'=>'P', 'Iné'=>'X'];
$allAbs = [];

// Upravíme dopyt na absencie tak, aby načítal len absencie používateľov z daného oddelenia
$stmt = $pdo->prepare("SELECT a.user_id, a.start_date, a.end_date, a.absence_type
                       FROM absences a
                       JOIN users u ON a.user_id = u.id
                       WHERE u.oddelenie_id = :oddelenie_id AND a.start_date <= :end_date AND a.end_date >= :start_date");
$stmt->execute([':oddelenie_id' => $activeOddelenieId, ':end_date' => $endOfMonth->format('Y-m-d'), ':start_date' => $startOfMonth->format('Y-m-d')]);
while($r=$stmt->fetch(PDO::FETCH_ASSOC)){ $uid = (int)$r['user_id']; $c = $short[$r['absence_type']] ?? 'X'; $from = new DateTimeImmutable($r['start_date']); $to = new DateTimeImmutable($r['end_date']); for($d=$from; $d<=$to; $d=$d->modify('+1 day')){ $ds = $d->format('Y-m-d'); if($ds >= $startOfMonth->format('Y-m-d') && $ds <= $endOfMonth->format('Y-m-d')){ $allAbs[$ds][$uid] = $c; } } }
$canEditAll = in_array($loggedUser['role1'] ?? '', ['veduci','schifflieder'], true);
?>
<!DOCTYPE html>
<html lang="sk">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Mesačný Prehľad Absencií - <?php echo htmlspecialchars($activeOddelenieNazov); ?></title>
<link rel="stylesheet" href="style.css">
</head>
<body>
<div class="container container-wide"> <div class="header">Mesačný Prehľad Absencií</div>
  <div class="navigation">
    <a href="monthly_overview.php?date=<?=$prevMonthDateStr?>">&laquo; Predošlý mesiac</a>
    <span><?=htmlspecialchars($displayMonthStr)?></span>
    <a href="monthly_overview.php?date=<?=$nextMonthDateStr?>">Nasledujúci mesiac &raquo;</a>
  </div>

  <div class="table-responsive">
      <table class="overview-table"> <thead>
          <tr>
            <th class="date-cell"></th>
            <?php foreach ($orderedUsers as $user): ?>
                <?php
                $headerClass = ($user['id'] === $loggedInUserId) ? 'highlight-user vertical-header' : 'vertical-header';
                $displayName = $user['name'];
                
                // Špeciálne prípady pre Jany
                if ($displayName === 'Mrázková Jana') {
                    $displayName = 'Janka M.';
                } elseif ($displayName === 'Andrášková Jana') {
                    $displayName = 'Janka A.';
                } else {
                    // Pôvodná logika pre ostatných
                    $surname = explode(' ', $displayName);
                    $displayName = end($surname);
                }
                ?>
                <th class="<?php echo trim($headerClass); ?>">
                    <div><?php echo htmlspecialchars($displayName); ?></div>
                </th>
            <?php endforeach; ?>
          </tr>
        </thead>
        <tbody>
          <?php
          $wd=['Po','Ut','St','Št','Pi','So','Ne'];
          for($d=$startOfMonth; $d<=$endOfMonth; $d=$d->modify('+1 day')):
            $ds = $d->format('Y-m-d');
            $work = isWorkingDay($d,$pdo);
            $isToday = ($ds === $today_date_str_for_compare);
            $rowClass = $work ? '' : 'non-working-day';
            if ($isToday) { $rowClass .= ' highlight-today'; }
            echo '<tr class="'.trim($rowClass).'">';
            echo '<td class="date-cell">' . htmlspecialchars($d->format('j')) . '</td>';
            foreach($orderedUsers as $u){
                $uid = $u['id'];
                $isCurrentUserColumn = ($uid === $loggedInUserId);
                $isClickable = ($work && ($canEditAll || $uid === $loggedInUserId));
                $cellClasses = [];

                // Určenie highlight tried
                if ($isToday && $isCurrentUserColumn) { $cellClasses[] = 'highlight-intersection'; }
                elseif ($isCurrentUserColumn) { $cellClasses[] = 'highlight-user'; }

                // Pridanie triedy podľa obsahu absencie
                if (isset($allAbs[$ds][$uid])) { $cellClasses[] = 'status-absence'; }

                // Pridanie triedy pre klikateľnosť (pre JS)
                if ($isClickable) {
                    $cellClasses[] = 'cell-clickable';
                }

                // Spojíme všetky triedy dokopy
                $finalCellClass = implode(' ', array_unique($cellClasses));
                $attributes = $isClickable ? 'data-date="' . $ds . '" data-uid="' . $uid . '"' : '';
                $content = $allAbs[$ds][$uid] ?? ($work ? '' : '-'); // Zobrazí '-' len ak je nepracovný deň a nie je absencia
                echo "<td class='" . trim($finalCellClass) . "' " . $attributes . ">" . htmlspecialchars($content) . "</td>";
            }
            echo '</tr>';
          endfor;
          ?>
        </tbody>
      </table>
  </div>

  <div class="buttons">
    <button onclick="location.href='dashboard.php'" class="back-button">Späť na Dashboard</button>
  </div>
</div>

<script>

</script>
</body>
</html>
