<?php
require_once 'config.php';
require_once 'functions.php';

requireLogin();
$pdo            = getDbConnection();
$loggedInUserId = $_SESSION['user_id'] ?? null;

// --- KĽÚČOVÁ ZMENA: Načítanie aktívneho oddelenia ---
// Prioritu má oddelenie zvolené v dropdowne na dashboarde, potom defaultné oddelenie používateľa.
$activeOddelenieId = $_SESSION['selected_oddelenie_id'] ?? $_SESSION['oddelenie_id'] ?? null;
$activeOddelenieNazov = $_SESSION['selected_oddelenie_nazov'] ?? $_SESSION['oddelenie_nazov'] ?? null;

// Kontrola, či je oddelenie zvolené
if ($activeOddelenieId === null) {
    $_SESSION['message'] = "Prosím, vyberte oddelenie na Dashboarde pre zobrazenie mesačného prehľadu absencií.";
    header("Location: dashboard.php");
    exit;
}


$loggedUser     = getUserById($loggedInUserId, $pdo);

// --- DÁTUMY ---
$requested = $_GET['date'] ?? date('Y-m-d');
try { $ref = new DateTimeImmutable($requested); }
catch (Exception $e) { $ref = new DateTimeImmutable(); }

$startOfMonth     = $ref->modify('first day of this month');
$endOfMonth       = $ref->modify('last day of this month');
$prevMonthDateStr = $startOfMonth->modify('-1 month')->format('Y-m-d');
$nextMonthDateStr = $startOfMonth->modify('+1 month')->format('Y-m-d');

$skMonths = [1=>'Január',2=>'Február',3=>'Marec',4=>'Apríl',5=>'Máj',6=>'Jún',7=>'Júl',8=>'August',9=>'September',10=>'Október',11=>'November',12=>'December'];
$displayMonthStr = $skMonths[(int)$ref->format('n')] . ' ' . $ref->format('Y') . ' (Oddelenie: ' . htmlspecialchars($activeOddelenieNazov) . ')';
$today_date_str_for_compare = date('Y-m-d');


// --- POUŽÍVATELIA & ABSENCIE ---
// Načítame používateľov len pre aktuálne oddelenie
$usersForDepartment = [];
// Kontrola $activeOddelenieId už prebehla vyššie, takže môžeme predpokladať, že je nastavené.
// Ak by nebolo, skript by sa ukončil presmerovaním.
$usersForDepartment = getAllUsers($pdo, $activeOddelenieId);

// Ak by sme chceli zobraziť chybovú hlášku, ak sa nepodarí načítať používateľov pre zvolené oddelenie:
// if (empty($usersForDepartment) && $activeOddelenieId) { error_log("Monthly Overview: No users found for department ID: " . $activeOddelenieId); }

$roleOrd  = ['veduci'=>1,'schifflieder'=>2,'skladnik'=>3,'strojnik'=>4];
usort($usersForDepartment, function($a,$b)use($roleOrd){ $pa=$roleOrd[$a['role1']]??99; $pb=$roleOrd[$b['role1']]??99; if($pa!==$pb) return $pa-$pb; return strcmp($a['name'],$b['name']); });
$orderedUsers = $usersForDepartment; // Použijeme vyfiltrovaných a zoradených používateľov

$short = ['Celý deň'=>'D', 'Ráno'=>'R', 'Poobede'=>'P', 'Iné'=>'X'];
$allAbs = [];

// Upravíme dopyt na absencie tak, aby načítal len absencie používateľov z daného oddelenia
$stmt = $pdo->prepare("SELECT a.user_id, a.start_date, a.end_date, a.absence_type
                       FROM absences a
                       JOIN users u ON a.user_id = u.id
                       WHERE u.oddelenie_id = :oddelenie_id AND a.start_date <= :end_date AND a.end_date >= :start_date");
$stmt->execute([':oddelenie_id' => $activeOddelenieId, ':end_date' => $endOfMonth->format('Y-m-d'), ':start_date' => $startOfMonth->format('Y-m-d')]);
while($r=$stmt->fetch(PDO::FETCH_ASSOC)){ $uid = (int)$r['user_id']; $c = $short[$r['absence_type']] ?? 'X'; $from = new DateTimeImmutable($r['start_date']); $to = new DateTimeImmutable($r['end_date']); for($d=$from; $d<=$to; $d=$d->modify('+1 day')){ $ds = $d->format('Y-m-d'); if($ds >= $startOfMonth->format('Y-m-d') && $ds <= $endOfMonth->format('Y-m-d')){ $allAbs[$ds][$uid] = $c; } } }
$canEditAll = in_array($loggedUser['role1'] ?? '', ['veduci','schifflieder'], true);

// Definícia informácií pre zobrazenie typov absencií (rovnaká ako v dashboard.php)
$absenceDisplayInfo = [
    'Celý deň'    => ['shortcut' => 'D', 'color' => '#8bc34a', 'class' => 'absence-d'],
    'Ráno'        => ['shortcut' => 'R', 'color' => '#2196f3', 'class' => 'absence-r'],
    'Poobede'     => ['shortcut' => 'P', 'color' => '#ff9800', 'class' => 'absence-p'],
    'Iné'         => ['shortcut' => 'X', 'color' => '#607d8b', 'class' => 'absence-x']
];
?>
<!DOCTYPE html>
<html lang="sk">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Mesačný Prehľad Absencií - <?php echo htmlspecialchars($activeOddelenieNazov); ?></title>
<link rel="stylesheet" href="style.css">
<style>
    .absence-dropdown {
        background-color: #333; border: 1px solid #555; border-radius: 5px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3); z-index: 10000;
        min-width: 180px; max-width: 250px; padding: 5px 0; position: absolute;
    }
    .absence-option { padding: 8px 15px; cursor: pointer; color: #ddd; transition: background-color 0.2s; }
    .absence-option:hover { background-color: #444; color: #fff; }
    .absence-close {
        position: absolute; top: 5px; right: 5px; width: 20px; height: 20px;
        line-height: 20px; text-align: center; cursor: pointer; color: #aaa;
        font-size: 14px; border-radius: 50%;
    }
    .absence-close:hover { background-color: #555; color: #fff; }
</style>
</head>
<body>
<div class="container container-wide"> <div class="header">Mesačný Prehľad Absencií</div>
  <div class="navigation">
    <a href="monthly_overview.php?date=<?=$prevMonthDateStr?>">&laquo; Predošlý mesiac</a>
    <span><?=htmlspecialchars($displayMonthStr)?></span>
    <a href="monthly_overview.php?date=<?=$nextMonthDateStr?>">Nasledujúci mesiac &raquo;</a>
  </div>

  <div class="table-responsive">
      <table class="overview-table"> <thead>
          <tr>
            <th class="date-cell"></th>
            <?php foreach ($orderedUsers as $user): ?>
                <?php
                $headerClass = ($user['id'] === $loggedInUserId) ? 'highlight-user vertical-header' : 'vertical-header';
                $displayName = $user['name'];
                
                // Špeciálne prípady pre Jany
                if ($displayName === 'Mrázková Jana') {
                    $displayName = 'Janka M.';
                } elseif ($displayName === 'Andrášková Jana') {
                    $displayName = 'Janka A.';
                } else {
                    // Pôvodná logika pre ostatných
                    $surname = explode(' ', $displayName);
                    $displayName = end($surname);
                }
                ?>
                <th class="<?php echo trim($headerClass); ?>">
                    <div><?php echo htmlspecialchars($displayName); ?></div>
                </th>
            <?php endforeach; ?>
          </tr>
        </thead>
        <tbody>
          <?php
          $wd=['Po','Ut','St','Št','Pi','So','Ne'];
          for($d=$startOfMonth; $d<=$endOfMonth; $d=$d->modify('+1 day')):
            $ds = $d->format('Y-m-d');
            $work = isWorkingDay($d,$pdo);
            $isToday = ($ds === $today_date_str_for_compare);
            $rowClass = $work ? '' : 'non-working-day';
            if ($isToday) { $rowClass .= ' highlight-today'; }
            echo '<tr class="'.trim($rowClass).'">';
            echo '<td class="date-cell">' . htmlspecialchars($d->format('j')) . '</td>';
            foreach($orderedUsers as $u){
                $uid = $u['id'];
                $isCurrentUserColumn = ($uid === $loggedInUserId);
                $isClickable = ($work && ($canEditAll || $uid === $loggedInUserId));
                $cellClasses = [];

                // Určenie highlight tried
                if ($isToday && $isCurrentUserColumn) { $cellClasses[] = 'highlight-intersection'; }
                elseif ($isCurrentUserColumn) { $cellClasses[] = 'highlight-user'; }

                // Pridanie triedy podľa obsahu absencie
                if (isset($allAbs[$ds][$uid])) { $cellClasses[] = 'status-absence'; }

                // Pridanie triedy pre klikateľnosť (pre JS)
                if ($isClickable) {
                    $cellClasses[] = 'cell-clickable';
                }

                // Spojíme všetky triedy dokopy
                $finalCellClass = implode(' ', array_unique($cellClasses));
                $attributes = $isClickable ? 'data-date="' . $ds . '" data-uid="' . $uid . '" data-username="' . htmlspecialchars($u['name']) . '"' : '';
                $content = $allAbs[$ds][$uid] ?? ($work ? '' : '-'); // Zobrazí '-' len ak je nepracovný deň a nie je absencia
                echo "<td class='" . trim($finalCellClass) . "' " . $attributes . ">" . htmlspecialchars($content) . "</td>";
            }
            echo '</tr>';
          endfor;
          ?>
        </tbody>
      </table>
  </div>

  <div class="buttons">
    <button onclick="location.href='dashboard.php'" class="back-button">Späť na Dashboard</button>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let currentAbsenceDropdown = null;

    // Funkcia na zatvorenie dropdownu
    function closeAbsenceDropdown() {
        if (currentAbsenceDropdown) {
            currentAbsenceDropdown.remove();
            currentAbsenceDropdown = null;
        }
    }

    // Funkcia na zatvorenie dropdownu pri kliku mimo neho
    function closeAbsenceDropdownOnClickOutside(event) {
        if (currentAbsenceDropdown && !currentAbsenceDropdown.contains(event.target)) {
            closeAbsenceDropdown();
            document.removeEventListener('click', closeAbsenceDropdownOnClickOutside);
        }
    }

    // Event listener pre kliknutie na bunky
    document.addEventListener('click', function(event) {
        const clickedCell = event.target.closest('td.cell-clickable');

        if (clickedCell) {
            event.stopPropagation();

            const date = clickedCell.dataset.date;
            const userId = clickedCell.dataset.uid;
            const userName = clickedCell.dataset.username;
            const currentContent = clickedCell.textContent.trim();

            // Ak už má absenciu, ponúkni možnosť zrušiť
            if (currentContent && currentContent !== '-') {
                if (confirm(`Chcete zrušiť absenciu pre používateľa ${userName} na deň ${date}?`)) {
                    removeAbsence(date, userId, userName);
                }
                return;
            }

            // Zobrazenie dropdownu s typmi absencií
            showAbsenceDropdown(date, userId, userName, clickedCell);
        }
    });

    // Funkcia na zobrazenie dropdownu s typmi absencií
    function showAbsenceDropdown(date, userId, userName, targetElement) {
        closeAbsenceDropdown();

        const dropdown = document.createElement('div');
        dropdown.className = 'absence-dropdown';

        const absenceTypes = ['Celý deň', 'Ráno', 'Poobede', 'Iné'];
        absenceTypes.forEach(type => {
            const option = document.createElement('div');
            option.className = 'absence-option';
            option.textContent = type;
            option.onclick = (e) => {
                e.stopPropagation();
                submitAbsence(date, userId, type, userName);
                closeAbsenceDropdown();
            };
            dropdown.appendChild(option);
        });

        const closeButton = document.createElement('div');
        closeButton.className = 'absence-close';
        closeButton.textContent = '×';
        closeButton.onclick = (e) => {
            e.stopPropagation();
            closeAbsenceDropdown();
        };
        dropdown.appendChild(closeButton);

        document.body.appendChild(dropdown);
        currentAbsenceDropdown = dropdown;

        // Pozicovanie dropdownu
        const rect = targetElement.getBoundingClientRect();
        dropdown.style.position = 'absolute';
        dropdown.style.top = `${rect.bottom + window.scrollY}px`;
        dropdown.style.left = `${rect.left + window.scrollX}px`;

        // Pridanie event listenera pre zatvorenie pri kliku mimo
        setTimeout(() => {
            document.addEventListener('click', closeAbsenceDropdownOnClickOutside);
        }, 0);
    }

    // Funkcia na odoslanie absencie
    function submitAbsence(date, userId, absenceType, userName) {
        const formData = new FormData();
        formData.append('action', 'add');
        formData.append('user_id', userId);
        formData.append('start_date', date);
        formData.append('end_date', date);
        formData.append('absence_type', absenceType);

        fetch('absences.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reload stránky pre zobrazenie zmien
                window.location.reload();
            } else {
                alert('Chyba pri pridávaní absencie: ' + (data.error || 'Neznáma chyba'));
            }
        })
        .catch(error => {
            console.error('Chyba:', error);
            alert('Nastala chyba pri komunikácii so serverom');
        });
    }

    // Funkcia na zrušenie absencie
    function removeAbsence(date, userId, userName) {
        const formData = new FormData();
        formData.append('date', date);
        formData.append('uid', userId);

        fetch('absences.php?action=delete', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reload stránky pre zobrazenie zmien
                window.location.reload();
            } else {
                alert('Chyba pri odstraňovaní absencie: ' + (data.error || 'Neznáma chyba'));
            }
        })
        .catch(error => {
            console.error('Chyba:', error);
            alert('Nastala chyba pri komunikácii so serverom');
        });
    }
});
</script>
</body>
</html>
