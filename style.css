/* Reset z<PERSON><PERSON><PERSON><PERSON><PERSON> */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  /* Globálne tmavé nastavenia */
  html, body {
    background-color: #121212;
    color: #e0e0e0;
    font-family: 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    font-size: 100%;
    min-height: 100vh;
  }

  /* Odkazy */
  a {
    color: #8ab4f8;
    text-decoration: none; /* Odstránenie podčiarknutia */
  }
  a:hover {
      text-decoration: underline; /* Podčiarknutie pri prejdení my<PERSON> */
  }


  /* Kontajner pre centrálny obsah */
  .container {
    background-color: #1e1e1e;
    margin: 20px auto;
    padding: 20px;
    max-width: 800px; /* <PERSON><PERSON> š<PERSON> kontaj<PERSON> pre dashboard */
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
  }
  /* <PERSON><PERSON><PERSON><PERSON> kont<PERSON> pre prehľady */
  .container-wide {
    max-width: 95%; /* Alebo 98% */
  }


  /* <PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON> */
  .header {
    background-color: #272727;
    color: #ffffff;
    text-align: center;
    padding: 15px;
    font-size: 1.5em;
    border-radius: 5px;
    margin-bottom: 20px;
  }

  /* Štýly pre formuláre */
  form {
    background-color: #1e1e1e;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
  }
  form.inline-form {
      padding: 0;
      background: none;
      margin-bottom: 0;
      border: none;
  }
  form label {
    display: block;
    margin-bottom: 5px;
    color: #cfcfcf;
    font-size: 1em;
  }
  form input[type="text"],
  form input[type="password"],
  form input[type="email"],
  form input[type="date"],
  form input[type="number"],
  form input[type="time"],
  textarea,
  select {
    width: 100%;
    padding: 10px;
    font-size: 1em;
    margin-bottom: 15px;
    background-color: #2a2a2a;
    border: 1px solid #444;
    border-radius: 5px;
    color: #e0e0e0;
  }
  form button {
    width: 100%;
    padding: 12px;
    background-color: #3a3a3a;
    color: #e0e0e0;
    border: none;
    border-radius: 5px;
    font-size: 1.1em;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }
  form button:hover {
    background-color: #555;
  }

  /* Štýl pre chybové a úspešné hlásenia */
  .message {
      padding: 10px 15px;
      margin-bottom: 15px;
      border-radius: 5px;
      text-align: center;
      font-weight: bold;
  }
  .message.success { background-color: #4CAF50; color: white; }
  .message.error { background-color: #f44336; color: white; }
  .message.info { background-color: #2196F3; color: white; }

  /* Navigácia medzi dňami/mesiacmi */
  .navigation {
       text-align: center;
       margin: 20px 0;
       padding: 10px 0;
       border-top: 1px solid #333;
       border-bottom: 1px solid #333;
   }
  .navigation a, .navigation span {
      margin: 0 15px;
      font-size: 1.1em;
      vertical-align: middle;
  }
  .navigation span {
      font-weight: bold;
      color: #fff;
  }

  /* Tlačidlo na generovanie */
  .generate-button-container {
      margin: 25px 0;
      padding: 15px;
      background-color: #272727;
      border-radius: 5px;
      text-align: center;
  }
  .generate-button-container button {
     padding: 12px 25px;
     font-size: 1.1em;
     background-color: #007bff;
     color: white;
     border: none;
     border-radius: 5px;
     cursor: pointer;
     transition: background-color 0.3s ease;
     width: auto;
     display: inline-block;
  }
  .generate-button-container button:hover { background-color: #0056b3; }
  .generate-button-container div { margin-top: 8px; font-size: 0.9em; color: #aaa; }

  /* Sekcie s priradeniami na dashboarde */
  .assignment-section {
      margin-bottom: 30px;
      padding: 20px;
      background-color: #272727;
      border-radius: 8px;
      border: 1px solid #333;
  }
  .assignment-section h3 {
      margin-top: 0;
      margin-bottom: 20px;
      border-bottom: 1px solid #444;
      padding-bottom: 10px;
      font-size: 1.3em;
  }

  /* --- ZJEDNOTENÉ ŠTÝLY PRE TABUĽKY --- */
  .overview-table,
  .monthly-data-table {
      border-collapse: collapse;
      width: 100%;
      /* table-layout: fixed; <-- ODSTRÁNENÉ pre automatickú šírku stĺpcov */
      margin-bottom: 15px;
  }
  .overview-table th,
  .overview-table td,
  .monthly-data-table th,
  .monthly-data-table td{
      border: 1px solid #444;
      padding: 12px 6px;
      text-align: center;
      font-size: 1.19em;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      vertical-align: middle;
      min-width: 40px; /* Zachováme pôvodnú minimálnu šírku */
      /* width: 32px;  Ak by ste chceli pevnú šírku, odkomentujte tento riadok */
  }
  .overview-table th,
  .monthly-data-table th {
      background-color: #3a3a3a;
      color: #e0e0e0;
      font-weight: bold;
      position: sticky;
      top: 0;
      font-size: 1.275em; /* Nové: Zväčšenie písma pre hlavičky */
       /* width: 32px;  Ak by ste chceli pevnú šírku, odkomentujte tento riadok */
      z-index: 5;
      cursor: pointer;
  }

  /* 1. Dátumový stĺpec - ZJEDNOTENÝ */
  .overview-table th.date-cell,
  .overview-table td.date-cell,
  .monthly-data-table th.date-cell,
  .monthly-data-table td.date-cell {
      background: #4a4a4a;
      color: #fff;
      font-weight: bold;
      width: 1%;
      min-width: 30px;
      max-width: 50px;

      padding: 12px 3px;
      white-space: normal;
      font-size: 1.105em;
      text-align: center;
      cursor: default;
      overflow-wrap: break-word;
      word-break: break-all;

      position: sticky;
      left: 0;
      z-index: 6;
  }
  /* Špeciálne pre hlavičku dátumu */
  .overview-table th.date-cell,
  .monthly-data-table th.date-cell {
      z-index: 7;
  }


  /* 2. Prihlásený používateľ - ZJEDNOTENÝ */
  .overview-table th.highlight-user,
  .overview-table td.highlight-user,
  .monthly-data-table th.highlight-user,
  .monthly-data-table td.highlight-user {
      background: #555 !important;
      border-left: 2px solid #aaa !important;
      border-right: 2px solid #aaa !important;

  }

  .overview-table th.highlight-user:not(.collapsed-user),
  .overview-table td.highlight-user:not(.collapsed-user),
  .monthly-data-table th.highlight-user:not(.collapsed-user),
  .monthly-data-table td.highlight-user:not(.collapsed-user) {
      /*width: 20%; <-- ODSTRÁNENÉ */
  }


  /* Bloky .collapsed-user a .expanded boli ODSTRÁNENÉ */


  /* Nepracovné dni - ZJEDNOTENÉ */
  .overview-table tr.non-working-day td,
  .monthly-data-table tr.non-working-day td {
      background-color: #252525;
  }
  /* Dátumové bunky v nepracovné dni */
  .overview-table tr.non-working-day td.date-cell,
  .monthly-data-table tr.non-working-day td.date-cell {
      background-color: #403030;
      color: #ffaaaa;
  }
  /* Zvýraznený používateľ v nepracovný deň */
  .overview-table tr.non-working-day td.highlight-user,
  .monthly-data-table tr.non-working-day td.highlight-user {
      background-color: #4b4b4b !important;
  }


  /* Aktuálny deň (Today) - ZJEDNOTENÉ */
  .overview-table tr.highlight-today td,
  .monthly-data-table tr.highlight-today td {
      background-color: #383838 !important;
  }
  .overview-table tr.highlight-today td.date-cell,
  .monthly-data-table tr.highlight-today td.date-cell {
      background-color: #555555 !important;
      color: #ffffff !important;
      font-weight: bold;
  }
  /* Aktuálny deň a zároveň nepracovný */
  .overview-table tr.highlight-today.non-working-day td,
  .monthly-data-table tr.highlight-today.non-working-day td {
       background-color: #403A3A !important;
  }
  .overview-table tr.highlight-today.non-working-day td.date-cell,
  .monthly-data-table tr.highlight-today.non-working-day td.date-cell {
       background-color: #614E4E !important;
       color: #FFDDE1 !important;
  }

  /* Priesečník aktuálneho dňa a prihláseného používateľa - ZJEDNOTENÝ */
  .overview-table td.highlight-intersection,
  .monthly-data-table td.highlight-intersection {
      background-color:#ff3c00 !important;
      color:#ffffff !important;
      font-weight: bold !important;
      border: 1px solid #ffffff !important;
  }


  /* Špecifické pre monthly_overview */
  .overview-table td.status-absence { font-weight: bold; color: #ffcc80; }
  .overview-table td.cell-clickable { cursor: pointer; }
  .overview-table td.cell-clickable:hover { background: #707070; }

  /* --- NOVÉ ŠTÝLY PRE MANUÁLNE PRIRADENIE --- */
  td.manual-assign-cell {
      cursor: pointer;
      position: relative;
  }
  td.manual-assign-cell:hover {
      background-color: #5a5a5a !important;
      outline: 1px dashed #8ab4f8;
      outline-offset: -1px;
  }
  /* Štýly pre dropdown menu */
  .machine-dropdown {
      padding: 5px 0;
      min-width: 80px;
  }
  .machine-dropdown .dropdown-option {
      padding: 8px 12px;
      color: #e0e0e0;
      cursor: pointer;
      font-size: 0.9em;
      white-space: nowrap;
  }
  .machine-dropdown .dropdown-option:hover {
      background-color: #4a4a4a;
  }
  .machine-dropdown .dropdown-option[data-machine="null"] {
      border-top: 1px solid #555;
      color: #ffcccc;
  }
  .machine-dropdown .dropdown-option[data-machine="null"]:hover {
      background-color: #6f4a4a;
  }
  .machine-dropdown .dropdown-option[data-machine="0"] {
      color: #ffd700;
  }


  /* Štýly špecifické pre dashboard tabuľku (ak existuje inde) */
  .role-label { font-weight: bold; min-width: 120px; color: #bbb; }
  .kontrola-list { list-style: none; padding-left: 0; }
  .kontrola-list li { margin-bottom: 5px; padding: 3px 0; border-bottom: 1px dotted #444; }
  .kontrola-list li:last-child { border-bottom: none; }
  .kontrola-list li.highlight-logged-user { background-color: #dc3545; color: #ffffff !important; font-weight: bold; padding-left: 5px; margin-left: -5px; border-radius: 3px; }

  .assignments-table td:not(.role-label) {
      font-size: 1.2em;
  }
  .assignments-table td.role-label {
      font-size: 1em;
  }
  .assignments-table .highlight-logged-user {
      background-color: #dc3545;
      color: #ffffff !important;
      font-weight: bold;
      font-size: 1.2em;
  }

  /* Tlačidlá pod obsahom */
  .buttons { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #333; }
  .buttons button { margin: 8px; padding: 10px 20px; font-size: 1em; min-width: 150px; background-color: #3a3a3a; color: #e0e0e0; border: none; border-radius: 4px; cursor: pointer; transition: background-color 0.3s ease; width: auto; display: inline-block; }
  .buttons button:hover { background-color: #555; }
  .buttons button.back-button { background-color: #6c757d; }
  .buttons button.back-button:hover { background-color: #5a6268; }

  /* Horizontálne skrolovanie pre tabuľky na menších obrazovkách */
  .table-responsive {
      position: relative;
      overflow: auto;
      height: calc(100vh - 250px);
  }

  /* Nastavenia pre thead a th */
  .overview-table thead,
  .monthly-data-table thead {
      position: sticky;
      top: 0;
      z-index: 10;
      background-color: #121212;
  }

  /* Špeciálne nastavenie pre dátumový stĺpec */
  .overview-table th.date-cell,
  .monthly-data-table th.date-cell {
      position: sticky;
      top: 0;
      left: 0;
      z-index: 11;
      background-color: #4a4a4a;
  }

  /* Pridanie tieňa pod hlavičkou */
  .overview-table thead::after,
  .monthly-data-table thead::after {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      bottom: -5px;
      height: 5px;
      background: linear-gradient(to bottom, rgba(0,0,0,0.2), transparent);
      pointer-events: none;
  }

  /* === Responzívny dizajn === */
  @media only screen and (max-width: 768px) {
    /* Základné zmenšenie písma pre obe tabuľky */
    .overview-table th,
    .overview-table td,
    .monthly-data-table th,
    .monthly-data-table td {
        font-size: 1.105em;
        padding: 9px 4.5px;
    }
     .overview-table th.date-cell,
     .overview-table td.date-cell,
     .monthly-data-table th.date-cell,
     .monthly-data-table td.date-cell {
        font-size: 1.105em;
        padding: 12px 3px;
        width: 1%;
        min-width: 25px;
        max-width: 40px;
     }
      .overview-table th.date-cell,
      .monthly-data-table th.date-cell {
           position: sticky;
           top: 0;
           left: auto;
       }
    /* Bloky pre šírku .highlight-user a .expanded boli ODSTRÁNENÉ */
  }


  @media only screen and (max-width: 600px) {
    .container { margin: 10px; padding: 15px; max-width: calc(100% - 20px); }
    .header { font-size: 1.3em; }
    .navigation a, .navigation span { margin: 0 8px; font-size: 1em; }
    .buttons button { min-width: 120px; font-size: 0.9em; padding: 8px 15px; }

    .overview-table th,
    .overview-table td,
    .monthly-data-table th,
    .monthly-data-table td {
        font-size: 1.02em;
        padding: 6px 3px;
    }
    .overview-table th.date-cell,
    .overview-table td.date-cell,
    .monthly-data-table th.date-cell,
    .monthly-data-table td.date-cell {
        font-size: 0.935em;
        padding: 6px 3px;
        position: relative;
        left: auto;
        width: 1%;
        min-width: 22px;
        max-width: 35px;
    }
     .overview-table th.date-cell,
     .monthly-data-table th.date-cell {
           position: sticky;
           top: 0;
           left: auto;
      }
    /* Bloky pre šírku .highlight-user a .expanded boli ODSTRÁNENÉ */
  }

  @media only screen and (max-width: 480px) {
      .navigation span { display: block; margin-bottom: 5px; }

      /* Cielené zúženie dátových buniek (td) */
      .overview-table td,
      .monthly-data-table td {
          min-width: 14px; /* Extrémne zúženie: obsahová šírka 14px */
      }
      /* Zúženie aj pre dátumové bunky (TH aj TD) a prázdnu TH bunku vľavo hore */
      .overview-table th.date-cell,
      .overview-table td.date-cell,
      .monthly-data-table th.date-cell,
      .monthly-data-table td.date-cell {
          width: 1%;
          min-width: 18px; /* Zúženie pre najmenšie obrazovky */
          max-width: 25px;
      }

      .overview-table th,
      .overview-table td,
      .monthly-data-table th,
      .monthly-data-table td {
          font-size: 0.55em; /* Veľmi malé písmo pre najmenšie obrazovky */
          padding: 4px 1px; /* Ešte menší horizontálny padding (1px na stranu) */
      }
      .overview-table th.date-cell,
      .overview-table td.date-cell,
      .monthly-data-table th.date-cell,
      .monthly-data-table td.date-cell {
          font-size: 0.5em;
          /* padding už je nastavený vyššie na 4px 1px */
          position: relative;
          left: auto;
      }
      .overview-table th.date-cell,
      .monthly-data-table th.date-cell {
          position: sticky;
          top: 0;
          left: auto;
      }
      /* Bloky pre šírku .highlight-user a .expanded boli ODSTRÁNENÉ */
  }

/* Silný selektor pre vynútenie výšky buniek */
.table-responsive .overview-table td,
.table-responsive .overview-table th,
.table-responsive .monthly-data-table td,
.table-responsive .monthly-data-table th {
    height: 34px !important;
    line-height: 34px !important;
    max-height: 34px !important;
    min-height: 34px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    vertical-align: middle !important;
}

/* Aplikujeme rovnaké pravidlá aj na špeciálne bunky */
.table-responsive .overview-table td.date-cell,
.table-responsive .overview-table th.date-cell,
.table-responsive .monthly-data-table td.date-cell,
.table-responsive .monthly-data-table th.date-cell,
.table-responsive .overview-table td.highlight-user,
.table-responsive .overview-table th.highlight-user,
.table-responsive .monthly-data-table td.highlight-user,
.table-responsive .monthly-data-table th.highlight-user {
    height: 34px !important;
    line-height: 34px !important;
    max-height: 34px !important;
    min-height: 34px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    vertical-align: middle !important;
}

/* Štýly pre mesačný kalendár */
.monthly-calendar-section {
    margin-top: 30px;
    margin-bottom: 20px;
    background-color: #2a2a2a;
    border-radius: 5px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    border: 1px solid #3a3a3a;
}

.monthly-calendar-section h3 {
    text-align: center;
    margin-bottom: 15px;
    font-size: 1.3em;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    border-bottom: 1px solid #444;
    padding-bottom: 8px;
}

.calendar-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 0 10px;
    background-color: #333;
    border-radius: 4px;
    padding: 8px 12px;
}

.calendar-navigation a {
    color: #aaa;
    text-decoration: none;
    font-size: 0.9em;
    transition: color 0.2s;
    padding: 3px 8px;
    border-radius: 3px;
}

.calendar-navigation a:hover {
    color: #fff;
    background-color: #444;
}

.calendar-navigation span {
    font-weight: bold;
    color: #e0e0e0;
    font-size: 1.1em;
}

.calendar-container {
    overflow-x: auto;
    border-radius: 4px;
    border: 1px solid #444;
    background-color: #222;
}

.calendar-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
}

.calendar-table th, 
.calendar-table td {
    border: 1px solid #444;
    text-align: center;
    padding: 8px;
    height: 40px;
    vertical-align: top;
}

.calendar-table th {
    background-color: #3a3a3a;
    color: #e0e0e0;
    font-weight: bold;
    border-bottom: 2px solid #555;
}

.calendar-table td {
    background-color: #333;
    color: #ddd;
    position: relative;
    transition: background-color 0.2s;
}

.calendar-table td:hover {
    background-color: #3d3d3d;
}

.calendar-table td.other-month {
    color: #666;
    background-color: #2a2a2a;
}

.calendar-table td.today {
    background-color: #ff0000;
    box-shadow: inset 0 0 5px rgb(255, 255, 255);
    border: 1px solid #ff0000;
}

.calendar-table td.non-working-day {
    background-color: #353535;
    background-image: linear-gradient(45deg, #333333 25%, #383838 25%, #383838 50%, #333333 50%, #333333 75%, #383838 75%, #383838 100%);
    background-size: 8px 8px;
}

.calendar-table td.holiday {
    background-color: #353535;
    background-image: linear-gradient(45deg, #333333 25%, #383838 25%, #383838 50%, #333333 50%, #333333 75%, #383838 75%, #383838 100%);
    background-size: 8px 8px;
}

.calendar-table td.vacation {
    background-color: #304a30;
    background-image: linear-gradient(45deg, #304a30 25%, #355535 25%, #355535 50%, #304a30 50%, #304a30 75%, #355535 75%, #355535 100%);
    background-size: 8px 8px;
}

.calendar-table td.today.vacation {
    background-color: #405040;
    border: 1px solid #6a9a6a;
    box-shadow: inset 0 0 8px rgba(100, 200, 100, 0.3);
}

.calendar-table td.today.non-working-day {
    background-color: #454040;
    border: 1px solid #777;
    box-shadow: inset 0 0 8px rgba(150, 150, 150, 0.3);
}

.calendar-table td.clickable-day {
    cursor: pointer;
    position: relative;
}

.calendar-table td.clickable-day:hover {
    background-color: #4a4a4a;
    transition: background-color 0.2s;
}

.calendar-table td.clickable-day:hover::after {
    content: "+";
    position: absolute;
    top: 2px;
    right: 2px;
    font-weight: bold;
    color: #4CAF50;
    font-size: 1.2em;
}

.calendar-table td.clickable-day.has-vacation {
    cursor: pointer;
}

.calendar-table td.clickable-day.has-vacation:hover {
    background-color: #553535;
    transition: background-color 0.2s;
}

.calendar-table td.clickable-day.has-vacation:hover::after {
    content: "×";
    position: absolute;
    top: 2px;
    right: 2px;
    font-weight: bold;
    color: #ff5555;
    font-size: 1.2em;
}

.day-number {
    font-weight: bold;
    margin-bottom: 3px;
    font-size: 1.1em;
}

.holiday-name {
    font-size: 0.7em;
    color: #ffaaaa;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.vacation-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    background-color: #4CAF50;
    color: white;
    width: 18px;
    height: 18px;
    line-height: 18px;
    border-radius: 50%;
    font-size: 0.7em;
    font-weight: bold;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.calendar-legend {
    display: flex;
    justify-content: center;
    margin-top: 15px;
    flex-wrap: wrap;
    background-color: #333;
    border-radius: 4px;
    padding: 8px;
}

.legend-item {
    display: flex;
    align-items: center;
    margin: 0 10px;
    font-size: 0.85em;
    color: #ccc;
    padding: 3px 6px;
    border-radius: 3px;
}

.legend-item:hover {
    background-color: #3a3a3a;
}

.legend-color {
    display: inline-block;
    width: 15px;
    height: 15px;
    margin-right: 5px;
    border: 1px solid #555;
    border-radius: 3px;
}

.legend-color.today {
    background-color: #4a4a4a;
    box-shadow: inset 0 0 3px rgba(255, 255, 255, 0.3);
}

.legend-color.non-working-day {
    background-color: #353535;
    background-image: linear-gradient(45deg, #333333 25%, #383838 25%, #383838 50%, #333333 50%, #333333 75%, #383838 75%, #383838 100%);
    background-size: 8px 8px;
}

.legend-color.vacation {
    background-color: #304a30;
    background-image: linear-gradient(45deg, #304a30 25%, #355535 25%, #355535 50%, #304a30 50%, #304a30 75%, #355535 75%, #355535 100%);
    background-size: 8px 8px;
}

/* Responzívne úpravy pre malé obrazovky */
@media (max-width: 768px) {
    .calendar-table th, 
    .calendar-table td {
        padding: 5px;
        font-size: 0.9em;
    }
    
    .holiday-name {
        display: none;
    }
    
    .legend-item {
        margin: 3px 5px;
        font-size: 0.8em;
    }
}

/* Štýly pre dropdown menu priradenia strojov */
td.manual-assign-cell.absent:hover {
    background-color: #5a3a3a;
}

/* Štýly pre týždennú tabuľku */
.weekly-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 5px;
    table-layout: fixed;
}

.weekly-table th, 
.weekly-table td {
    border: 1px solid #444;
    padding: 6px 4px;
    text-align: center;
    font-size: 0.9em;
    height: 34px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.weekly-table th {
    background-color: #333;
    color: #fff;
}

.weekly-table td {
    color: #e0e0e0;
    font-weight: normal;
}

.weekly-table td:not(:empty) {
    font-weight: bold;
    font-size: 1.1em;
}

/* Extrémne zvýraznenie prihláseného používateľa */
.highlight-logged-user {
    background-color: #dc3545 !important;
    color: #ffffff !important;
    font-weight: bold !important;
    font-size: 1.2em !important;
    padding: 5px 8px !important;
    margin: 3px 0 !important;
    border-radius: 4px !important;
    box-shadow: 0 0 5px rgba(220, 53, 69, 0.7) !important;
    display: inline-block !important;
    text-align: center !important;
    width: auto !important;
}

/* --- FINÁLNA VERZIA VERTIKÁLNYCH HLAVIČIEK (KOMPATIBILNÉ SO SAFARI) --- */

/* 1. Bunka hlavičky (TH) - slúži ako kontajner */
.table-responsive th.vertical-header {
    width: 40px !important;
    min-width: 40px !important;
    max-width: 40px !important;
    height: 110px !important;
    padding: 0 !important;
    position: relative !important;
    overflow: hidden !important;
}

/* 2. Vnútorný DIV, ktorý obsahuje text (finálny pokus pre Safari) */
.table-responsive th.vertical-header > div {
    position: absolute;
    width: 110px;
    height: 40px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%) rotate(90deg);
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    padding: 5px;
    font-weight: bold;
    color: #e0e0e0;
}

/* 3. Špeciálne úpravy pre zvýrazneného používateľa */
.table-responsive th.vertical-header.highlight-user {
    background: #555 !important;
    border-left: 2px solid #aaa !important;
    border-right: 2px solid #aaa !important;
}

/* 4. Úprava pre zvýraznený div, aby mal správnu farbu textu */
.table-responsive th.vertical-header.highlight-user > div {
    color: #ffffff !important;
}