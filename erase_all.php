<?php
// erase_all.php
// Tento skript vymaže obsah (dáta) zo všetkých tabuliek v tvojej databáze.
// UPOZORNENIE: Dáta budú nevratne odstránené!

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Zahrň pripojenie k databáze
require_once 'config.php';

// Bezpečnostná kontrola – vykoná sa len ak je v URL parameter confirm=true
if (!isset($_GET['confirm']) || $_GET['confirm'] !== 'true') {
    echo "Upozornenie: Tento skript vymaže obsah všetkých tabuliek v databáze.<br>";
    echo "Ak si istý, spusti stránku nasledovne: erase_all.php?confirm=true";
    exit;
}

try {
    // Zakážeme kontrolu cudz<PERSON>ch k<PERSON>ú<PERSON> pre truncatovanie
    $pdo->exec("SET FOREIGN_KEY_CHECKS=0");

    // Získame zoznam všetkých tabuliek v aktuálnej databáze
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_NUM);

    if (!$tables) {
        echo "Databáza je prázdna. Žiadne tabuľky na vymazanie obsahu.";
        exit;
    }

    // Pre každú tabuľku vymažeme obsah
    foreach ($tables as $table) {
        $tableName = $table[0];
        $pdo->exec("TRUNCATE TABLE `$tableName`");
        echo "Vymazaný obsah tabuľky: " . htmlspecialchars($tableName) . "<br>";
    }

    // Opätovne povolíme kontrolu cudzích kľúčov
    $pdo->exec("SET FOREIGN_KEY_CHECKS=1");

    echo "Obsah všetkých tabuliek bol úspešne vymazaný.";
} catch (Exception $e) {
    echo "Chyba: " . $e->getMessage();
}
?>