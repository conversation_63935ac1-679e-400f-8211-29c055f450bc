<?php
// Inicializácia session a pripojenie k databáze
session_start();
require_once 'config.php';
require_once 'functions.php';

// Pridáme error handling pre lepšiu diagnostiku
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Funkcia pre logovanie chýb
function logError($message) {
    error_log("[get_assignments.php] " . $message);
}

try {
    // Kontrola, či je používateľ prihlásený
    if (!isset($_SESSION['user_id'])) {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Používateľ nie je prihlásený']);
        exit;
    }

    // Získanie PDO spojenia
    $pdo = getDbConnection();
    if (!$pdo) {
        throw new Exception("Nepodarilo sa získať spojenie s databázou");
    }

    // Získanie dátumu z parametra
    $date_str = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');
    $current_date = new DateTime($date_str);
    $current_date_str = $current_date->format('Y-m-d');

    // Získanie aktuálneho dátumu
    $today_date = new DateTime();
    $today_date_str = $today_date->format('Y-m-d');

    // Formátovanie dátumu pre zobrazenie
    $display_date_str = $current_date->format('d.m.Y');

    // Získanie používateľa
    $loggedUser = getUserById($_SESSION['user_id'], $pdo);

    // Výpočet navigačných dátumov
    $prev_date_obj = findPreviousWorkingDay($current_date, $pdo);
    $next_date_obj = findNextWorkingDay($current_date, $pdo);
    $prev_date_str = $prev_date_obj->format('Y-m-d');
    $next_date_str = $next_date_obj->format('Y-m-d');

    // Inicializácia premenných
    $generation_message = '';
    $assignments_today = [];
    $presentUsersToday = [];

    // Získanie metadát o priradeniach (kontrola, či funkcia existuje)
    $meta = [];
    if (function_exists('getAssignmentMetadataMeta')) {
        $meta = getAssignmentMetadataMeta($current_date, $pdo);
    }

    // Logika pre získanie priradení
    if ($current_date_str < $today_date_str) {
        // Historické dáta
        $generation_message = "Zobrazujú sa historické dáta.";
        $assignments_today = getAssignmentsForDate($current_date, $pdo);
        $presentUsersToday = getPresentUsers($current_date, $pdo);
        
        if (empty($assignments_today)) {
            $generation_message = "Pre tento minulý deň neboli nájdené žiadne priradenia.";
        }
    } elseif (!isWorkingDay($current_date, $pdo)) {
        // Voľný deň
        $generation_message = "Voľný deň - žiadne priradenia.";
    } else {
        // Aktuálny alebo budúci pracovný deň
        $assignments_today = getAssignmentsForDate($current_date, $pdo);
        $presentUsersToday = getPresentUsers($current_date, $pdo);
        
        if (empty($assignments_today) && $current_date_str == $today_date_str) {
            $generation_message = "Pre dnešný deň zatiaľ neboli vygenerované priradenia.";
        } elseif (empty($assignments_today)) {
            $generation_message = "Pre tento deň zatiaľ neboli vygenerované priradenia.";
        }
    }

    // Spracovanie dát pre zobrazenie
    // Ak nie sú priradenia, vytvoríme prázdne hodnoty
    if (empty($assignments_today)) {
        $today_schifflieder = '---';
        $today_skladnik = null;
        $today_machines = [];
        $today_kontrola = [];
    } else {
        list($today_schifflieder, $today_skladnik, $today_machines, $today_kontrola) = processAssignmentsForDisplay($assignments_today, $presentUsersToday);
    }

    // Získanie neprítomných používateľov
    $absentUsers = getAbsentUsersForDate($current_date, $pdo);

    // Príprava dát pre odpoveď
    $response = [
        'success' => true,
        'current_date' => $current_date_str,
        'display_date_str' => $display_date_str,
        'prev_date_str' => $prev_date_str,
        'next_date_str' => $next_date_str,
        'generation_message' => $generation_message,
        'schifflieder' => is_string($today_schifflieder) ? explode('<br>', strip_tags($today_schifflieder, '<br>')) : ['---'],
        'skladnik' => $today_skladnik ?: '---',
        'machines' => $today_machines ?: [],
        'kontrola' => is_array($today_kontrola) ? $today_kontrola : [],
        'absent_users' => is_array($absentUsers) ? $absentUsers : [],
        'logged_user_name' => $loggedUser['name'] ?? null,
        'is_working_day' => isWorkingDay($current_date, $pdo)
    ];

    // Odoslanie odpovede
    header('Content-Type: application/json');
    echo json_encode($response);

} catch (Exception $e) {
    // Logovanie chyby
    logError("Chyba: " . $e->getMessage() . " v " . $e->getFile() . " na riadku " . $e->getLine());
    
    // Odoslanie chybovej odpovede
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Nastala chyba pri spracovaní požiadavky: ' . $e->getMessage()
    ]);
}
?>
