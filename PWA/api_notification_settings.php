<?php
require_once 'config.php';

header('Content-Type: application/json');

// Kontrola prihlásenia
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Neprihlásený používateľ']);
    exit;
}

$loggedInUserId = $_SESSION['user_id'];

try {
    // Získanie nastavení notifikácií pre prihláseného používateľa
    $sql = "SELECT 
                COALESCE(uns.notifications_enabled, 1) as notifications_enabled,
                COALESCE(uns.notification_time, '07:00:00') as notification_time,
                COALESCE(uns.workdays_only, 1) as workdays_only
            FROM users u
            LEFT JOIN user_notification_settings uns ON u.id = uns.user_id
            WHERE u.id = ? AND u.is_active = 1";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$loggedInUserId]);
    $settings = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$settings) {
        // Ak používateľ neexistuje alebo nie je aktívny, vrátime predvolené nastavenia
        $settings = [
            'notifications_enabled' => 1,
            'notification_time' => '07:00:00',
            'workdays_only' => 1
        ];
    }
    
    // Konverzia času na formát HH:MM
    $settings['notification_time'] = substr($settings['notification_time'], 0, 5);
    
    echo json_encode([
        'success' => true,
        'settings' => $settings
    ]);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Chyba databázy',
        'message' => $e->getMessage()
    ]);
}
?>
