<?php
// Zapnutie zobrazenia chýb pre diagnostiku
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config.php';

header('Content-Type: application/json');

// Kontrola prihlásenia
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Neprihlásený používateľ']);
    exit;
}

$loggedInUserId = $_SESSION['user_id'];

// Diagnostika
error_log("API: Načítavam nastavenia pre používateľa ID: " . $loggedInUserId);

try {
    // Kontrola či existuje tabuľka user_notification_settings
    $checkTable = $pdo->query("SHOW TABLES LIKE 'user_notification_settings'");
    if ($checkTable->rowCount() == 0) {
        error_log("API: Tabuľka user_notification_settings neexistuje");
        echo json_encode([
            'error' => 'Tabuľka user_notification_settings neexistuje',
            'message' => 'Spustite SQL skript create_notification_settings_table.sql'
        ]);
        exit;
    }

    // Získanie nastavení notifikácií pre prihláseného používateľa
    $sql = "SELECT
                COALESCE(uns.notifications_enabled, 1) as notifications_enabled,
                COALESCE(uns.notification_time, '07:00:00') as notification_time,
                COALESCE(uns.workdays_only, 1) as workdays_only
            FROM users u
            LEFT JOIN user_notification_settings uns ON u.id = uns.user_id
            WHERE u.id = ? AND u.is_active = 1";

    error_log("API: Spúšťam SQL dotaz pre používateľa ID: " . $loggedInUserId);
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$loggedInUserId]);
    $settings = $stmt->fetch(PDO::FETCH_ASSOC);

    error_log("API: Výsledok SQL dotazu: " . json_encode($settings));
    
    if (!$settings) {
        // Ak používateľ neexistuje alebo nie je aktívny, vrátime predvolené nastavenia
        $settings = [
            'notifications_enabled' => 1,
            'notification_time' => '07:00:00',
            'workdays_only' => 1
        ];
    }
    
    // Konverzia času na formát HH:MM
    $settings['notification_time'] = substr($settings['notification_time'], 0, 5);
    
    echo json_encode([
        'success' => true,
        'settings' => $settings
    ]);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Chyba databázy',
        'message' => $e->getMessage()
    ]);
}
?>
