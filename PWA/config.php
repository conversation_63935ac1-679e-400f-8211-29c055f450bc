<?php
// config.php
$host = 'sql18.hostcreators.sk';  // hostiteľ databázy bez portu
$port = 3323;                     // port pripojenia
$dbname = 'd52810_dochadzka_test'; // názov databázy
$user = 'u52810_radisbruder';      // používateľ databázy
$pass = 'aiger-43AA-eoiII';     // heslo <--- SKONTROLUJ A ZMEŇ NA HESLO PRE u52810_radisbruder

// Globálna premenná pre PDO objekt pre jednoduchší prístup vo funkciách
global $pdo;
$pdo = null; // Initialize $pdo to null

try {
    // DSN reťazec s explicitne zadaným portom a charsetom
    $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4"; // Používame utf8mb4
    $pdo = new PDO($dsn, $user, $pass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,         // Vyhadzovanie výnimiek pri chybách
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,    // Predvolený fetch mód na asociatívne pole
        PDO::ATTR_EMULATE_PREPARES => false,                 // Vypnutie emulácie prepared statements pre vyššiu bezpečnosť
    ]);
} catch(PDOException $e) {
    // V produkcii by sa nemala zobrazovať detailná chyba používateľovi
    error_log("Database connection failed: " . $e->getMessage() . " (File: " . $e->getFile() . ", Line: " . $e->getLine() . ")"); // Zapísanie do logu
    // Zobraziť všeobecnú chybovú správu používateľovi
    die("CHYBA PRIPOJENIA K DATABÁZE: Nastala chyba pri pokuse o pripojenie k databáze. Skúste to prosím neskôr.");
}

// Funkcia na získanie PDO spojenia (ak by sme nechceli používať globálnu premennú)
function getDbConnection() {
    global $pdo;
    if ($pdo === null) {
        // This case should ideally not happen if config.php is included correctly
        // and the connection was successful.
        // You might want to log this specific failure or attempt reconnection,
        // but for now, we'll die with a message.
        error_log("getDbConnection() called when PDO object is null.");
        die("PDO spojenie nie je inicializované. Skript config.php sa pravdepodobne nenačítal správne alebo pripojenie zlyhalo.");
    }
    return $pdo;
}

// Nastavenie session, ak ešte nie je štartovaná
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Nastavenie časovej zóny
date_default_timezone_set('Europe/Bratislava');

// Zapnutie zobrazovania chýb počas vývoja (v produkcii vypnúť!)
// Odporúča sa mať tieto nastavenia iba raz, ideálne na začiatku hlavného skriptu alebo v configu.
// Ak sú aj v indexPWA.php, toto je duplicitné, ale neškodné.
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

?>