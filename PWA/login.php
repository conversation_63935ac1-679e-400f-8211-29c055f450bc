<?php
// login.php
require_once 'config.php'; // Načíta konfiguráciu a spustí session_start()

// Ak je používateľ už p<PERSON>hl<PERSON>ený, presmeruj na indexPWA.php
if (isset($_SESSION['user_id'])) {
    header("Location: indexPWA.php");
    exit;
}

$error = null; // Inicializácia chybovej premennej

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['username']) && isset($_POST['password'])) {
        $username = trim($_POST['username']);
        $password = $_POST['password'];

        if (empty($username) || empty($password)) {
            $error = "Musíte zadať meno aj heslo.";
        } else {
            try {
                $pdo = getDbConnection(); // Získanie PDO spojenia
                $stmt = $pdo->prepare("SELECT id, username, password, name FROM users WHERE username = ? AND is_active = 1");
                $stmt->execute([$username]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);

                // Overenie používateľa a hesla
                if ($user && password_verify($password, $user['password'])) {
                    // Heslo je správne, ulož ID a meno do session
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['user_name'] = $user['name']; // Uloženie mena pre prípadné zobrazenie
                    $_SESSION['username'] = $user['username'];

                    // Regenerácia session ID po prihlásení pre vyššiu bezpečnosť
                    session_regenerate_id(true);

                    header("Location: indexPWA.php");
                    exit;
                } else {
                    // Nesprávne meno alebo heslo, alebo neaktívny používateľ
                    $error = "Nesprávne prihlasovacie meno alebo heslo.";
                }
            } catch (PDOException $e) {
                // error_log("Login failed for user $username: " . $e->getMessage());
                $error = "Nastala chyba pri prihlasovaní. Skúste znova.";
            }
        }
    } else {
        $error = "Chýbajú prihlasovacie údaje.";
    }
}
?>
<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Prihlásenie</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container" style="max-width: 400px;"> <div class="header">Prihlásenie do systému</div>

        <?php if ($error): ?>
            <div class="message error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <form method="POST" action="login.php">
            <label for="username">Prihlasovacie meno:</label>
            <input type="text" id="username" name="username" required autofocus>

            <label for="password">Heslo:</label>
            <input type="password" id="password" name="password" required>

            <button type="submit">Prihlásiť sa</button>
        </form>
    </div>
</body>
</html>