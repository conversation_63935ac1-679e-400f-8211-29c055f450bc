const CACHE_NAME = 'denny-stav-cache-v1.1'; // Odporúčam zmeniť verziu pri každej zmene v cacheovaných súboroch
const urlsToCache = [
  './', // Relatívna cesta k PWA adresáru
  './indexPWA.php',
  './style.css',
  './manifest.json',
  './login.php',
  './images/icon-192x192.png',
  './images/icon-512x512.png',
  './images/icon-192x192-maskable.png',
  './images/icon-512x512-maskable.png'
];

// Inštalácia Service Workera a cacheovanie súborov
self.addEventListener('install', event => {
  console.log('[Service Worker] Install event');
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('[Service Worker] Opened cache:', CACHE_NAME);
        return cache.addAll(urlsToCache.map(url => new Request(url, { cache: 'reload' }))); // Vynúti načítanie zo siete pri inštalácii
      })
      .catch(error => {
        console.error('[Service Worker] Failed to cache urls during install:', error);
      })
  );
  self.skipWaiting(); // Aktivuje nový SW hneď po inštalácii
});

// Aktivácia Service Workera a správa starých cache
self.addEventListener('activate', event => {
  console.log('[Service Worker] Activate event');
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cache => {
          if (cache !== CACHE_NAME) {
            console.log('[Service Worker] Clearing old cache:', cache);
            return caches.delete(cache);
          }
        })
      );
    }).then(() => self.clients.claim()) // Prevezme kontrolu nad otvorenými klientmi
  );
});

// Načítanie požiadaviek
self.addEventListener('fetch', event => {
  // Pre PHP súbory alebo dynamický obsah je často lepšie ísť najprv na sieť (Network first, potom cache fallback)
  // Alebo pre PWA shell iba Cache first a dáta cez fetch API
  // Pre POST požiadavky alebo non-GET, vždy len sieť
  if (event.request.method !== 'GET') {
    event.respondWith(fetch(event.request).catch(() => { /* Môžeš sem pridať error handling pre POST offline */ }));
    return;
  }

  // Stratégia pre PHP súbory (Network falling back to cache)
  if (event.request.url.includes('.php')) {
    event.respondWith(
      fetch(event.request)
        .then(networkResponse => {
          // Ak je odpoveď zo siete OK, vrátime ju a môžeme ju aj cacheovať (opatrne pri dynamickom obsahu)
          // Pre PWA shell ako indexPWA.php to môže byť OK
          if (networkResponse && networkResponse.ok && urlsToCache.includes(new URL(event.request.url).pathname)) {
             const responseToCache = networkResponse.clone();
             caches.open(CACHE_NAME).then(cache => {
               cache.put(event.request, responseToCache);
             });
          }
          return networkResponse;
        })
        .catch(() => {
          // Ak sieť zlyhá, skúsime nájsť v cache
          return caches.match(event.request);
        })
    );
    return;
  }

  // Stratégia pre ostatné (statické) zdroje (Cache first, potom network)
  event.respondWith(
    caches.match(event.request)
      .then(cachedResponse => {
        if (cachedResponse) {
          return cachedResponse;
        }
        return fetch(event.request).then(
          networkResponse => {
            if (networkResponse && networkResponse.ok) {
              // Cache-ujeme iba ak je to v zozname urlsToCache alebo iný typ súboru, ktorý chceme cacheovať
              // a nie je to napr. externý API request, ktorý nechceme cacheovať
              if (urlsToCache.includes(new URL(event.request.url).pathname) || event.request.destination === 'style' || event.request.destination === 'script' || event.request.destination === 'image' || event.request.destination === 'font') {
                const responseToCache = networkResponse.clone();
                caches.open(CACHE_NAME)
                  .then(cache => {
                    cache.put(event.request, responseToCache);
                  });
              }
            }
            return networkResponse;
          }
        ).catch(error => {
          console.warn('[Service Worker] Fetch failed, no cache match & network error:', error);
          // Tu by si mohol vrátiť generickú offline stránku, ak existuje
          // napr. return caches.match('/offline.html');
        });
      })
  );
});

// Podpora pre push notifikácie
self.addEventListener('push', event => {
  console.log('[Service Worker] Push received');

  const options = {
    body: event.data ? event.data.text() : 'Nové pracovné priradenie',
    icon: './images/icon-192x192.png',
    badge: './images/icon-192x192.png',
    tag: 'work-assignment',
    requireInteraction: true,
    actions: [
      {
        action: 'view',
        title: 'Zobraziť'
      },
      {
        action: 'close',
        title: 'Zavrieť'
      }
    ]
  };

  event.waitUntil(
    self.registration.showNotification('NPC - Pracovné priradenie', options)
  );
});

// Spracovanie kliknutia na notifikáciu
self.addEventListener('notificationclick', event => {
  console.log('[Service Worker] Notification click received');

  event.notification.close();

  if (event.action === 'view' || !event.action) {
    // Otvorenie PWA aplikácie
    event.waitUntil(
      clients.openWindow('./indexPWA.php')
    );
  }
});