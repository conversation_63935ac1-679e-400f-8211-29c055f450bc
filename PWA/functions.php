<?php
// functions.php
require_once 'config.php'; // Načíta $pdo a nastavenia

// --- Autentifikácia ---
function isLoggedIn(): bool {
    // V reálnej aplikácii by tu mala byť robustnejšia kontrola session
    return isset($_SESSION['user_id']);
}

function requireLogin(): void {
    if (!isLoggedIn()) {
        // Pred presmerovaním je dobré ukončiť skript
        header("Location: login.php");
        exit;
    }
}


function formatUserDisplayName(string $rawName): string {
    if (empty(trim($rawName)) || $rawName === '---') {
        return $rawName; // Vr<PERSON>ti pôvodnú hodnotu, ak je prázdna alebo placeholder
    }

    $fullName = $rawName;
    $suffix = '';

    // Detekcia a oddelenie sufixu "(Zástup)"
    $zastupSuffix = ' (Zástup)';
    if (substr($fullName, -strlen($zastupSuffix)) === $zastupSuffix) {
        $fullName = trim(substr($fullName, 0, strlen($fullName) - strlen($zastupSuffix)));
        $suffix = $zastupSuffix;
    }

    $formattedName = '';
    // Špecifické prípady pre "Priezvisko Meno"
    if ($fullName === 'Mrázková Jana') {
        $formattedName = 'Jana M.';
    } elseif ($fullName === 'Andrášková Jana') {
        $formattedName = 'Jana A.';
    } else {
        // Všeobecný prípad: extrakcia krstného mena
        $nameParts = explode(' ', trim($fullName));
        if (count($nameParts) > 1) {
            // Predpokladáme formát "Priezvisko Meno", takže krstné meno je posledná časť
            $formattedName = end($nameParts);
        } elseif (count($nameParts) === 1 && !empty($nameParts[0])) {
            // Ak je len jedna časť, predpokladáme, že je to krstné meno
            $formattedName = $nameParts[0];
        } else {
            $formattedName = $fullName; // Fallback na celé meno, ak je formát neočakávaný
        }
    }

    return $formattedName . $suffix;
}




// --- Práca s Dátumom ---

/**
 * Skontroluje, či je dátum víkend (Sobota alebo Nedeľa).
 */
function isWeekend(DateTimeInterface $date): bool {
    $dayOfWeek = $date->format('N'); // 1 (pre Pondelok) až 7 (pre Nedeľu)
    return $dayOfWeek >= 6;
}

/**
 * Načíta sviatky z databázy pre daný rok (s cache).
 */
function getHolidaysFromDb(int $year, PDO $pdo): array {
    static $holidaysCache = []; // Statická cache pre sviatky
    if (!isset($holidaysCache[$year])) {
        try {
            $stmt = $pdo->prepare("SELECT `date` FROM holidays WHERE year = ?");
            $stmt->execute([$year]);
            $holidaysCache[$year] = $stmt->fetchAll(PDO::FETCH_COLUMN);
        } catch (PDOException $e) {
            error_log("Chyba pri načítavaní sviatkov pre rok $year: " . $e->getMessage());
            $holidaysCache[$year] = [];
        }
    }
    return $holidaysCache[$year];
}

/**
 * Skontroluje, či je dátum sviatok podľa DB.
 */
function isHoliday(DateTimeInterface $date, PDO $pdo): bool {
    $year = (int)$date->format('Y');
    $dateStr = $date->format('Y-m-d');
    $holidays = getHolidaysFromDb($year, $pdo);
    return in_array($dateStr, $holidays);
}

/**
 * Skontroluje, či je dátum pracovný deň.
 */
function isWorkingDay(DateTimeInterface $date, PDO $pdo): bool {
    if (isWeekend($date)) {
        return false;
    }
    if (isHoliday($date, $pdo)) {
        return false;
    }
    return true;
}

/**
 * Nájde nasledujúci pracovný deň.
 */
function findNextWorkingDay(DateTimeInterface $currentDate, PDO $pdo): DateTime {
    // Použijeme meniteľnú kópiu pre modifikáciu
    $date = DateTime::createFromInterface($currentDate);
    do {
        $date->modify('+1 day');
    } while (!isWorkingDay($date, $pdo));
    return $date;
}

/**
 * Nájde predchádzajúci pracovný deň.
 */
function findPreviousWorkingDay(DateTimeInterface $currentDate, PDO $pdo): DateTime {
    $date = DateTime::createFromInterface($currentDate);
    do {
        $date->modify('-1 day');
    } while (!isWorkingDay($date, $pdo));
    return $date;
}

/**
 * Vypočíta počet pracovných dní medzi dvoma dátumami.
 */
function countWorkingDaysBetween(DateTimeInterface $startDate, DateTimeInterface $endDate, PDO $pdo): int {
    // Použijeme nemeniteľné kópie pre istotu
    $start = DateTimeImmutable::createFromInterface($startDate);
    $end = DateTimeImmutable::createFromInterface($endDate);

    $diffSign = ($start <= $end) ? 1 : -1;
    if ($diffSign < 0) {
        list($start, $end) = [$end, $start]; // Vymeníme pre správny výpočet
    }

    $workingDays = 0;
    $current = $start;

    // Kým sme nedosiahli koncový dátum (bez neho samotného)
    // Ak chceme zahrnúť aj koncový dátum do počítania, malo by byť <=
    // Ak chceme počet dní MEDZI, tak < je správne.
    // Pre "days_since", ak posledný bol včera a dnes je target, malo by to byť 1.
    // Ak $start je 2023-01-01 a $end je 2023-01-03 (pracovné dni),
    // prejde 2023-01-01 (workingDays++ -> 1), current -> 2023-01-02
    // prejde 2023-01-02 (workingDays++ -> 2), current -> 2023-01-03
    // $current (01-03) < $end (01-03) je false, slučka končí. Vráti 2.
    // To znamená počet pracovných dní *v intervale* [start, end).
    // Pre "days_since", ak posledný bol $lastDate a target je $targetDate,
    // chceme počet pracovných dní od ($lastDate, $targetDate).
    // Ak $lastDate je piatok a $targetDate je pondelok, countWorkingDaysBetween(piatok, pondelok, $pdo)
    // by mal vrátiť 1.
    // $start=piatok, $end=pondelok.
    // current=piatok. isWorkingDay(piatok) -> workingDays=1. current=sobota.
    // current=sobota. !isWorkingDay. current=nedeľa.
    // current=nedeľa. !isWorkingDay. current=pondelok.
    // slučka končí, lebo pondelok !< pondelok. Vráti 1. To je správne.

    while ($current < $end) {
        if (isWorkingDay($current, $pdo)) {
            $workingDays++;
        }
        $current = $current->modify('+1 day');
    }

    return $workingDays * $diffSign;
}


/**
 * Určí cieľový dátum z GET parametra alebo použije dnešný pracovný deň.
 */
function determineTargetDate(?string $requestedDateStr, PDO $pdo): DateTime {
    $targetDate = null;
    if ($requestedDateStr) {
        try {
            $d = DateTime::createFromFormat('Y-m-d', $requestedDateStr);
            if ($d && $d->format('Y-m-d') === $requestedDateStr) {
                 $targetDate = $d->setTime(0, 0, 0);
            }
        } catch (Exception $e) { /* Ignorujeme neplatný dátum */ }
    }

     if ($targetDate === null) {
         $targetDate = new DateTime('today');
     }

    if (!isWorkingDay($targetDate, $pdo)) {
        $targetDate = findPreviousWorkingDay($targetDate, $pdo);
    }
    return $targetDate;
}


// --- Načítanie Dát z DB ---
function getAllUsers(PDO $pdo): array {
    try {
        $stmt = $pdo->query("SELECT * FROM users WHERE is_active = 1 ORDER BY name ASC");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Chyba pri načítavaní všetkých používateľov: " . $e->getMessage());
        return [];
    }
}

function getUserById(int $userId, PDO $pdo): ?array {
    try {
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        return $user ?: null;
    } catch (PDOException $e) {
         error_log("Chyba pri načítavaní používateľa podľa ID $userId: " . $e->getMessage());
        return null;
    }
}

function getPresentUsers(DateTimeInterface $date, PDO $pdo): array {
    $dateStr = $date->format('Y-m-d');
    try {
        $stmtAbsent = $pdo->prepare("SELECT DISTINCT user_id FROM absences WHERE ? BETWEEN start_date AND end_date");
        $stmtAbsent->execute([$dateStr]);
        $absentUserIds = array_map('intval', $stmtAbsent->fetchAll(PDO::FETCH_COLUMN));

        $allUsers = getAllUsers($pdo);
        $presentUsers = [];
        foreach ($allUsers as $user) {
            if (!in_array((int)$user['id'], $absentUserIds, true)) {
                $presentUsers[$user['id']] = $user;
            }
        }
        return $presentUsers;
    } catch (PDOException $e) {
        error_log("Chyba pri načítavaní prítomných používateľov pre dátum $dateStr: " . $e->getMessage());
        return [];
    }
}

function getActiveMachines(DateTimeInterface $date, PDO $pdo): array {
    $dateStr = $date->format('Y-m-d');
    $allMachines = [1, 2, 3];
    try {
        $stmtDowntime = $pdo->prepare("SELECT DISTINCT machine_number FROM machine_downtime WHERE ? BETWEEN start_date AND end_date");
        $stmtDowntime->execute([$dateStr]);
        $inactiveMachineNumbers = array_map('intval', $stmtDowntime->fetchAll(PDO::FETCH_COLUMN));
        return array_values(array_diff($allMachines, $inactiveMachineNumbers));
    } catch (PDOException $e) {
        error_log("Chyba pri načítavaní aktívnych strojov pre dátum $dateStr: " . $e->getMessage());
        return $allMachines;
    }
}

function getAssignmentsForDate(DateTimeInterface $date, PDO $pdo): array {
    $dateStr = $date->format('Y-m-d');
    $assignments = [];
    try {
        $stmt = $pdo->prepare("
            SELECT a.*, u.name
            FROM assignments a
            JOIN users u ON a.user_id = u.id
            WHERE a.assignment_date = ?
        ");
        $stmt->execute([$dateStr]);
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $assignments[(int)$row['user_id']] = $row;
        }
        return $assignments;
    } catch (PDOException $e) {
        error_log("Chyba pri načítavaní priradení pre dátum $dateStr: " . $e->getMessage());
        return [];
    }
}

// --- Pomocné funkcie pre históriu strojníkov ---
function getLastMachineAssignment(int $userId, PDO $pdo, DateTimeInterface $targetDate): ?int {
    try {
        $stmt = $pdo->prepare("
            SELECT machine_number
            FROM assignments
            WHERE user_id = :userId
              AND assigned_role = 'strojnik'
              AND machine_number > 0
              AND assignment_date < :targetDate
            ORDER BY assignment_date DESC
            LIMIT 1
        ");
        $stmt->bindValue(':userId', $userId, PDO::PARAM_INT);
        $stmt->bindValue(':targetDate', $targetDate->format('Y-m-d'), PDO::PARAM_STR);
        $stmt->execute();
        $result = $stmt->fetchColumn();
        return $result !== false ? (int)$result : null;
    } catch (PDOException $e) {
        error_log("Chyba pri načítavaní posledného priradenia stroja pre používateľa $userId: " . $e->getMessage());
        return null;
    }
}

function getLastTwoMachineAssignments(int $userId, PDO $pdo, DateTimeInterface $targetDate): array {
    try {
        $stmt = $pdo->prepare("
            SELECT machine_number
            FROM assignments
            WHERE user_id = :userId
              AND assigned_role = 'strojnik'
              AND machine_number > 0
              AND assignment_date < :targetDate
            ORDER BY assignment_date DESC
            LIMIT 2
        ");
        $stmt->bindValue(':userId', $userId, PDO::PARAM_INT);
        $stmt->bindValue(':targetDate', $targetDate->format('Y-m-d'), PDO::PARAM_STR);
        $stmt->execute();
        return array_map('intval', $stmt->fetchAll(PDO::FETCH_COLUMN));
    } catch (PDOException $e) {
        error_log("Chyba pri načítavaní posledných dvoch priradení strojov pre používateľa $userId: " . $e->getMessage());
        return [];
    }
}

function getMostFrequentMachineInHistory(int $userId, PDO $pdo, DateTimeInterface $targetDate, int $historyLimit = 20): ?int {
    try {
        $stmt = $pdo->prepare("
            SELECT machine_number
            FROM assignments
            WHERE user_id = :userId
              AND assigned_role = 'strojnik'
              AND machine_number > 0
              AND assignment_date < :targetDate
            ORDER BY assignment_date DESC
            LIMIT :limitValue
        ");
        $stmt->bindValue(':userId', $userId, PDO::PARAM_INT);
        $stmt->bindValue(':targetDate', $targetDate->format('Y-m-d'), PDO::PARAM_STR);
        $stmt->bindValue(':limitValue', $historyLimit, PDO::PARAM_INT);
        $stmt->execute();
        $machines = $stmt->fetchAll(PDO::FETCH_COLUMN);

        if (empty($machines)) {
            return null;
        }
        $counts = array_count_values($machines);
        arsort($counts);
        return (int)key($counts);
    } catch (PDOException $e) {
        error_log("Chyba pri načítavaní najčastejšieho stroja pre používateľa $userId: " . $e->getMessage());
        return null;
    }
}

// --- NOVÉ POMOCNÉ FUNKCIE PRE HISTÓRIU A PENALIZÁCIU ---

/**
 * Získa počet pracovných dní od posledného priradenia daného stroja používateľovi.
 * Ak používateľ nikdy nebol na danom stroji, vráti veľké číslo (indikuje preferenciu).
 */
function getDaysSinceLastOnMachine(int $userId, int $machineNumber, PDO $pdo, DateTimeInterface $targetDate): int {
    try {
        $stmt = $pdo->prepare("
            SELECT assignment_date
            FROM assignments
            WHERE user_id = :userId
              AND machine_number = :machineNumber
              AND assigned_role = 'strojnik'
              AND assignment_date < :targetDate
            ORDER BY assignment_date DESC
            LIMIT 1
        ");
        $stmt->bindValue(':userId', $userId, PDO::PARAM_INT);
        $stmt->bindValue(':machineNumber', $machineNumber, PDO::PARAM_INT);
        $stmt->bindValue(':targetDate', $targetDate->format('Y-m-d'), PDO::PARAM_STR);
        $stmt->execute();
        $lastDateStr = $stmt->fetchColumn();

        if ($lastDateStr) {
            $lastDate = new DateTimeImmutable($lastDateStr);
            // countWorkingDaysBetween vracia počet pracovných dní *medzi* dvoma dátumami,
            // nezahŕňa prvý dátum, ale ak je rozdiel 1 pracovný deň, vráti 1.
            return countWorkingDaysBetween($lastDate, $targetDate, $pdo);
        } else {
            return 999; // Používateľ nikdy nebol na tomto stroji, vysoká hodnota pre preferenciu
        }
    } catch (PDOException $e) {
        error_log("Chyba pri načítavaní dní od posledného priradenia stroja $machineNumber pre používateľa $userId: " . $e->getMessage());
        return 999; // V prípade chyby tiež vrátime vysokú hodnotu
    }
}

/**
 * Získa zoznam posledných X strojov (machine_number), na ktorých používateľ pracoval.
 * Používa sa na penalizáciu, ak bol stroj nedávno priradený.
 */
function getRecentMachineHistory(int $userId, PDO $pdo, DateTimeInterface $targetDate, int $limit): array {
    if ($limit <= 0) {
        return [];
    }
    try {
        $stmt = $pdo->prepare("
            SELECT machine_number
            FROM assignments
            WHERE user_id = :userId
              AND assigned_role = 'strojnik'
              AND machine_number > 0
              AND assignment_date < :targetDate
            ORDER BY assignment_date DESC
            LIMIT :limitValue
        ");
        $stmt->bindValue(':userId', $userId, PDO::PARAM_INT);
        $stmt->bindValue(':targetDate', $targetDate->format('Y-m-d'), PDO::PARAM_STR);
        $stmt->bindValue(':limitValue', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return array_map('intval', $stmt->fetchAll(PDO::FETCH_COLUMN));
    } catch (PDOException $e) {
        error_log("Chyba pri načítavaní nedávnej histórie strojov pre používateľa $userId: " . $e->getMessage());
        return [];
    }
}

/**
 * Získa počet, koľkokrát bol daný stroj priradený používateľovi v rámci posledných X priradení.
 * Používa sa na penalizáciu za nevyváženosť.
 */
function getMachineCountInHistory(int $userId, int $machineNumber, PDO $pdo, DateTimeInterface $targetDate, int $assignmentLookback = 20): int {
    if ($assignmentLookback <= 0) {
        return 0;
    }
    try {
        $stmtHistory = $pdo->prepare("
            SELECT machine_number
            FROM assignments
            WHERE user_id = :userId
              AND assigned_role = 'strojnik'
              AND machine_number > 0
              AND assignment_date < :targetDate
            ORDER BY assignment_date DESC
            LIMIT :limitValue
        ");
        $stmtHistory->bindValue(':userId', $userId, PDO::PARAM_INT);
        $stmtHistory->bindValue(':targetDate', $targetDate->format('Y-m-d'), PDO::PARAM_STR);
        $stmtHistory->bindValue(':limitValue', $assignmentLookback, PDO::PARAM_INT);
        $stmtHistory->execute();
        $recentMachines = array_map('intval', $stmtHistory->fetchAll(PDO::FETCH_COLUMN));

        $count = 0;
        foreach ($recentMachines as $recentMachine) {
            if ($recentMachine === $machineNumber) {
                $count++;
            }
        }
        return $count;

    } catch (PDOException $e) {
        error_log("Chyba pri načítavaní počtu priradení stroja $machineNumber pre používateľa $userId v histórii: " . $e->getMessage());
        return 0;
    }
}


/**
 * Vypočíta denné priradenia.
 */
function calculateDailyAssignments(DateTimeInterface $targetDate, PDO $pdo): array
{
    if ($pdo->inTransaction()) {
        try { $pdo->rollBack(); } catch (PDOException $e) { /* ignorujeme */ }
        error_log("Transakcia bola vrátená pred začatím výpočtu pre " . $targetDate->format('Y-m-d'));
    }

    if (!isWorkingDay($targetDate, $pdo)) {
        error_log("Dátum " . $targetDate->format('Y-m-d') . " nie je pracovný deň.");
        return [];
    }

    $dateStr = $targetDate->format('Y-m-d');
    error_log("--- Výpočet priradení pre pracovný deň: $dateStr ---");

    $presentUsers = getPresentUsers($targetDate, $pdo);
    $activeMachineNumbers = getActiveMachines($targetDate, $pdo);
    $assignments = [];
    $busyIds = [];

    $veduciId = null; $veduciIsPresent = false; $veduciName = '';
    foreach ($presentUsers as $u) {
        if (($u['role1'] ?? null) === 'veduci') {
            $veduciId = (int)$u['id'];
            $veduciIsPresent = true;
            $veduciName = $u['name'];
            error_log("Vedúci (prítomný): ID $veduciId ($veduciName)");
            break;
        }
    }
    if (!$veduciIsPresent) { error_log("Vedúci je neprítomný alebo nebol nájdený."); }

    $absentUserIdsMap = [];
    try {
        $stmtAbsentAll = $pdo->prepare("SELECT user_id FROM absences WHERE ? BETWEEN start_date AND end_date");
        $stmtAbsentAll->execute([$dateStr]);
        $absentIds = $stmtAbsentAll->fetchAll(PDO::FETCH_COLUMN);
        $absentUserIdsMap = array_flip(array_map('intval', $absentIds));
        error_log("ID neprítomných používateľov pre $dateStr: " . (!empty($absentUserIdsMap) ? implode(', ', array_keys($absentUserIdsMap)) : 'Žiadne'));
    } catch (PDOException $e) {
        error_log("Chyba DB pri načítavaní absencií pre $dateStr: " . $e->getMessage());
        throw $e;
    }

    // 1. Schifflieder
    $schiffId = null; $bruder = null; $domi = null;
    try {
        $stmtAllSchiff = $pdo->query("SELECT id, username, role1, name FROM users WHERE role1 = 'schifflieder' AND is_active = 1");
        while ($u = $stmtAllSchiff->fetch(PDO::FETCH_ASSOC)) {
            if ($u['username'] === 'radis') $bruder = $u;
            if ($u['username'] === 'dominika') $domi = $u;
        }
    } catch (PDOException $e) { error_log("Chyba DB pri načítavaní Schifflieder: " . $e->getMessage()); }

    $prefSchiff = null; $altSchiff = null;
    try {
        $stmtRef = $pdo->prepare("SELECT reference_date FROM schifflieder_reference WHERE id = 1");
        $stmtRef->execute(); $refDateStr = $stmtRef->fetchColumn();
        if (!$refDateStr) throw new Exception("Referenčný dátum Schifflieder nenájdený.");
        $refDate = new DateTimeImmutable($refDateStr);
        $wDiff = countWorkingDaysBetween($refDate, $targetDate, $pdo);
        error_log("Rozdiel prac. dní Schifflieder od $refDateStr do $dateStr: $wDiff");
        $prefSchiff = ($wDiff % 2 === 0) ? $bruder : $domi;
        $altSchiff = ($wDiff % 2 === 0) ? $domi : $bruder;
    } catch (Exception $e) { error_log("Chyba výpočtu preferencie Schifflieder pre $dateStr: " . $e->getMessage()); }

    $assignedRegularSchiff = false;
    if ($prefSchiff && isset($presentUsers[$prefSchiff['id']]) && !in_array($prefSchiff['id'], $busyIds, true)) {
        $schiffId = $prefSchiff['id']; $assignedRegularSchiff = true;
        error_log("Schifflieder (Preferovaný): ID $schiffId ({$prefSchiff['name']})");
    } elseif ($altSchiff && isset($presentUsers[$altSchiff['id']]) && !in_array($altSchiff['id'], $busyIds, true)) {
        $schiffId = $altSchiff['id']; $assignedRegularSchiff = true;
        error_log("Schifflieder (Alternatívny): ID $schiffId ({$altSchiff['name']})");
    }

    if (!$assignedRegularSchiff) {
        if ($veduciIsPresent && !in_array($veduciId, $busyIds, true)) {
            $schiffId = $veduciId;
            error_log("Schifflieder (Fallback Vedúci): ID $schiffId ($veduciName)");
        } else {
            error_log("Rola Schifflieder nemohla byť priradená pre $dateStr.");
        }
    }

    if ($schiffId) {
        $order = null;
        if ($bruder && $schiffId === $bruder['id']) $order = 1;
        elseif ($domi && $schiffId === $domi['id']) $order = 2;
        elseif ($schiffId === $veduciId) $order = 9;
        $assignments[] = ['user_id' => $schiffId, 'assigned_role' => 'schifflieder', 'machine_number' => null, 'assignment_order' => $order];
        $busyIds[] = $schiffId;
    }

    // 2. Skladník
    $skladnikId = null; $skladnici = []; $skladnikUsersData = []; $assignedRegularSkladnik = false;
    try {
        $stmtAllSkl = $pdo->query("SELECT id, name FROM users WHERE role1 = 'skladnik' AND is_active = 1 ORDER BY name ASC");
        $skladnikUsersData = $stmtAllSkl->fetchAll(PDO::FETCH_ASSOC);
        $skladnici = array_column($skladnikUsersData, 'id');
        $countSkladnici = count($skladnici);

        if ($countSkladnici > 0) {
            $refMon = new DateTimeImmutable('2025-04-14');
            $weekMon = (clone DateTime::createFromInterface($targetDate))->modify('Monday this week');
            $daysDiff = (int)$refMon->diff($weekMon)->days;
            $blockIndex = floor($daysDiff / 14);
            $preferredSkladnikIndex = $blockIndex % $countSkladnici;
            error_log("Výpočet Skladníka: RefMon={$refMon->format('Y-m-d')}, WeekMon={$weekMon->format('Y-m-d')}, RozdielDni=$daysDiff, Blok=$blockIndex, PrefIndex=$preferredSkladnikIndex");

            for ($i = 0; $i < $countSkladnici; $i++) {
                $tryIdx = ($preferredSkladnikIndex + $i) % $countSkladnici;
                $tryId = $skladnici[$tryIdx];
                if (isset($presentUsers[$tryId]) && !in_array($tryId, $busyIds, true)) {
                    $skladnikId = $tryId; $assignedRegularSkladnik = true;
                    $skladnikNameFound = ''; foreach($skladnikUsersData as $skud) { if($skud['id'] == $tryId) $skladnikNameFound = $skud['name']; }
                    error_log("Skladník (Cyklus): ID $skladnikId ($skladnikNameFound) (Offset $i od pref. indexu $preferredSkladnikIndex)");
                    break;
                }
            }
        }
    } catch (Exception $e) { error_log("Chyba výpočtu Skladníka pre $dateStr: " . $e->getMessage()); }

    if (!$assignedRegularSkladnik) {
        if ($veduciIsPresent && !in_array($veduciId, $busyIds, true)) {
            $skladnikId = $veduciId;
            error_log("Skladník (Fallback Vedúci): ID $skladnikId ($veduciName)");
        } else {
            error_log("Rola Skladník nemohla byť priradená pre $dateStr.");
        }
    }

    if ($skladnikId) {
        $assignments[] = ['user_id' => $skladnikId, 'assigned_role' => 'skladnik', 'machine_number' => null];
        $busyIds[] = $skladnikId;
    }

    // 3. Strojníci
    $strojnikAssignments = [];
    $strojnikCandidatesForActiveMachines = [];
    $allPotentialMachinists = [];

    try {
        $q_cands = $pdo->query("SELECT id, name, strojnik_order FROM users WHERE strojnik_order IS NOT NULL AND is_active = 1 ORDER BY strojnik_order");
        while ($r = $q_cands->fetch(PDO::FETCH_ASSOC)) {
            $allPotentialMachinists[(int)$r['strojnik_order']] = ['id' => (int)$r['id'], 'name' => $r['name']];
        }
    } catch (PDOException $e) {
        error_log("Chyba DB pri načítavaní kandidátov strojníkov: " . $e->getMessage());
        if (empty($allPotentialMachinists)) goto process_final_roles;
    }

    if (empty($allPotentialMachinists)) {
        error_log("Nie sú definovaní kandidáti strojníkov. Preskakuje sa priradenie.");
        goto process_final_roles;
    }

    $numberOfActiveMachines = count($activeMachineNumbers);
    if ($numberOfActiveMachines == 0) {
        error_log("Žiadne aktívne stroje pre $dateStr. Preskakuje priradenie strojníkov.");
    } else {
        error_log("Aktívne stroje pre $dateStr: " . implode(', ', $activeMachineNumbers));
        
        $prevDayStrojnikOrders = [];
        try {
            $prevDay = findPreviousWorkingDay($targetDate, $pdo); $prevDayStr = $prevDay->format('Y-m-d');
            $prevStmt = $pdo->prepare("SELECT u.strojnik_order FROM assignments a JOIN users u ON a.user_id = u.id WHERE a.assignment_date = ? AND a.assigned_role = 'strojnik' AND u.strojnik_order IS NOT NULL");
            $prevStmt->execute([$prevDayStr]);
            $prevDayStrojnikOrders = array_map('intval', $prevStmt->fetchAll(PDO::FETCH_COLUMN));
            $prevDayStrojnikOrders = array_unique($prevDayStrojnikOrders); sort($prevDayStrojnikOrders);
            error_log("Unikátne porad. čísla strojníkov z predch. dňa ($prevDayStr): " . (!empty($prevDayStrojnikOrders) ? implode(', ', $prevDayStrojnikOrders) : 'Žiadne'));
        } catch (Exception $e) { error_log("Chyba načítania priradení z predch. dňa pre startOrder: " . $e->getMessage()); }
        
        $startOrder = 1;
        if (!empty($prevDayStrojnikOrders)) {
            $maxOrderPrevDay = max($prevDayStrojnikOrders); $blockEnd = $maxOrderPrevDay;
            $maxStrojnikOrderInSystem = !empty($allPotentialMachinists) ? max(array_keys($allPotentialMachinists)) : 9;
            $currentCheckOrder = ($maxOrderPrevDay % $maxStrojnikOrderInSystem) + 1; 
            $safetyLoopCheck = 0;
            while (in_array($currentCheckOrder, $prevDayStrojnikOrders) && $safetyLoopCheck < ($maxStrojnikOrderInSystem + 5)) {
                $blockEnd = $currentCheckOrder; 
                $currentCheckOrder = ($currentCheckOrder % $maxStrojnikOrderInSystem) + 1; 
                $safetyLoopCheck++;
            }
            $startOrder = ($blockEnd % $maxStrojnikOrderInSystem) + 1;
        } else { error_log("Žiadne porad. čísla strojníkov z predch. dňa. Predvolené štart. poradie: 1."); }
        error_log("Vypočítané štart. poradie pre výber kandidátov: $startOrder");

        $machinistKeys = array_keys($allPotentialMachinists);
        sort($machinistKeys);
        $startIndexInKeys = array_search($startOrder, $machinistKeys);
        if ($startIndexInKeys === false) {
            $startIndexInKeys = 0;
            if (!empty($machinistKeys)) $startOrder = $machinistKeys[0];
            error_log("Upozornenie: startOrder $startOrder nenájdený v kľúčoch. Používa sa prvý kľúč: " . ($machinistKeys[0] ?? 'N/A'));
        }

        $numMachinistKeys = count($machinistKeys);
        $presentCandidatesFoundCount = 0;
        
        if ($numMachinistKeys > 0) {
            for ($i = 0; $i < $numMachinistKeys; $i++) {
                if ($presentCandidatesFoundCount >= $numberOfActiveMachines) {
                    error_log("Dostatok PRÍTOMNÝCH kandidátov ($presentCandidatesFoundCount) pre $numberOfActiveMachines akt. strojov. Koniec vyhľadávania.");
                    break; 
                }
                $currentKeyIndex = ($startIndexInKeys + $i) % $numMachinistKeys;
                $currentEvalOrder = $machinistKeys[$currentKeyIndex];
                $machinistData = $allPotentialMachinists[$currentEvalOrder];
                $userId = $machinistData['id'];
                $userName = $machinistData['name'];

                if (in_array($userId, $busyIds, true)) {
                    error_log("Preskakuje strojník ID $userId (Poradie $currentEvalOrder, Meno: $userName) - zaneprázdnený.");
                    continue;
                }
                
                if (isset($absentUserIdsMap[$userId])) {
                    error_log("Strojník ID $userId (Poradie $currentEvalOrder, Meno: $userName) NEPRÍTOMNÝ. Priradený Stroj 0.");
                    $strojnikAssignments[] = ['user_id' => $userId, 'assigned_role' => 'strojnik', 'machine_number' => 0];
                    $busyIds[] = $userId;
                } else {
                    if ($presentCandidatesFoundCount < $numberOfActiveMachines) {
                        error_log("Strojník ID $userId (Poradie $currentEvalOrder, Meno: $userName) PRÍTOMNÝ. Pridaný ku kandidátom. (Nájdených $presentCandidatesFoundCount z $numberOfActiveMachines).");
                        $strojnikCandidatesForActiveMachines[] = ['user_id' => $userId, 'name' => $userName, 'strojnik_order' => $currentEvalOrder];
                        $presentCandidatesFoundCount++;
                    } else {
                        error_log("Strojník ID $userId (Poradie $currentEvalOrder, Meno: $userName) PRÍTOMNÝ, ale už je $presentCandidatesFoundCount kandidátov pre $numberOfActiveMachines strojov. Potenciál pre Kontrolu.");
                    }
                }
            }
        }
        error_log("Výber strojníkov ukončený. Prítomní kandidáti: " . count($strojnikCandidatesForActiveMachines) . ". Stroj 0 priradený: " . count(array_filter($strojnikAssignments, fn($a)=>$a['machine_number']===0)) );

        if (!empty($strojnikCandidatesForActiveMachines) && !empty($activeMachineNumbers)) {
            $numMachinesToAssignThisRun = min(count($strojnikCandidatesForActiveMachines), count($activeMachineNumbers));

            if ($numMachinesToAssignThisRun > 0) {
                $candidatesForPermutation = array_slice($strojnikCandidatesForActiveMachines, 0, $numMachinesToAssignThisRun);
                $machinesToUseInPermutation = array_slice($activeMachineNumbers, 0, $numMachinesToAssignThisRun);
                error_log("Permutácie: " . count($candidatesForPermutation) . " kandidátov pre " . count($machinesToUseInPermutation) . " strojov.");

                $permutations = [];
                $generatePermutations = function (array $elements, callable $callback, array $currentPermutation = []) use (&$generatePermutations, $numMachinesToAssignThisRun) {
                    if (count($currentPermutation) === $numMachinesToAssignThisRun) { $callback($currentPermutation); return; }
                    if (empty($elements)) return;
                    foreach ($elements as $key => $element) {
                        $remainingElements = $elements; unset($remainingElements[$key]);
                        $newPermutation = $currentPermutation; $newPermutation[] = $element;
                        $generatePermutations(array_values($remainingElements), $callback, $newPermutation);
                    }
                };
                $generatePermutations($machinesToUseInPermutation, function($p) use (&$permutations) { $permutations[] = $p; });
                error_log("Vygenerovaných " . count($permutations) . " permutácií pre " . count($candidatesForPermutation) . " kandidátov použitím strojov: [" . implode(',', $machinesToUseInPermutation) . "]");

                $bestPermutation = null; $lowestPenaltyScore = PHP_INT_MAX;
                if (empty($permutations) && $numMachinesToAssignThisRun > 0) {
                    error_log("Žiadne permutácie. Fallback na sekvenčné priradenie.");
                    $bestPermutation = $machinesToUseInPermutation; 
                }

                foreach ($permutations as $currentMachinePermutation) {
                    if (count($currentMachinePermutation) !== $numMachinesToAssignThisRun) continue;
                    $currentTotalPenalty = 0;
                    for ($i = 0; $i < $numMachinesToAssignThisRun; $i++) {
                        $candidate = $candidatesForPermutation[$i];
                        $userId = $candidate['user_id'];
                        $assignedMachine = $currentMachinePermutation[$i];
                        $penaltyForPair = 0;

                        $lastMachine = getLastMachineAssignment($userId, $pdo, $targetDate);
                        if ($lastMachine !== null && $assignedMachine == $lastMachine) {
                            $penaltyForPair += 1000;
                        }

                        $numRecentHistoryCheck = count($machinesToUseInPermutation) - 1;
                        if ($numRecentHistoryCheck > 0) {
                            $recentHistory = getRecentMachineHistory($userId, $pdo, $targetDate, $numRecentHistoryCheck);
                            if (in_array($assignedMachine, $recentHistory)) {
                                $penaltyForPair += 200;
                            }
                        }
                        
                        $daysSince = getDaysSinceLastOnMachine($userId, $assignedMachine, $pdo, $targetDate);
                        $penaltyForPair += round(50 / ($daysSince + 1));

                        $countInRecentHistory = getMachineCountInHistory($userId, $assignedMachine, $pdo, $targetDate, 20);
                        $penaltyForPair += $countInRecentHistory * 10;

                        $currentTotalPenalty += $penaltyForPair;
                    }

                    if ($currentTotalPenalty < $lowestPenaltyScore) {
                        $lowestPenaltyScore = $currentTotalPenalty;
                        $bestPermutation = $currentMachinePermutation;
                    }
                }
                error_log("Najlepšia permutácia so skóre $lowestPenaltyScore: [" . ($bestPermutation ? implode(',', $bestPermutation) : 'Žiadna') . "]");

                if ($bestPermutation) {
                    for ($i = 0; $i < $numMachinesToAssignThisRun; $i++) {
                        $candidate = $candidatesForPermutation[$i];
                        $assignedMachine = $bestPermutation[$i];
                        $isAlreadyAssignedStrojnik = false;
                        foreach($strojnikAssignments as $existingAs) {
                            if($existingAs['user_id'] == $candidate['user_id'] && $existingAs['assigned_role'] == 'strojnik') {
                                $isAlreadyAssignedStrojnik = true; break;
                            }
                        }
                        if(!$isAlreadyAssignedStrojnik) {
                             $strojnikAssignments[] = ['user_id' => $candidate['user_id'], 'assigned_role' => 'strojnik', 'machine_number' => $assignedMachine];
                            $busyIds[] = $candidate['user_id'];
                            error_log("PRIRADENÝ AKTÍVNY STROJ: Používateľ {$candidate['user_id']} ({$candidate['name']}) -> Stroj $assignedMachine (Skóre $lowestPenaltyScore)");
                        } else {
                             error_log("Používateľ {$candidate['user_id']} ({$candidate['name']}) už má rolu strojníka, nepriraďuje sa akt. stroj $assignedMachine.");
                        }
                    }
                } else { error_log("Najlepšia permutácia nenájdená."); }
            } else { error_log("Nedostatok kandidátov/strojov pre permutácie."); }
        } else { error_log("Preskakuje optimalizácia: chýbajú kandidáti alebo akt. stroje."); }
    } 

    $finalStrojnikUserIds = [];
    foreach($strojnikAssignments as $sa_final) {
        if(!in_array($sa_final['user_id'], $finalStrojnikUserIds)) {
            $assignments[] = $sa_final;
            $finalStrojnikUserIds[] = $sa_final['user_id'];
        }
    }
    
    process_final_roles:

    $mainSchiffId = null;
    foreach($assignments as $assign) {
        if ($assign['assigned_role'] === 'schifflieder') {
            if (($bruder && $assign['user_id'] === $bruder['id']) || ($domi && $assign['user_id'] === $domi['id'])) {
                $mainSchiffId = $assign['user_id'];
            }
            break;
        }
    }
    if ($mainSchiffId !== null) {
        $otherSchiffUser = null;
        if ($bruder && $mainSchiffId === $bruder['id'] && $domi) $otherSchiffUser = $domi;
        elseif ($domi && $mainSchiffId === $domi['id'] && $bruder) $otherSchiffUser = $bruder;

        if ($otherSchiffUser && isset($presentUsers[$otherSchiffUser['id']]) && !in_array($otherSchiffUser['id'], $busyIds, true)) {
            $busyIds[] = $otherSchiffUser['id'];
            error_log("Druhý Schifflieder (ID {$otherSchiffUser['id']}, {$otherSchiffUser['name']}) označený ako zaneprázdnený.");
        }
    }

    // 7. Kontrola
    error_log("--- Priradenie roly Kontrola ---");
    $kontrolaCount = 0;
    foreach ($presentUsers as $uid => $u) {
        if (!in_array((int)$uid, $busyIds, true) && ($veduciId === null || (int)$uid !== $veduciId) ) {
            $assignments[] = ['user_id' => (int)$uid, 'assigned_role' => 'kontrola', 'machine_number' => null];
            $kontrolaCount++;
            error_log("Priradená Kontrola: $uid ({$u['name']})");
        }
    }
    error_log("Rola Kontrola priradená $kontrolaCount používateľom.");

    error_log("--- Výpočet priradení ukončený pre $dateStr. Celkový počet: " . count($assignments) . " ---");
    return $assignments;
}


 // --- Ostatné funkcie (saveAssignments, deleteFutureDataFromDate, atď.) ---
 // Tieto funkcie by mali nasledovať tak, ako boli v pôvodnom súbore.
 // Pre stručnosť ich tu neopakujem, ale je dôležité, aby boli prítomné.
 // ...
 function saveAssignments(DateTimeInterface $date, array $assignments, PDO $pdo): void
 {
     if ($pdo->inTransaction()) {
         try { $pdo->rollBack(); error_log("Transakcia bola vrátená (rolled back) pred saveAssignments pre " . $date->format('Y-m-d')); }
         catch (PDOException $e) { error_log("Chyba pri vrátení transakcie pred saveAssignments: " . $e->getMessage()); }
     }
 
     $dateStr = $date->format('Y-m-d');
     error_log("--- Ukladanie priradení pre $dateStr ---");
 
     try {
         $pdo->beginTransaction();
 
         $stmtDel = $pdo->prepare("DELETE FROM assignments WHERE assignment_date = ?");
         $stmtDel->execute([$dateStr]);
 
         $stmtIns = $pdo->prepare("
             INSERT INTO assignments
             (assignment_date, user_id, assigned_role, machine_number, assignment_order)
             VALUES (?, ?, ?, ?, ?)
         ");
 
         $stmtUpdateUser = $pdo->prepare("
             UPDATE users
             SET last_machine_assigned = ?,
                 last_assignment_date = ?
             WHERE id = ?
         ");
 
         $insertedCount = 0;
         foreach ($assignments as $assignment) {
             if (!isset($assignment['user_id'], $assignment['assigned_role'])) {
                  error_log("Preskakuje sa neplatný záznam priradenia: " . print_r($assignment, true));
                  continue;
              }
 
             $machineNumber = $assignment['machine_number'] ?? null;
             if ($machineNumber === '') $machineNumber = null;

             $assignmentOrder = $assignment['assignment_order'] ?? null;
 
             $stmtIns->execute([
                 $dateStr,
                 $assignment['user_id'],
                 $assignment['assigned_role'],
                 $machineNumber,
                 $assignmentOrder
             ]);
             $insertedCount++;
 
             if ($assignment['assigned_role'] === 'strojnik' && $machineNumber !== null && $machineNumber > 0) {
                 try {
                      $stmtUpdateUser->execute([$machineNumber, $dateStr, $assignment['user_id']]);
                 } catch (PDOException $e) {
                      error_log("Upozornenie DB: Nepodarilo sa aktualizovať last_machine_assigned pre používateľa {$assignment['user_id']} dňa $dateStr: " . $e->getMessage());
                  }
             }
         }
         error_log("Vložených $insertedCount nových priradení pre $dateStr.");
 
         $pdo->commit();
         error_log("--- Priradenia úspešne uložené pre $dateStr ---");
 
     } catch (Exception $e) {
         if ($pdo->inTransaction()) {
             try { $pdo->rollBack(); } catch (PDOException $re) { error_log("Chyba pri vrátení transakcie po neúspešnom saveAssignments: " . $re->getMessage()); }
         }
         error_log("!!! KRITICKÁ CHYBA pri ukladaní priradení pre $dateStr: " . $e->getMessage() . " !!!");
         throw new Exception("Nepodarilo sa uložiť priradenia pre $dateStr kvôli chybe.", 0, $e);
     }
 }

function deleteFutureDataFromDate(DateTimeInterface $date, PDO $pdo): bool {
    try {
        $dateStr = $date->format('Y-m-d');
        error_log("Mazanie budúcich priradení od dátumu: $dateStr");
        
        $stmtDeleteAssignments = $pdo->prepare("DELETE FROM assignments WHERE assignment_date >= ?");
        $stmtDeleteAssignments->execute([$dateStr]);
        $deletedAssignments = $stmtDeleteAssignments->rowCount();
        error_log("Vymazaných $deletedAssignments záznamov z tabuľky assignments");
        
        $stmtDeleteMetadata = $pdo->prepare("DELETE FROM assignment_metadata WHERE assignment_date >= ?");
        $stmtDeleteMetadata->execute([$dateStr]);
        $deletedMetadata = $stmtDeleteMetadata->rowCount();
        error_log("Vymazaných $deletedMetadata záznamov z tabuľky assignment_metadata");
        
        if (isset($_SESSION['username']) && $_SESSION['username'] === 'radis') {
            error_log("Používateľ 'radis' - spúšťam prepočítanie priradení od $dateStr po dnešok");
            try {
                $today = new DateTimeImmutable('today');
                $currentDate = new DateTimeImmutable($dateStr);
                
                while ($currentDate <= $today) {
                    if (isWorkingDay($currentDate, $pdo)) {
                        error_log("Prepočítavam priradenia pre deň: " . $currentDate->format('Y-m-d'));
                        regenerateAssignmentsForDates($currentDate, $pdo);
                    }
                    $currentDate = $currentDate->modify('+1 day');
                }
            } catch (Exception $e) {
                error_log("Chyba pri prepočítavaní priradení: " . $e->getMessage());
            }
        }
        return true;
    } catch (Exception $e) {
        error_log("Chyba pri mazaní budúcich priradení: " . $e->getMessage());
        return false;
    }
}

function getAssignmentMetadataMeta(DateTimeInterface $date, PDO $pdo): ?array {
    $stmt = $pdo->prepare("SELECT signature, updated_at, last_absence, last_downtime, forced_start_order FROM assignment_metadata WHERE assignment_date = ?");
    $stmt->execute([$date->format('Y-m-d')]);
    return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
}

function upsertAssignmentMetadataMeta(DateTimeInterface $date, string $signature, ?string $lastAbs, ?string $lastDown, PDO $pdo, ?string $forcedStartOrder = null): void {
    $stmt = $pdo->prepare("
      INSERT INTO assignment_metadata (assignment_date, signature, updated_at, last_absence, last_downtime, forced_start_order)
      VALUES (?, ?, NOW(), ?, ?, ?)
      ON DUPLICATE KEY UPDATE signature = VALUES(signature), updated_at = NOW(), last_absence = VALUES(last_absence), last_downtime = VALUES(last_downtime), forced_start_order = VALUES(forced_start_order)
    ");
    $stmt->execute([$date->format('Y-m-d'), $signature, $lastAbs, $lastDown, $forcedStartOrder]);
}

function assignMachineToCandidateInternal(?int $lastMachineAssigned, array $availableMachineNumbers, array $assignedMachineNumbersOnThisRun): ?int {
    $preferredMachine = match ($lastMachineAssigned) { 1 => 2, 2 => 3, 3 => 1, default => 1, };
    $eligibleMachines = array_diff($availableMachineNumbers, $assignedMachineNumbersOnThisRun);
    if (empty($eligibleMachines)) { return null; }
    if (in_array($preferredMachine, $eligibleMachines)) { return $preferredMachine; }
    else { return reset($eligibleMachines); }
}

function getMachineUsageStats(int $userId, PDO $pdo, int $lookback = 2): array {
    $since = (new DateTimeImmutable())->modify("-{$lookback} days")->format('Y-m-d');
    $stmt = $pdo->prepare("SELECT machine_number, COUNT(*) AS c FROM assignments WHERE user_id = :uid AND assignment_date >= :since AND machine_number BETWEEN 1 AND 3 GROUP BY machine_number");
    $stmt->bindValue(':uid',  $userId, PDO::PARAM_INT);
    $stmt->bindValue(':since', $since, PDO::PARAM_STR);
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_KEY_PAIR) ?: [];
}

function assignBalancedMachine(int $userId, ?int $lastMachineAssigned, array $availableMachines, array $assignedToday, PDO $pdo, int $lookback = 3 ): ?int {
    $free = array_values(array_diff($availableMachines, $assignedToday));
    if (!$free) return null;

    $stats = getMachineUsageStats($userId, $pdo, $lookback);
    foreach ([1, 2, 3] as $m) { if (!isset($stats[$m])) $stats[$m] = 0; }

    $candidates = array_intersect_key($stats, array_flip($free));
    asort($candidates);

    $leastUsed = array_keys($candidates, min($candidates), true);
    $preferredNext = match ($lastMachineAssigned) { 1 => 2, 2 => 3, 3 => 1, default => 1, };

    if (in_array($preferredNext, $leastUsed, true)) { return $preferredNext; }
    return reset($leastUsed);
}

function processAssignmentsForDisplay(array $assignments, array $allPresentUsers): array {
    $schiffliederText = '---';
    $skladnik = null;
    $machines = [1 => null, 2 => null, 3 => null];
    $kontrola = [];
    $assignedSchiffId = null;
    $veduciAssignedAsSchiff = false;

    foreach ($assignments as $assignment) {
         if (!is_array($assignment) || !isset($assignment['user_id'])) continue;
         $userId = (int)$assignment['user_id'];
         $role = $assignment['assigned_role'] ?? '';

        if ($role === 'schifflieder') {
            $assignedSchiffId = $userId;
            if (isset($allPresentUsers[$userId]) && $allPresentUsers[$userId]['role1'] === 'veduci') {
                $veduciAssignedAsSchiff = true;
            }
            break;
        }
    }
     $radisUser = null; $domiUser = null;
     foreach ($allPresentUsers as $uid => $uData) {
         if (($uData['username'] ?? '') === 'radis') $radisUser = $uData;
         if (($uData['username'] ?? '') === 'dominika') $domiUser = $uData;
     }

     if ($veduciAssignedAsSchiff && isset($allPresentUsers[$assignedSchiffId])) {
          $schiffliederText = htmlspecialchars($allPresentUsers[$assignedSchiffId]['name']) . ' (Zástup)';
     } elseif ($assignedSchiffId !== null) {
          $assignedName = ($radisUser && $radisUser['id'] == $assignedSchiffId)
                         ? htmlspecialchars($radisUser['name'])
                         : (($domiUser && $domiUser['id'] == $assignedSchiffId) ? htmlspecialchars($domiUser['name']) : '???');
          $otherUser = null;
          if ($radisUser && $radisUser['id'] != $assignedSchiffId) $otherUser = $radisUser;
          elseif ($domiUser && $domiUser['id'] != $assignedSchiffId) $otherUser = $domiUser;
          $schiffliederText = "<strong>" . $assignedName . "</strong>";
          if ($otherUser && isset($allPresentUsers[$otherUser['id']])) {
            $schiffliederText .= "<br>" . htmlspecialchars($otherUser['name']);
          }
     }

    foreach ($assignments as $assignment) {
        if (!is_array($assignment) || !isset($assignment['user_id']) || !isset($assignment['assigned_role'])) continue;
        $userId = (int)$assignment['user_id'];
        $role = $assignment['assigned_role'];
        $machineNum = isset($assignment['machine_number']) ? (int)$assignment['machine_number'] : null;
        $userName = $allPresentUsers[$userId]['name'] ?? $assignment['name'] ?? 'Neznámy (' . $userId . ')';

        switch ($role) {
            case 'skladnik': $skladnik = $userName; break;
            case 'strojnik':
                if ($machineNum !== null && $machineNum >= 1 && $machineNum <= 3) {
                    $machines[$machineNum] = $userName;
                }
                break;
            case 'kontrola': $kontrola[] = $userName; break;
        }
    }
    sort($kontrola);
    return [$schiffliederText, $skladnik, $machines, $kontrola];
}

function regenerateAssignmentsForDates($dates, PDO $pdo): void {
    $dates = is_array($dates) ? $dates : [ $dates ];
    foreach ($dates as $date) {
        try {
            $dstr = $date->format('Y-m-d');
            error_log("Regenerujem priradenia pre deň: $dstr");
            
            $pdo->prepare("DELETE FROM assignments WHERE assignment_date = ?")->execute([$dstr]);
            $pdo->prepare("DELETE FROM assignment_metadata WHERE assignment_date = ?")->execute([$dstr]);
            
            $new_assignments = calculateDailyAssignments($date, $pdo);
            saveAssignments($date, $new_assignments, $pdo);
            
            $sig = md5(json_encode($new_assignments));
            upsertAssignmentMetadataMeta($date, $sig, null, null, $pdo, null);
            
            error_log("Regenerácia priradení pre deň $dstr dokončená");
        } catch (Exception $e) {
            error_log("Chyba pri regenerácii priradení pre deň " . $date->format('Y-m-d') . ": " . $e->getMessage());
        }
    }
}

function getAbsencesForUser(int $userId, PDO $pdo): array {
     try { $stmt = $pdo->prepare("SELECT * FROM absences WHERE user_id = ? ORDER BY start_date DESC"); $stmt->execute([$userId]); return $stmt->fetchAll(PDO::FETCH_ASSOC); }
     catch (PDOException $e) { return []; }
}
function getFutureAbsences(PDO $pdo): array {
    $today = date('Y-m-d');
     try { $stmt = $pdo->prepare("SELECT a.*, u.name as user_name FROM absences a JOIN users u ON a.user_id = u.id WHERE a.end_date >= ? ORDER BY a.start_date ASC, u.name ASC"); $stmt->execute([$today]); return $stmt->fetchAll(PDO::FETCH_ASSOC); }
     catch (PDOException $e) { return []; }
}
function deleteAbsence(int $absenceId, PDO $pdo): bool {
     try { $stmt = $pdo->prepare("DELETE FROM absences WHERE id = ?"); return $stmt->execute([$absenceId]); }
     catch (PDOException $e) { return false; }
}
function getFutureDowntimes(PDO $pdo): array {
     $today = date('Y-m-d');
     try { $stmt = $pdo->prepare("SELECT * FROM machine_downtime WHERE end_date >= ? ORDER BY start_date ASC, machine_number ASC"); $stmt->execute([$today]); return $stmt->fetchAll(PDO::FETCH_ASSOC); }
     catch (PDOException $e) { return []; }
}
function deleteDowntime(int $downtimeId, PDO $pdo): bool {
      try { $stmt = $pdo->prepare("DELETE FROM machine_downtime WHERE id = ?"); return $stmt->execute([$downtimeId]); }
      catch (PDOException $e) { return false; }
}
function getAbsentUsersForDate(DateTimeInterface $date, PDO $pdo) {
    $dateStr = $date->format('Y-m-d');
    $absentUsers = [];
    try {
        $stmt = $pdo->prepare("
            SELECT a.user_id, a.absence_type, u.name as user_name
            FROM absences a
            JOIN users u ON a.user_id = u.id
            WHERE ? BETWEEN a.start_date AND a.end_date
            ORDER BY u.name ASC
        ");
        $stmt->execute([$dateStr]);
        $absentUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Chyba pri získavaní neprítomných používateľov: " . $e->getMessage());
    }
    return $absentUsers;
}

function getHolidaysFromApi(int $year, string $countryCode = 'SK'): array {
    $url = "https://date.nager.at/api/v3/PublicHolidays/{$year}/{$countryCode}";
    $contextOptions = [
        'ssl' => [
            'verify_peer' => true, 'verify_peer_name' => true,
            'cafile' => '/etc/ssl/certs/ca-certificates.crt'
        ],
        'http' => ['timeout' => 10]
    ];
    if (!file_exists($contextOptions['ssl']['cafile'])) {
        error_log("Chyba: CA súbor neexistuje: " . $contextOptions['ssl']['cafile']);
        return [];
    }
    $context = stream_context_create($contextOptions);
    $response = @file_get_contents($url, false, $context); // Potlačenie warningu, chyba sa loguje nižšie
    if ($response === false) {
        $error = error_get_last();
        error_log("Chyba pri načítavaní sviatkov z API: " . ($error['message'] ?? 'Neznáma chyba'));
        return [];
    }
    $holidays = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("Chyba pri dekódovaní JSON odpovede: " . json_last_error_msg());
        return [];
    }
    return array_map(function($holiday) {
        return ['date' => $holiday['date'], 'name' => $holiday['name'], /* ... ostatné polia */ ];
    }, $holidays ?: []); // Pridaná kontrola pre prípad, že $holidays je null
}

function recalculateAssignmentsFromDateToToday(DateTimeInterface $startDate, PDO $pdo): bool {
    try {
        $today = new DateTimeImmutable('today');
        $currentDate = new DateTimeImmutable($startDate->format('Y-m-d'));
        while ($currentDate <= $today) {
            if (isWorkingDay($currentDate, $pdo)) {
                regenerateAssignmentsForDates($currentDate, $pdo);
            }
            $currentDate = $currentDate->modify('+1 day');
        }
        return true;
    } catch (Exception $e) {
        error_log("Chyba pri prepočítavaní priradení od dátumu do dneška: " . $e->getMessage());
        return false;
    }
}

function isActiveMachine(int $machineNumber, DateTimeInterface $date, PDO $pdo): bool {
    $dateStr = $date->format('Y-m-d');
    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM machine_downtime WHERE machine_number = ? AND ? BETWEEN start_date AND end_date");
        $stmt->execute([$machineNumber, $dateStr]);
        return (int)$stmt->fetchColumn() === 0;
    } catch (Exception $e) {
        error_log("Error checking machine status: " . $e->getMessage());
        return true;
    }
}

function insertHolidaysForYearInternal(int $year, PDO $pdo): int {
    $stmtCheck = $pdo->prepare("SELECT COUNT(*) FROM holidays WHERE year = ?"); $stmtCheck->execute([$year]);
    if ($stmtCheck->fetchColumn() > 0) { error_log("Sviatky pre rok $year už sú v DB."); return 0; }

    error_log("Načítavam sviatky pre rok $year z Nager.Date API...");
    $holidays = getHolidaysFromApi($year, 'SK');
    if (empty($holidays)) { error_log("Nepodarilo sa načítať sviatky pre rok $year z API."); return 0; }

    $insertStmt = $pdo->prepare("INSERT IGNORE INTO holidays (year, `date`, localName, name, countryCode) VALUES (?, ?, ?, ?, ?)");
    $insertCount = 0;
    $pdo->beginTransaction();
    try {
        foreach ($holidays as $holiday) {
            if (isset($holiday['date'], $holiday['localName'], $holiday['name'], $holiday['countryCode'])) {
                $d = DateTime::createFromFormat('Y-m-d', $holiday['date']);
                if ($d && $d->format('Y-m-d') === $holiday['date']) {
                     $insertStmt->execute([$year, $holiday['date'], $holiday['localName'], $holiday['name'], $holiday['countryCode']]);
                    if ($insertStmt->rowCount() > 0) $insertCount++;
                } else { error_log("Neplatný formát dátumu '{$holiday['date']}' pre '{$holiday['name']}' v roku $year."); }
            } else { error_log("Neúplné dáta pre sviatok v roku $year: " . print_r($holiday, true)); }
        }
        $pdo->commit();
        error_log("Úspešne vložených sviatkov pre rok $year: $insertCount.");
        return $insertCount;
    } catch (Exception $e) {
        $pdo->rollBack();
        error_log("Chyba pri vkladaní sviatkov pre rok $year: " . $e->getMessage());
        throw $e;
    }
}

function checkAndLoadHolidays(PDO $pdo): array {
    $messages = []; $currentYear = (int)date('Y'); $nextYear = $currentYear + 1;
    $yearsToCheck = [$currentYear, $nextYear];

    foreach ($yearsToCheck as $year) {
        try {
             $stmtCheck = $pdo->prepare("SELECT COUNT(*) FROM holidays WHERE year = ?"); $stmtCheck->execute([$year]);
            if ($stmtCheck->fetchColumn() == 0) {
                $inserted = insertHolidaysForYearInternal($year, $pdo);
                if ($inserted > 0) { $messages[] = "Auto. načítané sviatky pre rok $year ($inserted)."; }
                elseif (empty(getHolidaysFromApi($year, 'SK'))) {
                    $messages[] = "Nepodarilo sa auto. načítať sviatky pre rok $year (API problém?).";
                } else { $messages[] = "Sviatky pre rok $year sa nepodarilo auto. vložiť."; }
            }
        } catch (Exception $e) {
            $messages[] = "Kritická chyba pri spracovaní sviatkov pre rok $year: " . $e->getMessage();
            error_log("FATÁLNA chyba pri spracovaní sviatkov pre rok $year: " . $e->getMessage());
        }
    }
    return $messages;
}

function getHolidaysForMonth($month, $year, PDO $pdo) {
    $startDate = sprintf('%04d-%02d-01', $year, $month);
    $endDate = date('Y-m-t', strtotime($startDate));
    $stmt = $pdo->prepare("SELECT date, localName FROM holidays WHERE date BETWEEN ? AND ? ORDER BY date");
    $stmt->execute([$startDate, $endDate]);
    $holidays = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $holidays[$row['date']] = $row['localName'];
    }
    return $holidays;
}

function getUserAbsencesForMonthWithType(int $userId, int $month, int $year, PDO $pdo): array {
    $absences = [];
    $startDateOfMonth = new DateTimeImmutable("$year-$month-01");
    $endDateOfMonth = $startDateOfMonth->modify('last day of this month');
    $stmt = $pdo->prepare(
        "SELECT start_date, end_date, absence_type FROM absences 
         WHERE user_id = :user_id AND start_date <= :month_end AND end_date >= :month_start"
    );
    $stmt->execute([
        ':user_id' => $userId,
        ':month_start' => $startDateOfMonth->format('Y-m-d'),
        ':month_end' => $endDateOfMonth->format('Y-m-d')
    ]);
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $current = new DateTimeImmutable($row['start_date']);
        $last = new DateTimeImmutable($row['end_date']);
        while ($current <= $last) {
            if ($current->format('n') == $month && $current->format('Y') == $year) {
                $absences[$current->format('Y-m-d')] = $row['absence_type'];
            }
            $current = $current->modify('+1 day');
        }
    }
    return $absences;
}

function getUserVacationsForMonth($userId, $month, $year, PDO $pdo) {
    $startDate = sprintf('%04d-%02d-01', $year, $month);
    $endDate = date('Y-m-t', strtotime($startDate));
    $stmt = $pdo->prepare("
        SELECT a.start_date, a.end_date, a.absence_type FROM absences a
        WHERE a.user_id = ? AND (
            (a.start_date BETWEEN ? AND ?) OR (a.end_date BETWEEN ? AND ?) OR
            (a.start_date <= ? AND a.end_date >= ?)
        ) ORDER BY a.start_date
    ");
    $stmt->execute([$userId, $startDate, $endDate, $startDate, $endDate, $startDate, $endDate]);
    $vacations = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $start = new DateTime($row['start_date']);
        $end = new DateTime($row['end_date']);
        $interval = new DateInterval('P1D');
        $dateRange = new DatePeriod($start, $interval, $end->modify('+1 day'));
        foreach ($dateRange as $date) {
            $dateStr = $date->format('Y-m-d');
            if ($date->format('Y-m') == sprintf('%04d-%02d', $year, $month)) {
                $vacations[$dateStr] = $row['absence_type'];
            }
        }
    }
    return $vacations;
}

?>