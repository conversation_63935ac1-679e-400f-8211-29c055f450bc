<?php
// Zapnutie zobrazenia chýb pre diagnostiku
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config.php';
require_once 'functions.php';

// Kontrola prihlásenia
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$loggedInUserId = $_SESSION['user_id'];

try {
    $loggedInUserData = getUserData($loggedInUserId);
    if (!$loggedInUserData) {
        die('Chyba: Nepodarilo sa načítať údaje používateľa s ID: ' . $loggedInUserId);
    }
} catch (Exception $e) {
    die('Chyba pri načítavaní údajov používateľa: ' . $e->getMessage());
}

// Kontrola oprávnení - len vedúci môže upravovať nastavenia iných používateľov
$isVeduci = in_array('veduci', [$loggedInUserData['role1'], $loggedInUserData['role2'], $loggedInUserData['role3']]);

// Spracovanie POST požiadavky
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $targetUserId = isset($_POST['user_id']) ? (int)$_POST['user_id'] : $loggedInUserId;
    
    // Kontrola oprávnení - používateľ môže upravovať len svoje nastavenia, vedúci môže upravovať všetky
    if ($targetUserId !== $loggedInUserId && !$isVeduci) {
        die('Nemáte oprávnenie upravovať nastavenia iného používateľa.');
    }
    
    $notificationsEnabled = isset($_POST['notifications_enabled']) ? 1 : 0;
    $notificationTime = $_POST['notification_time'] ?? '07:00';
    $workdaysOnly = isset($_POST['workdays_only']) ? 1 : 0;
    
    try {
        // Upsert - vloženie alebo aktualizácia nastavení
        $sql = "INSERT INTO user_notification_settings (user_id, notifications_enabled, notification_time, workdays_only) 
                VALUES (?, ?, ?, ?) 
                ON DUPLICATE KEY UPDATE 
                notifications_enabled = VALUES(notifications_enabled),
                notification_time = VALUES(notification_time),
                workdays_only = VALUES(workdays_only)";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$targetUserId, $notificationsEnabled, $notificationTime, $workdaysOnly]);
        
        $successMessage = "Nastavenia notifikácií boli úspešne uložené.";
    } catch (PDOException $e) {
        $errorMessage = "Chyba pri ukladaní nastavení: " . $e->getMessage();
    }
}

// Diagnostika - skontroluj či existuje tabuľka user_notification_settings
try {
    $checkTable = $pdo->query("SHOW TABLES LIKE 'user_notification_settings'");
    if ($checkTable->rowCount() == 0) {
        die('Chyba: Tabuľka user_notification_settings neexistuje. Spustite najprv SQL skript create_notification_settings_table.sql');
    }
} catch (PDOException $e) {
    die('Chyba pri kontrole tabuľky: ' . $e->getMessage());
}

// Získanie všetkých používateľov (pre vedúceho) alebo len aktuálneho používateľa
try {
    if ($isVeduci) {
        $sql = "SELECT u.id, u.name, u.username, 
                       COALESCE(uns.notifications_enabled, 1) as notifications_enabled,
                       COALESCE(uns.notification_time, '07:00:00') as notification_time,
                       COALESCE(uns.workdays_only, 1) as workdays_only
                FROM users u
                LEFT JOIN user_notification_settings uns ON u.id = uns.user_id
                WHERE u.is_active = 1
                ORDER BY u.name";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } else {
        $sql = "SELECT u.id, u.name, u.username, 
                       COALESCE(uns.notifications_enabled, 1) as notifications_enabled,
                       COALESCE(uns.notification_time, '07:00:00') as notification_time,
                       COALESCE(uns.workdays_only, 1) as workdays_only
                FROM users u
                LEFT JOIN user_notification_settings uns ON u.id = uns.user_id
                WHERE u.id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$loggedInUserId]);
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Diagnostika - skontroluj výsledky
    if (empty($users)) {
        die('Chyba: Nepodarilo sa načítať žiadnych používateľov. Skontrolujte databázu.');
    }
    
} catch (PDOException $e) {
    die('Chyba pri načítavaní používateľov: ' . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nastavenia notifikácií PWA</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .notification-settings {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #1e1e1e;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .user-settings {
            border: 1px solid #444;
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 5px;
            background: #272727;
        }
        .user-name {
            font-weight: bold;
            margin-bottom: 10px;
            color: #e0e0e0;
        }
        .setting-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            gap: 10px;
        }
        .setting-label {
            min-width: 200px;
            font-weight: 500;
            color: #cfcfcf;
        }
        .time-input {
            padding: 5px;
            border: 1px solid #555;
            border-radius: 3px;
            background: #333;
            color: #e0e0e0;
        }
        .save-btn {
            background: #4CAF50;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .save-btn:hover {
            background: #45a049;
        }
        .back-btn {
            background: #666;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 20px;
            text-decoration: none;
            display: inline-block;
        }
        .back-btn:hover {
            background: #555;
        }
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="notification-settings">
        <a href="indexPWA.php" class="back-btn">← Späť na PWA aplikáciu</a>
        
        <h1>Nastavenia notifikácií PWA aplikácie</h1>
        
        <?php if (isset($successMessage)): ?>
            <div class="success-message"><?= htmlspecialchars($successMessage) ?></div>
        <?php endif; ?>
        
        <?php if (isset($errorMessage)): ?>
            <div class="error-message"><?= htmlspecialchars($errorMessage) ?></div>
        <?php endif; ?>
        
        <?php if ($isVeduci): ?>
            <p><strong>Ako vedúci môžete upravovať nastavenia notifikácií pre všetkých používateľov.</strong></p>
        <?php else: ?>
            <p>Tu si môžete upraviť nastavenia notifikácií pre PWA aplikáciu.</p>
        <?php endif; ?>
        
        <?php foreach ($users as $user): ?>
            <div class="user-settings">
                <div class="user-name"><?= htmlspecialchars($user['name']) ?> (<?= htmlspecialchars($user['username']) ?>)</div>
                
                <form method="POST" action="">
                    <input type="hidden" name="user_id" value="<?= $user['id'] ?>">
                    
                    <div class="setting-row">
                        <label class="setting-label">
                            <input type="checkbox" name="notifications_enabled" <?= $user['notifications_enabled'] ? 'checked' : '' ?>>
                            Povoliť notifikácie
                        </label>
                    </div>
                    
                    <div class="setting-row">
                        <label class="setting-label">Čas notifikácie:</label>
                        <input type="time" name="notification_time" value="<?= substr($user['notification_time'], 0, 5) ?>" class="time-input">
                    </div>
                    
                    <div class="setting-row">
                        <label class="setting-label">
                            <input type="checkbox" name="workdays_only" <?= $user['workdays_only'] ? 'checked' : '' ?>>
                            Len v pracovné dni (pondelok-piatok)
                        </label>
                    </div>
                    
                    <button type="submit" class="save-btn">Uložiť nastavenia</button>
                </form>
            </div>
        <?php endforeach; ?>
    </div>
</body>
</html>
