<?php
// indexPWA.php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once 'config.php';

if (!function_exists('getLoggedInUserId')) {
    function getLoggedInUserId() {
        if (isset($_SESSION['user_id'])) {
            return $_SESSION['user_id'];
        }
        return null;
    }
}

if (!function_exists('getUserData')) {
    function getUserData($userId, $pdo_connection) {
        try {
            // Načítame 'role' aj 'role1', aby sme mali obe k dispozícii.
            // Predpokladáme, že pre identifikáciu "Schiffliedra" alebo "Vedúceho" použijeme jednu z nich.
            $sql = "SELECT id, name, role, role1, username FROM users WHERE id = ?";
            $stmt = $pdo_connection->prepare($sql);
            $stmt->execute([$userId]);
            return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
        } catch (PDOException $e) {
            error_log("Error getting user data for ID $userId: " . $e->getMessage());
            return null;
        }
    }
}

// Získame údaje o prihlásenom používateľovi
$loggedInUserId = getLoggedInUserId();

if ($loggedInUserId === null) {
    header("Location: login.php?reason=unauthorized");
    exit();
}

$pdo = getDbConnection();

// Pridajme podrobnejšie logovanie
error_log("Starting status check for user ID: $loggedInUserId");

$dateForStatus = date('Y-m-d'); // Aktuálny deň
// Ak chcete testovať s konkrétnym dátumom:
// $dateForStatus = '2025-05-23';

$realTodayDate = date('Y-m-d');
$datePrefix = ($dateForStatus == $realTodayDate) ? "Dnes" : "Dňa " . date('d.m.Y', strtotime($dateForStatus));

$loggedInUserStatusMessage = "";
$loggedInUserData = getUserData($loggedInUserId, $pdo);

// Podrobné logovanie údajov o používateľovi
error_log("User data retrieved: " . print_r($loggedInUserData, true));

// Určenie všeobecnej roly používateľa.
$loggedInUserGeneralRole = '';
if ($loggedInUserData) {
    if (!empty($loggedInUserData['role1'])) {
        $loggedInUserGeneralRole = strtolower($loggedInUserData['role1']);
    } elseif (!empty($loggedInUserData['role'])) {
        $loggedInUserGeneralRole = strtolower($loggedInUserData['role']);
    }
}

// Debugovanie - zapíšeme hodnoty do logu
error_log("User ID: $loggedInUserId, General Role: $loggedInUserGeneralRole");

// Explicitne nastavíme schiffliedera, ak má túto rolu
if ($loggedInUserGeneralRole === 'schifflieder') {
    error_log("User has schifflieder role - setting status directly");
}

// 1. Skontrolujeme absenciu (má najvyššiu prioritu)
$isAbsent = false;
try {
    $sqlAbsence = "SELECT COUNT(*) FROM absences WHERE user_id = ? AND ? BETWEEN start_date AND end_date";
    $stmtAbsence = $pdo->prepare($sqlAbsence);
    $stmtAbsence->execute([$loggedInUserId, $dateForStatus]);
    $absenceCount = $stmtAbsence->fetchColumn();
    error_log("Absence check result: $absenceCount");
    
    if ($absenceCount > 0) {
        $loggedInUserStatusMessage = "$datePrefix máš voľno.";
        $isAbsent = true;
        error_log("User is absent");
    }
} catch (PDOException $e) {
    error_log("Error checking absence for user ID $loggedInUserId: " . $e->getMessage());
}

// 2. Ak nie je neprítomný, aplikujeme novú logiku
if (!$isAbsent) {
    error_log("User is not absent, checking role assignments");
    
    // PRIORITA 1: Ak je používateľ schifflieder podľa role
    if ($loggedInUserGeneralRole === 'schifflieder') {
        $loggedInUserStatusMessage = "$datePrefix si priradený ako schifflieder.";
        error_log("User is assigned as schifflieder based on general role");
    } 
    // PRIORITA 2: Ak je používateľ priradený ako schifflieder v assignments
    else {
        $isAssignedAsSchifflieder = false;
        try {
            $sqlCheckSchifflieder = "SELECT COUNT(*) FROM assignments 
                                    WHERE user_id = ? AND assignment_date = ? AND assigned_role = 'schifflieder'";
            $stmtCheckSchifflieder = $pdo->prepare($sqlCheckSchifflieder);
            $stmtCheckSchifflieder->execute([$loggedInUserId, $dateForStatus]);
            $schiffliederCount = $stmtCheckSchifflieder->fetchColumn();
            error_log("Schifflieder assignment check result: $schiffliederCount");
            
            if ($schiffliederCount > 0) {
                $loggedInUserStatusMessage = "$datePrefix si priradený ako schifflieder.";
                $isAssignedAsSchifflieder = true;
                error_log("User is assigned as schifflieder in assignments table");
            }
        } catch (PDOException $e) {
            error_log("Error checking schifflieder assignment: " . $e->getMessage());
        }
        
        // PRIORITA 3: Ostatné roly
        if (!$isAssignedAsSchifflieder) {
            error_log("User is not assigned as schifflieder, checking other assignments");
            // Skontrolujeme, či je používateľ priradený ako schifflieder v assignments
            $isAssignedAsSchifflieder = false;
            try {
                $sqlCheckSchifflieder = "SELECT COUNT(*) FROM assignments 
                                        WHERE user_id = ? AND assignment_date = ? AND assigned_role = 'schifflieder'";
                $stmtCheckSchifflieder = $pdo->prepare($sqlCheckSchifflieder);
                $stmtCheckSchifflieder->execute([$loggedInUserId, $dateForStatus]);
                if ($stmtCheckSchifflieder->fetchColumn() > 0) {
                    $loggedInUserStatusMessage = "$datePrefix si priradený ako schifflieder.";
                    $isAssignedAsSchifflieder = true;
                    error_log("User is assigned as schifflieder in assignments table");
                }
            } catch (PDOException $e) {
                error_log("Error checking schifflieder assignment for user ID $loggedInUserId: " . $e->getMessage());
            }

            // Ak nie je priradený ako schifflieder, pokračujeme s ostatnými rolami
            if (!$isAssignedAsSchifflieder) {
                // Logika pre ostatné roly (Vedúci, Skladník, Strojník, Kontrola atď. na základe tabuľky 'assignments')
                $userAssignments = [];
                try {
                    $sqlUserAssignments = "SELECT
                                           assigned_role AS assignment_type,
                                           machine_number,
                                           assignment_order AS `order`
                                       FROM assignments
                                       WHERE user_id = ? AND assignment_date = ?";
                    $stmtUserAssignments = $pdo->prepare($sqlUserAssignments);
                    $stmtUserAssignments->execute([$loggedInUserId, $dateForStatus]);
                    $userAssignments = $stmtUserAssignments->fetchAll(PDO::FETCH_ASSOC);
                } catch (PDOException $e) {
                    error_log("Error fetching assignments for user ID $loggedInUserId on $dateForStatus: " . $e->getMessage());
                }

                $foundExplicitAssignment = false;
                if (!empty($userAssignments)) {
                    // Vedúci - jeho status z 'assignments' má prioritu, ak nejaký má
                    if ($loggedInUserGeneralRole === 'vedúci') {
                        foreach ($userAssignments as $assignment) {
                            if (isset($assignment['assignment_type']) && $assignment['assignment_type'] === 'schifflieder') { // Vedúci môže byť priradený ako Schifflieder
                                $loggedInUserStatusMessage = "$datePrefix si priradený ako schifflieder.";
                                $foundExplicitAssignment = true;
                                break;
                            }
                            if (isset($assignment['assignment_type']) && $assignment['assignment_type'] === 'skladník') { // Vedúci môže byť priradený ako Skladník
                                $loggedInUserStatusMessage = "$datePrefix si priradený ako skladník.";
                                $foundExplicitAssignment = true;
                                break;
                            }
                            // Pridajte ďalšie explicitné roly pre Vedúceho z 'assignments', ak existujú
                        }
                        if (!$foundExplicitAssignment && !empty($userAssignments)) { // Ak má Vedúci iné, nešpecifikované úlohy v 'assignments'
                            $loggedInUserStatusMessage = "$datePrefix máš priradené úlohy.";
                            $foundExplicitAssignment = true;
                        }
                    } else { // Ostatné roly (nie Schifflieder podľa všeobecnej roly, nie Vedúci)
                        foreach ($userAssignments as $assignment) {
                            if (isset($assignment['assignment_type']) && $assignment['assignment_type'] === 'strojnik' && isset($assignment['machine_number']) && $assignment['machine_number'] > 0 && in_array($assignment['machine_number'], [1, 2, 3])) {
                                $loggedInUserStatusMessage = "$datePrefix si priradený na stroj " . htmlspecialchars($assignment['machine_number']) . ".";
                                $foundExplicitAssignment = true;
                                break;
                            }
                            if (isset($assignment['assignment_type']) && $assignment['assignment_type'] === 'kontrola') {
                                $loggedInUserStatusMessage = "$datePrefix si na kontrole.";
                                $foundExplicitAssignment = true;
                                break;
                            }
                            // Schifflieder s explicitným záznamom (ak by nebol pokrytý všeobecnou rolou vyššie, ale teraz je)
                            // if (isset($assignment['assignment_type']) && $assignment['assignment_type'] === 'schifflieder' && isset($assignment['order']) && ($assignment['order'] == 1 || $assignment['order'] == 2) ) {
                            //     $loggedInUserStatusMessage = "$datePrefix si priradený ako schifflieder.";
                            //     $foundExplicitAssignment = true;
                            //     break;
                            // }
                            if (isset($assignment['assignment_type']) && $assignment['assignment_type'] === 'skladník') {
                                $loggedInUserStatusMessage = "$datePrefix si priradený ako skladník.";
                                $foundExplicitAssignment = true;
                                break;
                            }
                            if (isset($assignment['assignment_type']) && $assignment['assignment_type'] === 'strojnik' && isset($assignment['machine_number']) && $assignment['machine_number'] == 0) {
                                $loggedInUserStatusMessage = "$datePrefix si v zozname strojníkov (bez stroja).";
                                $foundExplicitAssignment = true;
                                break;
                            }
                        }
                        if (!$foundExplicitAssignment && !empty($userAssignments)) {
                             $loggedInUserStatusMessage = "$datePrefix máš priradené špeciálne úlohy.";
                             $foundExplicitAssignment = true;
                        }
                    }
                }

                // Ak sa nenašiel žiadny explicitný status z 'assignments' A používateľ nie je Schifflieder podľa všeobecnej roly
                if (!$foundExplicitAssignment && $loggedInUserGeneralRole !== 'schifflieder') {
                    $loggedInUserStatusMessage = "$datePrefix si nepriradený v smene.";
                }
                // Ak je Schifflieder podľa všeobecnej roly, jeho status už bol nastavený na začiatku tohto bloku `if (!$isAbsent)`
            }
        }
    }
}

// Ak po všetkom stále nie je status (čo by sa nemalo stať, ak nie je neprítomný)
if (empty($loggedInUserStatusMessage) && !$isAbsent) {
    $loggedInUserStatusMessage = "$datePrefix si nepriradený v smene (kontrolný pád).";
    error_log("No status assigned, using default message");
}

// FINÁLNA KONTROLA: Ak má používateľ rolu schifflieder a nie je neprítomný, vždy ho označíme ako schiffliedera
if (!$isAbsent && $loggedInUserGeneralRole === 'schifflieder') {
    $loggedInUserStatusMessage = "$datePrefix si priradený ako schifflieder.";
    error_log("Final check: User is schifflieder by role and not absent - overriding any previous status");
}

error_log("Final status message: $loggedInUserStatusMessage");

?>
<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Denný Stav Priradenia</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body { display: flex; flex-direction: column; align-items: center; min-height: 95vh; padding: 15px; margin: 0; text-align: center; }
        .pwa-container { background-color: #1e1e1e; padding: 25px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.5); width: 100%; max-width: 480px; margin-top: 20px; margin-bottom: 20px; }
        .pwa-header { background-color: #272727; color: #fff; padding: 12px; font-size: 1.3em; border-radius: 5px; margin-bottom: 20px; }
        .status-message { font-size: 1.15em; color: #e0e0e0; margin-bottom: 30px; padding: 20px; background-color: #2a2a2a; border: 1px solid #333; border-radius: 5px; min-height: 60px; display: flex; align-items: center; justify-content: center; line-height: 1.4; }
        .logout-button-container { margin-top: 20px; }
        .logout-button { padding: 10px 20px; font-size: 1em; background-color: #555; color: #e0e0e0; border: none; border-radius: 5px; cursor: pointer; transition: background-color 0.3s ease; }
        .logout-button:hover { background-color: #666; }
    </style>
</head>
<body>
    <div class="pwa-container">
        <div class="pwa-header">
            Tvoj Denný Stav
            <?php
            if ($dateForStatus != $realTodayDate) {
                echo " (" . htmlspecialchars(date('d.m.Y', strtotime($dateForStatus))) . ")";
            }
            ?>
        </div>
        <div class="status-message">
            <?php echo htmlspecialchars($loggedInUserStatusMessage); ?>
        </div>
        <div class="logout-button-container">
            <form action="logout.php" method="post" style="margin:0;">
                <button type="submit" class="logout-button">Odhlásiť sa</button>
            </form>
        </div>
    </div>
</body>
</html>
