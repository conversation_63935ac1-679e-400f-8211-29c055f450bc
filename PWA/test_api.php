<?php
require_once 'config.php';

// Kontrola prihlásenia
if (!isset($_SESSION['user_id'])) {
    echo "❌ Nie ste prihlásený. Prihláste sa najprv v PWA aplikácii.";
    exit;
}

$loggedInUserId = $_SESSION['user_id'];

echo "<h2>🔍 Test API pre nastavenia notifikácií</h2>";
echo "<p><strong>Prihlásený používateľ ID:</strong> " . $loggedInUserId . "</p>";

// Test 1: Kontrola tabuľky
echo "<h3>Test 1: Kontrola tabuľky user_notification_settings</h3>";
try {
    $checkTable = $pdo->query("SHOW TABLES LIKE 'user_notification_settings'");
    if ($checkTable->rowCount() > 0) {
        echo "✅ Tabuľka user_notification_settings existuje<br>";
        
        // Zobrazenie štruktúry tabuľky
        $structure = $pdo->query("DESCRIBE user_notification_settings");
        echo "<strong><PERSON>truk<PERSON><PERSON><PERSON> tabuľky:</strong><br>";
        while ($row = $structure->fetch(PDO::FETCH_ASSOC)) {
            echo "- " . $row['Field'] . " (" . $row['Type'] . ")<br>";
        }
    } else {
        echo "❌ Tabuľka user_notification_settings neexistuje<br>";
        echo "<strong>Riešenie:</strong> Spustite SQL skript create_notification_settings_table.sql<br>";
    }
} catch (PDOException $e) {
    echo "❌ Chyba pri kontrole tabuľky: " . $e->getMessage() . "<br>";
}

// Test 2: Kontrola údajov používateľa
echo "<h3>Test 2: Kontrola údajov používateľa</h3>";
try {
    $userSql = "SELECT id, name, username, is_active FROM users WHERE id = ?";
    $userStmt = $pdo->prepare($userSql);
    $userStmt->execute([$loggedInUserId]);
    $userData = $userStmt->fetch(PDO::FETCH_ASSOC);
    
    if ($userData) {
        echo "✅ Používateľ existuje:<br>";
        echo "- ID: " . $userData['id'] . "<br>";
        echo "- Meno: " . $userData['name'] . "<br>";
        echo "- Username: " . $userData['username'] . "<br>";
        echo "- Aktívny: " . ($userData['is_active'] ? 'ÁNO' : 'NIE') . "<br>";
    } else {
        echo "❌ Používateľ s ID $loggedInUserId neexistuje<br>";
    }
} catch (PDOException $e) {
    echo "❌ Chyba pri kontrole používateľa: " . $e->getMessage() . "<br>";
}

// Test 3: Kontrola nastavení notifikácií
echo "<h3>Test 3: Kontrola nastavení notifikácií</h3>";
try {
    $settingsSql = "SELECT * FROM user_notification_settings WHERE user_id = ?";
    $settingsStmt = $pdo->prepare($settingsSql);
    $settingsStmt->execute([$loggedInUserId]);
    $settingsData = $settingsStmt->fetch(PDO::FETCH_ASSOC);
    
    if ($settingsData) {
        echo "✅ Nastavenia notifikácií existujú:<br>";
        echo "- Notifikácie povolené: " . ($settingsData['notifications_enabled'] ? 'ÁNO' : 'NIE') . "<br>";
        echo "- Čas notifikácie: " . $settingsData['notification_time'] . "<br>";
        echo "- Len pracovné dni: " . ($settingsData['workdays_only'] ? 'ÁNO' : 'NIE') . "<br>";
        echo "- Vytvorené: " . $settingsData['created_at'] . "<br>";
        echo "- Aktualizované: " . $settingsData['updated_at'] . "<br>";
    } else {
        echo "❌ Nastavenia notifikácií pre používateľa neexistujú<br>";
        echo "<strong>Riešenie:</strong> Vytvorím predvolené nastavenia...<br>";
        
        // Vytvorenie predvolených nastavení
        $insertSql = "INSERT INTO user_notification_settings (user_id, notifications_enabled, notification_time, workdays_only) VALUES (?, 1, '07:00:00', 1)";
        $insertStmt = $pdo->prepare($insertSql);
        if ($insertStmt->execute([$loggedInUserId])) {
            echo "✅ Predvolené nastavenia vytvorené<br>";
        } else {
            echo "❌ Chyba pri vytváraní predvolených nastavení<br>";
        }
    }
} catch (PDOException $e) {
    echo "❌ Chyba pri kontrole nastavení: " . $e->getMessage() . "<br>";
}

// Test 4: Test API endpoint
echo "<h3>Test 4: Test API endpoint</h3>";
echo "<p><a href='api_notification_settings.php' target='_blank'>Kliknite tu pre test API endpoint</a></p>";

echo "<hr>";
echo "<p><a href='indexPWA.php'>← Späť na PWA aplikáciu</a></p>";
?>
