/* Reset z<PERSON><PERSON><PERSON><PERSON><PERSON> */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  /* Globálne tmavé nastavenia */
  html, body {
    background-color: #121212;
    color: #e0e0e0;
    font-family: 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    font-size: 100%;
    min-height: 100vh;
  }

  /* Odkazy */
  a {
    color: #8ab4f8;
    text-decoration: none; /* Odstránenie podčiarknutia */
  }
  a:hover {
      text-decoration: underline; /* Podčiarknutie pri prejdení my<PERSON> */
  }


  /* Kontajner pre centrálny obsah */
  .container {
    background-color: #1e1e1e;
    margin: 20px auto;
    padding: 20px;
    max-width: 800px; /* <PERSON><PERSON> š<PERSON> kontaj<PERSON> pre dashboard */
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
  }
  /* <PERSON><PERSON><PERSON><PERSON> kont<PERSON> pre prehľady */
  .container-wide {
    max-width: 95%; /* Alebo 98% */
  }


  /* <PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON> */
  .header {
    background-color: #272727;
    color: #ffffff;
    text-align: center;
    padding: 15px;
    font-size: 1.5em;
    border-radius: 5px;
    margin-bottom: 20px;
  }

  /* Štýly pre formuláre */
  form {
    background-color: #1e1e1e;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
  }
  form.inline-form {
      padding: 0;
      background: none;
      margin-bottom: 0;
      border: none;
  }
  form label {
    display: block;
    margin-bottom: 5px;
    color: #cfcfcf;
    font-size: 1em;
  }
  form input[type="text"],
  form input[type="password"],
  form input[type="email"],
  form input[type="date"],
  form input[type="number"],
  form input[type="time"],
  textarea,
  select {
    width: 100%;
    padding: 10px;
    font-size: 1em;
    margin-bottom: 15px;
    background-color: #2a2a2a;
    border: 1px solid #444;
    border-radius: 5px;
    color: #e0e0e0;
  }
  form button {
    width: 100%;
    padding: 12px;
    background-color: #3a3a3a;
    color: #e0e0e0;
    border: none;
    border-radius: 5px;
    font-size: 1.1em;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }
  form button:hover {
    background-color: #555;
  }

  /* Štýl pre chybové a úspešné hlásenia */
  .message {
      padding: 10px 15px;
      margin-bottom: 15px;
      border-radius: 5px;
      text-align: center;
      font-weight: bold;
  }
  .message.success { background-color: #4CAF50; color: white; }
  .message.error { background-color: #f44336; color: white; }
  .message.info { background-color: #2196F3; color: white; }

  /* Navigácia medzi dňami/mesiacmi */
  .navigation {
       text-align: center;
       margin: 20px 0;
       padding: 10px 0;
       border-top: 1px solid #333;
       border-bottom: 1px solid #333;
   }
  .navigation a, .navigation span {
      margin: 0 15px;
      font-size: 1.1em;
      vertical-align: middle;
  }
  .navigation span {
      font-weight: bold;
      color: #fff;
  }

  /* Tlačidlo na generovanie */
  .generate-button-container {
      margin: 25px 0;
      padding: 15px;
      background-color: #272727;
      border-radius: 5px;
      text-align: center;
  }
  .generate-button-container button {
     padding: 12px 25px;
     font-size: 1.1em;
     background-color: #007bff;
     color: white;
     border: none;
     border-radius: 5px;
     cursor: pointer;
     transition: background-color 0.3s ease;
     width: auto;
     display: inline-block;
  }
  .generate-button-container button:hover { background-color: #0056b3; }
  .generate-button-container div { margin-top: 8px; font-size: 0.9em; color: #aaa; }

  /* Sekcie s priradeniami na dashboarde */
  .assignment-section {
      margin-bottom: 30px;
      padding: 20px;
      background-color: #272727;
      border-radius: 8px;
      border: 1px solid #333;
  }
  .assignment-section h3 {
      margin-top: 0;
      margin-bottom: 20px;
      border-bottom: 1px solid #444;
      padding-bottom: 10px;
      font-size: 1.3em;
  }

  /* --- ZJEDNOTENÉ ŠTÝLY PRE TABUĽKY --- */
  .overview-table,
  .monthly-data-table {
      border-collapse: collapse;
      width: 100%;
      table-layout: fixed;
      margin-bottom: 15px;
  }
  .overview-table th,
  .overview-table td,
  .monthly-data-table th,
  .monthly-data-table td {
      border: 1px solid #444;
      padding: 12px 6px;  /* Zvýšené o 50% */
      text-align: center;
      font-size: 1.19em;  /* Zvýšené o 70% z 0.70em */
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      vertical-align: middle;
      min-width: 40px;    /* Zvýšené o 50% */
  }
  .overview-table th,
  .monthly-data-table th {
      background-color: #3a3a3a;
      color: #e0e0e0;
      font-weight: bold;
      position: sticky;
      top: 0;
      z-index: 5; /* Zvýšime z-index pre headery */
      cursor: pointer;
  }

  /* 1. Dátumový stĺpec - ZJEDNOTENÝ */
  .overview-table th.date-cell,
  .overview-table td.date-cell,
  .monthly-data-table th.date-cell,
  .monthly-data-table td.date-cell {
      background: #4a4a4a;
      color: #fff;
      font-weight: bold;
      width: auto; /* ZJEDNOTENÉ: Automatická šírka */
      padding: 12px 3px;   /* Zvýšené o 50% */
      white-space: normal;
      font-size: 1.105em;  /* Zvýšené o 70% z 0.65em */
      text-align: center; /* ZJEDNOTENÉ: Centrovanie */
      cursor: default;
      overflow-wrap: break-word;
      word-break: break-all;
      position: sticky; /* Prilepíme aj dátumovú bunku */
      left: 0; /* Prilepíme ju doľava */
      z-index: 6; /* Vyšší z-index ako hlavička, ale nižší ako dropdown */
  }
  /* Špeciálne pre hlavičku dátumu */
  .overview-table th.date-cell,
  .monthly-data-table th.date-cell {
      z-index: 7; /* Ešte vyššie, aby bola nad ostatnými hlavičkami */
  }


  /* 2. Prihlásený používateľ - ZJEDNOTENÝ */
  .overview-table th.highlight-user,
  .overview-table td.highlight-user,
  .monthly-data-table th.highlight-user,
  .monthly-data-table td.highlight-user {
      background: #555 !important;
      border-left: 2px solid #aaa !important;
      border-right: 2px solid #aaa !important;
       /* Padding zdedený */
  }
  .overview-table th.highlight-user:not(.collapsed-user),
  .overview-table td.highlight-user:not(.collapsed-user),
  .monthly-data-table th.highlight-user:not(.collapsed-user),
  .monthly-data-table td.highlight-user:not(.collapsed-user) {
      width: 20%; /* ZJEDNOTENÉ: Šírka 20% */
      font-size: 1.275em;  /* Zvýšené o 70% z 0.75em */
  }


  /* Zúžený stĺpec - ZJEDNOTENÝ */
  .overview-table th.collapsed-user,
  .overview-table td.collapsed-user,
  .monthly-data-table th.collapsed-user,
  .monthly-data-table td.collapsed-user {
      font-size: 1.02em;  /* Zvýšené o 70% z 0.6em */
      padding: 12px 2px;  /* Zvýšené o 50% */
  }

  /* 3. Ostatné stĺpce - ZJEDNOTENÝ */
  .overview-table th:not(.date-cell):not(.highlight-user),
  .overview-table td:not(.date-cell):not(.highlight-user),
  .monthly-data-table th:not(.date-cell):not(.highlight-user),
  .monthly-data-table td:not(.date-cell):not(.highlight-user) {
      width: auto;
      font-size: 0.65em;
      padding: 6px 1px; /* Zdvojnásobený vertikálny padding */
  }

  /* Rozšírený stĺpec po kliknutí - ZJEDNOTENÝ */
  .overview-table th.expanded,
  .overview-table td.expanded,
  .monthly-data-table th.expanded,
  .monthly-data-table td.expanded {
      background-color: #606060; /* ZJEDNOTENÉ: Farba pozadia */
      width: 20% !important; /* ZJEDNOTENÉ: Šírka 20% */
      white-space: normal;
      font-size: 0.75em;
      text-overflow: clip;
      overflow: visible;
      padding: 8px 3px; /* Zdvojnásobený vertikálny padding */
  }


  /* Nepracovné dni - ZJEDNOTENÉ */
  .overview-table tr.non-working-day td,
  .monthly-data-table tr.non-working-day td {
      background-color: #252525;
  }
  /* Dátumové bunky v nepracovné dni */
  .overview-table tr.non-working-day td.date-cell,
  .monthly-data-table tr.non-working-day td.date-cell {
      background-color: #403030;
      color: #ffaaaa; /* Použijeme jednu */
  }
  /* Zvýraznený používateľ v nepracovný deň */
  .overview-table tr.non-working-day td.highlight-user,
  .monthly-data-table tr.non-working-day td.highlight-user {
      background-color: #4b4b4b !important;
  }
  /* Rozšírený stĺpec v nepracovný deň */
  .overview-table tr.non-working-day td.expanded,
  .monthly-data-table tr.non-working-day td.expanded {
      background-color: #303030; /* Tmavšia sivá */
  }


  /* Aktuálny deň (Today) - ZJEDNOTENÉ */
  .overview-table tr.highlight-today td,
  .monthly-data-table tr.highlight-today td {
      background-color: #383838 !important;
  }
  .overview-table tr.highlight-today td.date-cell,
  .monthly-data-table tr.highlight-today td.date-cell {
      background-color: #555555 !important;
      color: #ffffff !important;
      font-weight: bold;
  }
  /* Aktuálny deň a zároveň nepracovný */
  .overview-table tr.highlight-today.non-working-day td,
  .monthly-data-table tr.highlight-today.non-working-day td {
       background-color: #403A3A !important;
  }
  .overview-table tr.highlight-today.non-working-day td.date-cell,
  .monthly-data-table tr.highlight-today.non-working-day td.date-cell {
       background-color: #614E4E !important;
       color: #FFDDE1 !important;
  }
  /* Rozšírený stĺpec v aktuálny deň */
   .overview-table tr.highlight-today td.expanded,
   .monthly-data-table tr.highlight-today td.expanded {
      background-color: #6a5a50 !important; /* Iný odtieň */
  }
  /* Rozšírený stĺpec v aktuálny nepracovný deň */
   .overview-table tr.highlight-today.non-working-day td.expanded,
   .monthly-data-table tr.highlight-today.non-working-day td.expanded {
      background-color: #504a4a !important; /* Ešte iný odtieň */
  }

  /* Priesečník aktuálneho dňa a prihláseného používateľa - ZJEDNOTENÝ */
  .overview-table td.highlight-intersection,
  .monthly-data-table td.highlight-intersection {
      background-color:#ff3c00 !important;
      color:#ffffff !important;
      font-weight: bold !important;
      border: 1px solid #ffffff !important;
  }


  /* Špecifické pre monthly_overview */
  .overview-table td.status-absence { font-weight: bold; color: #ffcc80; }
  .overview-table td.cell-clickable { cursor: pointer; }
  .overview-table td.cell-clickable:hover { background: #707070; }
  .overview-table td.expanded:hover { background: #686868; }

  /* Špecifické pre schedule_view */
   .monthly-data-table td.expanded:hover { background: #686868; }
  /* .monthly-data-table td.assignment-cell {} */
  /* .monthly-data-table td.no-assignment {} */

  /* --- NOVÉ ŠTÝLY PRE MANUÁLNE PRIRADENIE --- */
  td.manual-assign-cell {
      cursor: pointer;
      position: relative; /* Dôležité pre poziciovanie dropdownu */
  }
  td.manual-assign-cell:hover {
      background-color: #5a5a5a !important; /* Mierne tmavšie pozadie pri prejdení myšou */
      outline: 1px dashed #8ab4f8; /* Modrý prerušovaný rámik */
      outline-offset: -1px;
  }
  /* Štýly pre dropdown menu */
  .machine-dropdown {
      /* Pozícia, farby a border sú nastavené inline v JS, tu môžeme pridať padding atď. */
      padding: 5px 0;
      min-width: 80px; /* Minimálna šírka */
  }
  .machine-dropdown .dropdown-option {
      padding: 8px 12px;
      color: #e0e0e0;
      cursor: pointer;
      font-size: 0.9em;
      white-space: nowrap;
  }
  .machine-dropdown .dropdown-option:hover {
      background-color: #4a4a4a;
  }
  /* Štýl pre možnosť "Zrušiť priradenie" */
  .machine-dropdown .dropdown-option[data-machine="null"] {
      border-top: 1px solid #555;
      color: #ffcccc; /* Svetlo červená */
  }
  .machine-dropdown .dropdown-option[data-machine="null"]:hover {
      background-color: #6f4a4a; /* Tmavšia červená pri hover */
  }
  /* Štýl pre Stroj 0 */
  .machine-dropdown .dropdown-option[data-machine="0"] {
      color: #ffd700; /* Zlatá farba pre odlíšenie */
  }
  /* --- KONIEC NOVÝCH ŠTÝLOV --- */


  /* --- KONIEC ZJEDNOTENÝCH ŠTÝLOV PRE TABUĽKY --- */


  /* Štýly špecifické pre dashboard tabuľku (ak existuje inde) */
  .role-label { font-weight: bold; min-width: 120px; color: #bbb; }
  .kontrola-list { list-style: none; padding-left: 0; }
  .kontrola-list li { margin-bottom: 5px; padding: 3px 0; border-bottom: 1px dotted #444; }
  .kontrola-list li:last-child { border-bottom: none; }
  .kontrola-list li.highlight-logged-user { background-color: #dc3545; color: #ffffff !important; font-weight: bold; padding-left: 5px; margin-left: -5px; border-radius: 3px; }

  /* Nové štýly pre zväčšenie písma v tabuľke priradení */
  .assignments-table td:not(.role-label) {
      font-size: 1.2em; /* Zväčšenie o 20% */
  }

  .assignments-table td.role-label {
      font-size: 1em; /* Zachovanie pôvodnej veľkosti pre názvy rolí */
  }

  /* Zachovanie zvýraznenia prihláseného používateľa */
  .assignments-table .highlight-logged-user {
      background-color: #dc3545;
      color: #ffffff !important;
      font-weight: bold;
      font-size: 1.2em; /* Zväčšenie o 20% */
  }

  /* Tlačidlá pod obsahom */
  .buttons { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #333; }
  .buttons button { margin: 8px; padding: 10px 20px; font-size: 1em; min-width: 150px; background-color: #3a3a3a; color: #e0e0e0; border: none; border-radius: 4px; cursor: pointer; transition: background-color 0.3s ease; width: auto; display: inline-block; }
  .buttons button:hover { background-color: #555; }
  .buttons button.back-button { background-color: #6c757d; }
  .buttons button.back-button:hover { background-color: #5a6268; }

  /* Horizontálne skrolovanie pre tabuľky na menších obrazovkách */
  .table-responsive {
      position: relative;
      overflow: auto;
      height: calc(100vh - 250px); /* Prispôsobte podľa potreby */
  }

  /* Základné nastavenia pre tabuľky */
  .overview-table,
  .monthly-data-table {
      position: relative;
      border-collapse: collapse;
      width: 100%;
      table-layout: fixed;
  }

  /* Nastavenia pre thead a th */
  .overview-table thead,
  .monthly-data-table thead {
      position: sticky;
      top: 0;
      z-index: 10;
      background-color: #121212;
  }

  .overview-table th,
  .monthly-data-table th {
      position: sticky;
      top: 0;
      background-color: #3a3a3a;
      color: #e0e0e0;
      font-weight: bold;
      z-index: 10;
      border: 1px solid #444;
      padding: 6px 1px;
  }

  /* Špeciálne nastavenie pre dátumový stĺpec */
  .overview-table th.date-cell,
  .monthly-data-table th.date-cell {
      position: sticky;
      top: 0;
      left: 0;
      z-index: 11;
      background-color: #4a4a4a;
  }

  /* Pridanie tieňa pod hlavičkou */
  .overview-table thead::after,
  .monthly-data-table thead::after {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      bottom: -5px;
      height: 5px;
      background: linear-gradient(to bottom, rgba(0,0,0,0.2), transparent);
      pointer-events: none;
  }

  /* === Responzívny dizajn === */
  @media only screen and (max-width: 768px) {
    /* Základné zmenšenie písma pre obe tabuľky */
    .overview-table th,
    .overview-table td,
    .monthly-data-table th,
    .monthly-data-table td {
        font-size: 1.105em;  /* Zvýšené o 70% z 0.65em */
        padding: 9px 4.5px;  /* Zvýšené o 50% */
    }
    /* Zjednotený padding pre dátumové bunky */
     .overview-table th.date-cell,
     .overview-table td.date-cell,
     .monthly-data-table th.date-cell,
     .monthly-data-table td.date-cell {
        font-size: 1.105em;  /* Zvýšené o 70% z 0.65em */
        padding: 12px 3px;   /* Zvýšené o 50% */
     }
      .overview-table th.date-cell,
      .monthly-data-table th.date-cell {
           position: sticky; /* Hlavička dátumu zostane sticky hore */
           top: 0;
           left: auto; /* Ale už nie doľava */
       }

     /* Zjednotená šírka pre highlight a expanded */
     .overview-table th.highlight-user:not(.collapsed-user),
     .overview-table td.highlight-user:not(.collapsed-user),
     .monthly-data-table th.highlight-user:not(.collapsed-user),
     .monthly-data-table td.highlight-user:not(.collapsed-user),
     .overview-table th.expanded,
     .overview-table td.expanded,
     .monthly-data-table th.expanded,
     .monthly-data-table td.expanded {
        width: 25% !important; /* Mierne širší na mobiloch */
     }
  }


  @media only screen and (max-width: 600px) {
    .container { margin: 10px; padding: 15px; max-width: calc(100% - 20px); }
    .header { font-size: 1.3em; }
    .navigation a, .navigation span { margin: 0 8px; font-size: 1em; }
    .buttons button { min-width: 120px; font-size: 0.9em; padding: 8px 15px; }

    /* Zjednotené zmenšenie písma a paddingu */
    .overview-table th,
    .overview-table td,
    .monthly-data-table th,
    .monthly-data-table td {
        font-size: 1.02em;  /* Zvýšené o 70% z 0.6em */
        padding: 6px 3px;    /* Zvýšené o 50% */
    }
    /* Zjednotený padding pre dátumové bunky */
    .overview-table th.date-cell,
    .overview-table td.date-cell,
    .monthly-data-table th.date-cell,
    .monthly-data-table td.date-cell {
        font-size: 0.935em;  /* Zvýšené o 70% z 0.55em */
        padding: 6px 3px;    /* Zvýšené o 50% */
        position: relative; /* Zrušíme sticky */
        left: auto;
        /* Odstránené pevné šírky */
    }
     .overview-table th.date-cell,
     .monthly-data-table th.date-cell {
           position: sticky; /* Hlavička dátumu zostane sticky hore */
           top: 0;
           left: auto; /* Ale už nie doľava */
      }

    /* Zjednotená šírka pre highlight a expanded */
    .overview-table th.highlight-user:not(.collapsed-user),
    .overview-table td.highlight-user:not(.collapsed-user),
    .monthly-data-table th.highlight-user:not(.collapsed-user),
    .monthly-data-table td.highlight-user:not(.collapsed-user),
    .overview-table th.expanded,
    .overview-table td.expanded,
     .monthly-data-table th.expanded,
     .monthly-data-table td.expanded {
        width: 30% !important; /* Ešte širší */
        font-size: 0.65em; /* Zjednotená veľkosť písma */
    }
    /* Padding pre expanded bunky (prepíše základný padding pre 600px) */
     .overview-table td.expanded,
     .monthly-data-table td.expanded {
         padding: 8px 3px !important; /* Udržanie väčšieho paddingu z base */
     }
  }

  @media only screen and (max-width: 480px) {
      .navigation span { display: block; margin-bottom: 5px; }

      /* Zjednotené zmenšenie písma */
      .overview-table th,
      .overview-table td,
      .monthly-data-table th,
      .monthly-data-table td {
          font-size: 0.55em;
           /* Padding zdedený z 600px */
      }
      /* Zjednotený dátumový stĺpec */
      .overview-table th.date-cell,
      .overview-table td.date-cell,
      .monthly-data-table th.date-cell,
      .monthly-data-table td.date-cell {
          font-size: 0.5em; /* Upravená veľkosť písma */
           /* Padding zdedený z 600px */
            position: relative; /* Zrušíme sticky */
            left: auto;
      }
        .overview-table th.date-cell,
        .monthly-data-table th.date-cell {
            position: sticky; /* Hlavička dátumu zostane sticky hore */
            top: 0;
            left: auto; /* Ale už nie doľava */
        }
      /* Zjednotená šírka pre highlight a expanded */
       .overview-table th.highlight-user:not(.collapsed-user),
       .overview-table td.highlight-user:not(.collapsed-user),
       .monthly-data-table th.highlight-user:not(.collapsed-user),
       .monthly-data-table td.highlight-user:not(.collapsed-user),
       .overview-table th.expanded,
       .overview-table td.expanded,
       .monthly-data-table th.expanded,
       .monthly-data-table td.expanded {
          width: 35% !important; /* Najširší */
          font-size: 0.6em; /* Zjednotená veľkosť písma */
      }
       /* Padding pre expanded bunky (prepíše základný padding pre 480px) */
        .overview-table td.expanded,
        .monthly-data-table td.expanded {
           padding: 8px 3px !important; /* Udržanie väčšieho paddingu z base */
       }
  }
/* Základné nastavenia buniek - ZJEDNOTENÉ PRE VŠETKY ZARIADENIA */
.overview-table td,
.monthly-data-table td,
.overview-table th,
.monthly-data-table th {
    height: 34px !important;
    padding: 12px 6px !important;  
    font-size: 1.20em !important;   /* Zmenšené na 1.20em */
    text-align: center;
    border: 1px solid #444;
    min-width: 40px;    
}

/* Dátumový stĺpec - ZJEDNOTENÝ PRE VŠETKY ZARIADENIA */
.overview-table th.date-cell,
.overview-table td.date-cell,
.monthly-data-table th.date-cell,
.monthly-data-table td.date-cell {
    height: 34px !important;
    font-size: 1.20em !important;  /* Zmenšené na 1.20em */
    padding: 12px 6px !important;   
    width: 60px !important;         
}

/* Highlight user - ZJEDNOTENÝ PRE VŠETKY ZARIADENIA */
.overview-table th.highlight-user:not(.collapsed-user),
.overview-table td.highlight-user:not(.collapsed-user),
.monthly-data-table th.highlight-user:not(.collapsed-user),
.monthly-data-table td.highlight-user:not(.collapsed-user) {
    height: 34px !important;
    font-size: 1.20em !important;  /* Zmenšené na 1.20em */
    padding: 12px 6px !important;   
}

/* Expanded bunky - ZJEDNOTENÉ PRE VŠETKY ZARIADENIA */
.overview-table th.expanded,
.overview-table td.expanded,
.monthly-data-table th.expanded,
.monthly-data-table td.expanded {
    height: 34px !important;
    font-size: 1.20em !important;  /* Zmenšené na 1.20em */
    padding: 12px 6px !important;   
}

/* Odstránime pôvodné media queries pre tieto vlastnosti, aby sa zachovala jednotná veľkosť */
@media only screen and (max-width: 768px) {
    .table-responsive {
        overflow-x: auto;
    }
}

@media only screen and (max-width: 600px) {
    .table-responsive {
        overflow-x: auto;
    }
}

@media only screen and (max-width: 480px) {
    .table-responsive {
        overflow-x: auto;
    }
}

/* Silný selektor pre vynútenie výšky buniek */
.table-responsive .overview-table td,
.table-responsive .overview-table th,
.table-responsive .monthly-data-table td,
.table-responsive .monthly-data-table th {
    height: 34px !important;
    line-height: 34px !important;
    max-height: 34px !important;
    min-height: 34px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    vertical-align: middle !important;
}

/* Aplikujeme rovnaké pravidlá aj na špeciálne bunky */
.table-responsive .overview-table td.date-cell,
.table-responsive .overview-table th.date-cell,
.table-responsive .monthly-data-table td.date-cell,
.table-responsive .monthly-data-table th.date-cell,
.table-responsive .overview-table td.highlight-user,
.table-responsive .overview-table th.highlight-user,
.table-responsive .monthly-data-table td.highlight-user,
.table-responsive .monthly-data-table th.highlight-user,
.table-responsive .overview-table td.expanded,
.table-responsive .overview-table th.expanded,
.table-responsive .monthly-data-table td.expanded,
.table-responsive .monthly-data-table th.expanded {
    height: 34px !important;
    line-height: 34px !important;
    max-height: 34px !important;
    min-height: 34px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    vertical-align: middle !important;
}

/* Štýly pre mesačný kalendár */
.monthly-calendar-section {
    margin-top: 30px;
    margin-bottom: 20px;
    background-color: #2a2a2a;
    border-radius: 5px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    border: 1px solid #3a3a3a;
}

.monthly-calendar-section h3 {
    text-align: center;
    margin-bottom: 15px;
    font-size: 1.3em;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    border-bottom: 1px solid #444;
    padding-bottom: 8px;
}

.calendar-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 0 10px;
    background-color: #333;
    border-radius: 4px;
    padding: 8px 12px;
}

.calendar-navigation a {
    color: #aaa;
    text-decoration: none;
    font-size: 0.9em;
    transition: color 0.2s;
    padding: 3px 8px;
    border-radius: 3px;
}

.calendar-navigation a:hover {
    color: #fff;
    background-color: #444;
}

.calendar-navigation span {
    font-weight: bold;
    color: #e0e0e0;
    font-size: 1.1em;
}

.calendar-container {
    overflow-x: auto;
    border-radius: 4px;
    border: 1px solid #444;
    background-color: #222;
}

.calendar-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
}

.calendar-table th, 
.calendar-table td {
    border: 1px solid #444;
    text-align: center;
    padding: 8px;
    height: 40px;
    vertical-align: top;
}

.calendar-table th {
    background-color: #3a3a3a;
    color: #e0e0e0;
    font-weight: bold;
    border-bottom: 2px solid #555;
}

.calendar-table td {
    background-color: #333;
    color: #ddd;
    position: relative;
    transition: background-color 0.2s;
}

.calendar-table td:hover {
    background-color: #3d3d3d;
}

.calendar-table td.other-month {
    color: #666;
    background-color: #2a2a2a;
}

.calendar-table td.today {
    background-color: #ff0000;
    box-shadow: inset 0 0 5px rgb(255, 255, 255);
    border: 1px solid #ff0000;
}

.calendar-table td.non-working-day {
    background-color: #353535;
    background-image: linear-gradient(45deg, #333333 25%, #383838 25%, #383838 50%, #333333 50%, #333333 75%, #383838 75%, #383838 100%);
    background-size: 8px 8px;
}

.calendar-table td.holiday {
    background-color: #353535;
    background-image: linear-gradient(45deg, #333333 25%, #383838 25%, #383838 50%, #333333 50%, #333333 75%, #383838 75%, #383838 100%);
    background-size: 8px 8px;
}

.calendar-table td.vacation {
    background-color: #304a30;
    background-image: linear-gradient(45deg, #304a30 25%, #355535 25%, #355535 50%, #304a30 50%, #304a30 75%, #355535 75%, #355535 100%);
    background-size: 8px 8px;
}

.calendar-table td.today.vacation {
    background-color: #405040;
    border: 1px solid #6a9a6a;
    box-shadow: inset 0 0 8px rgba(100, 200, 100, 0.3);
}

.calendar-table td.today.non-working-day {
    background-color: #454040;
    border: 1px solid #777;
    box-shadow: inset 0 0 8px rgba(150, 150, 150, 0.3);
}

.calendar-table td.clickable-day {
    cursor: pointer;
    position: relative;
}

.calendar-table td.clickable-day:hover {
    background-color: #4a4a4a;
    transition: background-color 0.2s;
}

.calendar-table td.clickable-day:hover::after {
    content: "+";
    position: absolute;
    top: 2px;
    right: 2px;
    font-weight: bold;
    color: #4CAF50;
    font-size: 1.2em;
}

.calendar-table td.clickable-day.has-vacation {
    cursor: pointer;
}

.calendar-table td.clickable-day.has-vacation:hover {
    background-color: #553535;
    transition: background-color 0.2s;
}

.calendar-table td.clickable-day.has-vacation:hover::after {
    content: "×";
    position: absolute;
    top: 2px;
    right: 2px;
    font-weight: bold;
    color: #ff5555;
    font-size: 1.2em;
}

.day-number {
    font-weight: bold;
    margin-bottom: 3px;
    font-size: 1.1em;
}

.holiday-name {
    font-size: 0.7em;
    color: #ffaaaa;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.vacation-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    background-color: #4CAF50;
    color: white;
    width: 18px;
    height: 18px;
    line-height: 18px;
    border-radius: 50%;
    font-size: 0.7em;
    font-weight: bold;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.calendar-legend {
    display: flex;
    justify-content: center;
    margin-top: 15px;
    flex-wrap: wrap;
    background-color: #333;
    border-radius: 4px;
    padding: 8px;
}

.legend-item {
    display: flex;
    align-items: center;
    margin: 0 10px;
    font-size: 0.85em;
    color: #ccc;
    padding: 3px 6px;
    border-radius: 3px;
}

.legend-item:hover {
    background-color: #3a3a3a;
}

.legend-color {
    display: inline-block;
    width: 15px;
    height: 15px;
    margin-right: 5px;
    border: 1px solid #555;
    border-radius: 3px;
}

.legend-color.today {
    background-color: #4a4a4a;
    box-shadow: inset 0 0 3px rgba(255, 255, 255, 0.3);
}

.legend-color.non-working-day {
    background-color: #353535;
    background-image: linear-gradient(45deg, #333333 25%, #383838 25%, #383838 50%, #333333 50%, #333333 75%, #383838 75%, #383838 100%);
    background-size: 8px 8px;
}

.legend-color.vacation {
    background-color: #304a30;
    background-image: linear-gradient(45deg, #304a30 25%, #355535 25%, #355535 50%, #304a30 50%, #304a30 75%, #355535 75%, #355535 100%);
    background-size: 8px 8px;
}

/* Responzívne úpravy pre malé obrazovky */
@media (max-width: 768px) {
    .calendar-table th, 
    .calendar-table td {
        padding: 5px;
        font-size: 0.9em;
    }
    
    .holiday-name {
        display: none;
    }
    
    .legend-item {
        margin: 3px 5px;
        font-size: 0.8em;
    }
}

/* --- ŠTÝLY PRE DROPDOWN MENU PRIRADENIA STROJOV --- */
.machine-dropdown {
    padding: 5px 0;
    min-width: 120px;
    z-index: 1000; /* Zvýšime z-index, aby bol nad všetkým */
}

.dropdown-option {
    padding: 8px 12px;
    cursor: pointer;
    color: #e0e0e0;
    transition: background-color 0.2s;
}

.dropdown-option:hover {
    background-color: #444;
}

/* Štýly pre bunky s možnosťou priradenia */
td.manual-assign-cell {
    cursor: pointer;
    position: relative;
}

td.manual-assign-cell:hover {
    background-color: #3a3a3a;
    outline: 1px dashed #8ab4f8; /* Modrý prerušovaný rámik */
    outline-offset: -1px;
}

/* Štýly pre neprítomnosť (N) */
td.absent {
    background-color: #4a2a2a;
    color: #ffcc80;
    font-weight: bold;
}

/* Kombinácia tried */
td.manual-assign-cell.today:hover {
    background-color: #3a4a3a;
}

td.manual-assign-cell.absent:hover {
    background-color: #5a3a3a;
}

/* Štýly pre týždennú tabuľku */
.weekly-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 5px;
    table-layout: fixed;
}

.weekly-table th, 
.weekly-table td {
    border: 1px solid #444;
    padding: 6px 4px;
    text-align: center;
    font-size: 0.9em;
    height: 34px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.weekly-table th {
    background-color: #333;
    color: #fff;
}

/* Oprava zobrazenia čísel strojov */
.weekly-table td {
    color: #e0e0e0; /* Svetlá farba textu pre lepšiu viditeľnosť */
    font-weight: normal; /* Normálna hrúbka písma */
}

/* Zvýraznenie čísel strojov */
.weekly-table td:not(:empty) {
    font-weight: bold; /* Tučné písmo pre neprázdne bunky */
    font-size: 1.1em; /* Mierne zväčšené písmo */
}

/* Extrémne zvýraznenie prihláseného používateľa */
.highlight-logged-user {
    background-color: #dc3545 !important; /* Sýtočervená */
    color: #ffffff !important; /* Biely text */
    font-weight: bold !important;
    font-size: 1.2em !important; /* Zväčšenie o 20% */
    padding: 5px 8px !important; /* Väčší padding pre lepšiu viditeľnosť */
    margin: 3px 0 !important; /* Odstup od ostatných položiek */
    border-radius: 4px !important; /* Zaoblené rohy */
    box-shadow: 0 0 5px rgba(220, 53, 69, 0.7) !important; /* Tieň pre zvýraznenie */
    display: inline-block !important; /* Aby sa aplikovali všetky štýly */
    text-align: center !important; /* Centrovanie textu */
    width: auto !important; /* Automatická šírka podľa obsahu */
}
