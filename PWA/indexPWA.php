<?php
// indexPWA.php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once 'config.php';

if (!function_exists('getLoggedInUserId')) {
    function getLoggedInUserId() {
        if (isset($_SESSION['user_id'])) {
            return $_SESSION['user_id'];
        }
        return null;
    }
}

if (!function_exists('getUserData')) {
    function getUserData($userId, $pdo_connection) {
        try {
            $sql = "SELECT id, name, role, role1, username FROM users WHERE id = ?";
            $stmt = $pdo_connection->prepare($sql);
            $stmt->execute([$userId]);
            return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
        } catch (PDOException $e) {
            error_log("Error getting user data for ID $userId: " . $e->getMessage());
            return null;
        }
    }
}

$loggedInUserId = getLoggedInUserId();

if ($loggedInUserId === null) {
    header("Location: login.php?reason=unauthorized");
    exit();
}

$pdo = getDbConnection();
error_log("Starting status check for user ID: $loggedInUserId");

$dateForStatus = date('Y-m-d');
$realTodayDate = date('Y-m-d');
$datePrefix = ($dateForStatus == $realTodayDate) ? "Dnes" : "Dňa " . date('d.m.Y', strtotime($dateForStatus));

$loggedInUserStatusMessage = "";
$loggedInUserData = getUserData($loggedInUserId, $pdo);
error_log("User data retrieved: " . print_r($loggedInUserData, true));

$isSchifflieder = false;
if ($loggedInUserData) {
    if ((!empty($loggedInUserData['role1']) && strtolower($loggedInUserData['role1']) === 'schifflieder') ||
        (!empty($loggedInUserData['role']) && strtolower($loggedInUserData['role']) === 'schifflieder')) {
        $isSchifflieder = true;
    }
    if (isset($loggedInUserData['username']) &&
        in_array(strtolower($loggedInUserData['username']), ['radis', 'dominika'])) {
        $isSchifflieder = true;
    }
}
error_log("Is user schifflieder: " . ($isSchifflieder ? "Yes" : "No"));

$isAbsent = false;
try {
    $sqlAbsence = "SELECT COUNT(*) FROM absences WHERE user_id = ? AND ? BETWEEN start_date AND end_date";
    $stmtAbsence = $pdo->prepare($sqlAbsence);
    $stmtAbsence->execute([$loggedInUserId, $dateForStatus]);
    $absenceCount = $stmtAbsence->fetchColumn();
    error_log("Absence check result: $absenceCount");
    if ($absenceCount > 0) {
        $loggedInUserStatusMessage = "$datePrefix máš voľno.";
        $isAbsent = true;
        error_log("User is absent");
    }
} catch (PDOException $e) {
    error_log("Error checking absence for user ID $loggedInUserId: " . $e->getMessage());
}

if (!$isAbsent && $isSchifflieder) {
    $loggedInUserStatusMessage = "$datePrefix si priradený ako schifflieder.";
    error_log("User is schifflieder (general) and not absent - setting schifflieder status");
} elseif (!$isAbsent) {
    error_log("User is not absent, and not a general schifflieder, checking assignments table.");
    $isAssignedAsSchiffliederInAssignments = false;
    try {
        $sqlCheckSchifflieder = "SELECT COUNT(*) FROM assignments
                                WHERE user_id = ? AND assignment_date = ? AND assigned_role = 'schifflieder'";
        $stmtCheckSchifflieder = $pdo->prepare($sqlCheckSchifflieder);
        $stmtCheckSchifflieder->execute([$loggedInUserId, $dateForStatus]);
        if ($stmtCheckSchifflieder->fetchColumn() > 0) {
            $loggedInUserStatusMessage = "$datePrefix si priradený ako schifflieder.";
            $isAssignedAsSchiffliederInAssignments = true;
            error_log("User is assigned as schifflieder in assignments table");
        }
    } catch (PDOException $e) {
        error_log("Error checking schifflieder assignment in assignments table: " . $e->getMessage());
    }

    if (!$isAssignedAsSchiffliederInAssignments) {
        error_log("User is not assigned as schifflieder in assignments, checking other specific assignments");
        $userAssignments = [];
        try {
            $sqlUserAssignments = "SELECT assigned_role AS assignment_type, machine_number
                               FROM assignments
                               WHERE user_id = ? AND assignment_date = ?";
            $stmtUserAssignments = $pdo->prepare($sqlUserAssignments);
            $stmtUserAssignments->execute([$loggedInUserId, $dateForStatus]);
            $userAssignments = $stmtUserAssignments->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error fetching other assignments for user ID $loggedInUserId on $dateForStatus: " . $e->getMessage());
        }

        $foundExplicitAssignment = false;
        if (!empty($userAssignments)) {
            foreach ($userAssignments as $assignment) {
                if (isset($assignment['assignment_type']) && $assignment['assignment_type'] === 'strojnik' && isset($assignment['machine_number']) && $assignment['machine_number'] > 0 && in_array($assignment['machine_number'], [1, 2, 3])) {
                    $loggedInUserStatusMessage = "$datePrefix si priradený na stroj " . htmlspecialchars($assignment['machine_number']) . ".";
                    $foundExplicitAssignment = true;
                    break;
                }
                if (isset($assignment['assignment_type']) && $assignment['assignment_type'] === 'kontrola') {
                    $loggedInUserStatusMessage = "$datePrefix si na kontrole.";
                    $foundExplicitAssignment = true;
                    break;
                }
                if (isset($assignment['assignment_type']) && $assignment['assignment_type'] === 'skladník') {
                    $loggedInUserStatusMessage = "$datePrefix si priradený ako skladník.";
                    $foundExplicitAssignment = true;
                    break;
                }
                if (isset($assignment['assignment_type']) && $assignment['assignment_type'] === 'strojnik' && isset($assignment['machine_number']) && $assignment['machine_number'] == 0) {
                    $loggedInUserStatusMessage = "$datePrefix si v zozname strojníkov (bez stroja).";
                    $foundExplicitAssignment = true;
                    break;
                }
            }
            if (!$foundExplicitAssignment && !empty($userAssignments)) {
                 $loggedInUserStatusMessage = "$datePrefix máš priradené špeciálne úlohy.";
                 $foundExplicitAssignment = true;
            }
        }
        if (!$foundExplicitAssignment) {
            $loggedInUserStatusMessage = "$datePrefix si nepriradený v smene.";
            error_log("User has no specific assignment in assignments table.");
        }
    }
}

// Ak je používateľ stále nepriradený po všetkých kontrolách (a nie je neprítomný, a nie je generálny schifflieder)
if (empty($loggedInUserStatusMessage) && !$isAbsent && !$isSchifflieder) {
    $loggedInUserStatusMessage = "$datePrefix si nepriradený v smene.";
    error_log("Fallback: User is not absent, not general schifflieder, and no specific assignment found. Setting to 'unassigned'.");
}

// Finálna kontrola pre generálneho schiffliedera (ak nebol neprítomný)
if (!$isAbsent && $isSchifflieder) {
    // Ak je $isSchifflieder true, jeho status by už mal byť nastavený na začiatku.
    // Toto je skôr poistka alebo explicitné potvrdenie.
    $loggedInUserStatusMessage = "$datePrefix si priradený ako schifflieder.";
    error_log("Final override check: User is general schifflieder and not absent. Ensured status is 'schifflieder'.");
}

error_log("Final status message for user $loggedInUserId: $loggedInUserStatusMessage");

?>
<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Denný Stav Priradenia</title>
    <link rel="stylesheet" href="style.css"> <link rel="manifest" href="manifest.json">

    <meta name="theme-color" content="#272727">

    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="NPC Smena">
    <meta name="apple-touch-fullscreen" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-orientation" content="portrait">

    <!-- Android Chrome špecifické meta tagy -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="application-name" content="NPC Smena">
    <meta name="msapplication-TileColor" content="#272727">
    <meta name="msapplication-TileImage" content="images/icon-192x192.png">

    <link rel="apple-touch-icon" href="images/icon-192x192.png">
    <link rel="apple-touch-icon" sizes="192x192" href="images/icon-192x192.png">
    <link rel="apple-touch-icon" sizes="512x512" href="images/icon-512x512.png">
    <link rel="icon" type="image/png" sizes="192x192" href="images/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="images/icon-512x512.png">
    <style>
        body { display: flex; flex-direction: column; align-items: center; justify-content: center; min-height: 100vh; padding: 15px; margin: 0; text-align: center; background-color: #121212; color: #e0e0e0; }
        .pwa-container { background-color: #1e1e1e; padding: 25px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.5); width: 100%; max-width: 480px; margin-top: 20px; margin-bottom: 20px; }
        .pwa-header { background-color: #272727; color: #fff; padding: 12px; font-size: 1.3em; border-radius: 5px; margin-bottom: 20px; }
        .status-message { font-size: 1.15em; color: #e0e0e0; margin-bottom: 30px; padding: 20px; background-color: #2a2a2a; border: 1px solid #333; border-radius: 5px; min-height: 60px; display: flex; align-items: center; justify-content: center; line-height: 1.4; }
        .logout-button-container { margin-top: 20px; }
        .logout-button { padding: 10px 20px; font-size: 1em; background-color: #555; color: #e0e0e0; border: none; border-radius: 5px; cursor: pointer; transition: background-color 0.3s ease; }
        .logout-button:hover { background-color: #666; }
    </style>
</head>
<body>
    <div class="pwa-container">
        <div class="pwa-header">
            Tvoj Denný Stav
            <?php
            if ($dateForStatus != $realTodayDate) {
                echo " (" . htmlspecialchars(date('d.m.Y', strtotime($dateForStatus))) . ")";
            }
            ?>
        </div>
        <div class="status-message">
            <?php echo htmlspecialchars($loggedInUserStatusMessage); ?>
        </div>
        <div class="logout-button-container">
            <button onclick="showWorkAssignmentNotification()" class="logout-button" style="margin-bottom: 10px; background-color: #2196f3;">
                🔔 Test notifikácie
            </button>
            <button onclick="showCurrentSettings()" class="logout-button" style="margin-bottom: 10px; background-color: #9C27B0;">
                📋 Zobraziť nastavenia
            </button>
            <button onclick="location.href='notification_settings.php'" class="logout-button" style="margin-bottom: 10px; background-color: #ff9800;">
                ⚙️ Nastavenia notifikácií
            </button>
            <form action="logout.php" method="post" style="margin:0;">
                <button type="submit" class="logout-button">Odhlásiť sa</button>
            </form>
        </div>
    </div>

    <script>
        // Registrácia Service Workera
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('./service-worker.js') // Opravená cesta
                    .then(registration => {
                        console.log('[PWA] ServiceWorker Registration successful with scope: ', registration.scope);
                    })
                    .catch(error => {
                        console.error('[PWA] ServiceWorker Registration failed: ', error);
                    });
            });
        }

        // Globálne nastavenia notifikácií
        let notificationSettings = {
            notifications_enabled: true,
            notification_time: '07:00',
            workdays_only: true
        };

        // Načítanie nastavení notifikácií z databázy
        async function loadNotificationSettings() {
            try {
                const response = await fetch('./api_notification_settings.php');
                const data = await response.json();
                if (data.success) {
                    notificationSettings = data.settings;
                    console.log('Nastavenia notifikácií načítané:', notificationSettings);
                }
            } catch (error) {
                console.error('Chyba pri načítaní nastavení notifikácií:', error);
            }
        }

        // Funkcia pre notifikácie
        async function requestNotificationPermission() {
            if ('Notification' in window && notificationSettings.notifications_enabled) {
                const permission = await Notification.requestPermission();
                if (permission === 'granted') {
                    console.log('Notifikácie povolené');
                    scheduleWorkDayNotifications();
                } else {
                    console.log('Notifikácie zamietnuté');
                }
            }
        }

        // Plánovanie notifikácií podľa nastavení používateľa
        function scheduleWorkDayNotifications() {
            if (!notificationSettings.notifications_enabled) {
                console.log('Notifikácie sú vypnuté v nastaveniach');
                return;
            }

            console.log('Plánovanie notifikácií zapnuté');
            console.log('Nastavenia:', notificationSettings);

            // Kontrola každých 10 sekúnd pre testovanie
            setInterval(() => {
                const now = new Date();
                const currentTime = now.getHours().toString().padStart(2, '0') + ':' +
                                   now.getMinutes().toString().padStart(2, '0');
                const day = now.getDay(); // 0 = nedeľa, 1 = pondelok, ..., 6 = sobota

                // Kontrola času notifikácie
                const isNotificationTime = currentTime === notificationSettings.notification_time;

                // Kontrola pracovných dní ak je to nastavené
                const isWorkDay = notificationSettings.workdays_only ? (day >= 1 && day <= 5) : true;

                console.log(`Kontrola: ${currentTime}, Nastavený čas: ${notificationSettings.notification_time}, Zhoduje sa: ${isNotificationTime}, Pracovný deň: ${isWorkDay}`);

                if (isNotificationTime && isWorkDay) {
                    console.log('Zobrazujem automatickú notifikáciu!');
                    showWorkAssignmentNotification();
                }
            }, 10000); // Kontrola každých 10 sekúnd pre testovanie
        }

        // Zobrazenie notifikácie s pracovným priradením
        function showWorkAssignmentNotification() {
            const statusMessage = <?php echo json_encode($loggedInUserStatusMessage); ?>;
            const userName = <?php echo json_encode($loggedInUserData['name'] ?? 'Používateľ'); ?>;

            console.log('Test notifikácie - spúšťam...');
            console.log('Notification permission:', Notification.permission);
            console.log('Používateľ:', userName);
            console.log('Status:', statusMessage);

            if ('Notification' in window) {
                if (Notification.permission === 'granted') {
                    console.log('Zobrazujem notifikáciu...');
                    new Notification('NPC - Pracovné priradenie', {
                        body: `${userName}: ${statusMessage}`,
                        icon: './images/icon-192x192.png',
                        badge: './images/icon-192x192.png',
                        tag: 'work-assignment',
                        requireInteraction: true
                    });
                } else if (Notification.permission === 'default') {
                    console.log('Vyžiadam povolenie notifikácií...');
                    Notification.requestPermission().then(permission => {
                        if (permission === 'granted') {
                            showWorkAssignmentNotification();
                        } else {
                            alert('Notifikácie sú zakázané. Povoľte ich v nastaveniach prehliadača.');
                        }
                    });
                } else {
                    alert('Notifikácie sú zakázané. Povoľte ich v nastaveniach prehliadača.');
                }
            } else {
                alert('Váš prehliadač nepodporuje notifikácie.');
            }
        }

        // Zobrazenie aktuálnych nastavení notifikácií
        function showCurrentSettings() {
            const now = new Date();
            const currentTime = now.getHours().toString().padStart(2, '0') + ':' +
                               now.getMinutes().toString().padStart(2, '0');
            const day = now.getDay();
            const dayNames = ['Nedeľa', 'Pondelok', 'Utorok', 'Streda', 'Štvrtok', 'Piatok', 'Sobota'];

            let settingsText = `📋 AKTUÁLNE NASTAVENIA:\n\n`;
            settingsText += `⏰ Aktuálny čas: ${currentTime}\n`;
            settingsText += `📅 Dnes je: ${dayNames[day]}\n\n`;

            if (notificationSettings) {
                settingsText += `🔔 Notifikácie: ${notificationSettings.notifications_enabled ? 'ZAPNUTÉ' : 'VYPNUTÉ'}\n`;
                settingsText += `⏰ Čas notifikácie: ${notificationSettings.notification_time}\n`;
                settingsText += `📅 Len pracovné dni: ${notificationSettings.workdays_only ? 'ÁNO' : 'NIE'}\n\n`;

                const isWorkDay = notificationSettings.workdays_only ? (day >= 1 && day <= 5) : true;
                settingsText += `✅ Dnes je pracovný deň: ${isWorkDay ? 'ÁNO' : 'NIE'}\n`;
                settingsText += `🔔 Notification permission: ${Notification.permission}\n`;
            } else {
                settingsText += `❌ Nastavenia sa nenačítali`;
            }

            alert(settingsText);
        }

        // Automatické vyžiadanie povolenia pre notifikácie
        window.addEventListener('load', async () => {
            // Najprv načítame nastavenia notifikácií
            await loadNotificationSettings();

            // Potom vyžiadame povolenie ak sú notifikácie povolené
            setTimeout(() => {
                requestNotificationPermission();
            }, 2000);
        });
    </script>
</body>
</html>