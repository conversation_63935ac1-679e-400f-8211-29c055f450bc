<?php
require_once 'config.php';
require_once 'functions.php';
requireLogin();
$pdo = getDbConnection();
$message = ''; $error = '';
$today = new DateTimeImmutable('today'); // Dnešný dátum

// --- KĽÚČOVÁ ZMENA: Načítanie aktívneho oddelenia ---
$activeOddelenieId = $_SESSION['selected_oddelenie_id'] ?? $_SESSION['oddelenie_id'] ?? null;
$activeOddelenieNazov = $_SESSION['selected_oddelenie_nazov'] ?? $_SESSION['oddelenie_nazov'] ?? null;

// Kontrola, či je oddelenie zvolené
if ($activeOddelenieId === null) {
    $_SESSION['message'] = "Prosím, vyberte oddelenie na Dashboarde pre správu odstávok strojov.";
    header("Location: dashboard.php");
    exit;
}

// Kontrola oprávnení - len vedúci alebo schifflieder môžu spravovať stroje
$loggedUser = getUserById($_SESSION['user_id'], $pdo);
if (!in_array($loggedUser['role1'] ?? '', ['veduci', 'schifflieder'], true)) {
    $_SESSION['error'] = "Nemáte oprávnenie na prístup k tejto stránke.";
    header("Location: dashboard.php"); // Alebo iná vhodná stránka
    exit;
}

// Spracovanie formulára na pridanie novej odstávky
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_downtime'])) {
    $machine_number = filter_input(INPUT_POST, 'machine_number', FILTER_VALIDATE_INT);
    $start_date_str = filter_input(INPUT_POST, 'start_date');
    $end_date_str = filter_input(INPUT_POST, 'end_date');
    $reason = trim(filter_input(INPUT_POST, 'reason', FILTER_SANITIZE_STRING));

    // Určenie maximálneho počtu strojov pre dané oddelenie
    $max_machines_for_department = 3; // Predvolené
    if ($activeOddelenieNazov === 'ID3') { // Použijeme aktívny názov oddelenia
        $max_machines_for_department = 2;
    } // Pridajte ďalšie oddelenia podľa potreby

    $valid_machines = range(1, $max_machines_for_department);

    if (empty($machine_number) || !in_array($machine_number, $valid_machines) || empty($start_date_str) || empty($end_date_str)) {
        $error = "Musíte vybrať platný stroj pre vaše oddelenie a zadať platné dátumy (Od aj Do).";
    } else {
        $start_date = null; $end_date = null;
        try {
            $start_date = new DateTimeImmutable($start_date_str);
            $end_date = new DateTimeImmutable($end_date_str);
        } catch (Exception $e) { $error = "Neplatný formát zadaného dátumu."; }
        if ($start_date && $end_date && $end_date < $start_date) {
            $error = "Dátum ukončenia nemôže byť skôr ako dátum začiatku.";
        }
        // --- ZAČIATOK ZMIEN: Kontrola minulosti pri pridávaní ---
        elseif ($start_date && $end_date && $end_date < $today) {
            $error = 'Nie je možné pridávať odstávky, ktoré celé patria do minulosti.';
        }
        // --- KONIEC ZMIEN ---
        elseif ($start_date && $end_date) {
             $pdo->beginTransaction();
             try {
                // 1. Vložíme odstávku
                $stmt = $pdo->prepare("INSERT INTO machine_downtime (machine_number, start_date, end_date, reason, oddelenie_id) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute([ $machine_number, $start_date->format('Y-m-d'), $end_date->format('Y-m-d'), $reason ?: null, $activeOddelenieId ]); // Použijeme activeOddelenieId
                $inserted = $stmt->rowCount() > 0;

                // 2. Ak sa vložilo, zmažeme budúce dáta od začiatku odstávky
                if ($inserted) {
                    $deleteSuccess = deleteFutureDataFromDate($start_date, $activeOddelenieId, $pdo); // Použijeme activeOddelenieId
                    if (!$deleteSuccess) {
                        throw new Exception("Odstávka bola pridaná, ale nepodarilo sa zmazať budúce priradenia.");
                    }
                } else {
                     throw new Exception("Záznam odstávky sa nepodarilo vložiť.");
                }

                $pdo->commit();
                $_SESSION['message'] = "Odstávka stroja $machine_number od " . $start_date->format('d.m.Y') . " do " . $end_date->format('d.m.Y') . " bola úspešne pridaná.";
                header("Location: machines.php");
                exit;

            } catch (PDOException $e) {
                if ($pdo->inTransaction()) $pdo->rollBack();
                $errorCode = $e->getCode(); $errorMessage = $e->getMessage();
                error_log("DB Error adding downtime: Code=$errorCode, Message=$errorMessage");
                $error = "Nastala databázová chyba pri pridávaní odstávky.";
            } catch (Exception $e) {
                 if ($pdo->inTransaction()) $pdo->rollBack();
                 error_log("General Error adding downtime process: " . $e->getMessage());
                 $error = "Nastala chyba pri spracovaní: " . $e->getMessage();
            }
        }
    }
}

// Spracovanie požiadavky na mazanie odstávky (cez GET parameter)
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['delete_id'])) {
    $delete_id = filter_input(INPUT_GET, 'delete_id', FILTER_VALIDATE_INT);
    if ($delete_id && $delete_id > 0) {
        $pdo->beginTransaction();
        try {
            // --- ZAČIATOK ZMIEN: Načítame aj end_date ---
            // 1. Načítame odstávku z DB a overíme, či patrí k aktuálnemu oddeleniu
            $stmtGet = $pdo->prepare("SELECT id, start_date, end_date, oddelenie_id FROM machine_downtime WHERE id = ?");
            // --- KONIEC ZMIEN ---
            $stmtGet->execute([$delete_id]);
            $downtime = $stmtGet->fetch(PDO::FETCH_ASSOC);

            if ($downtime) {
                $start_date_obj = null; $end_date_obj = null;
                try {
                    // Overenie, či odstávka patrí k oddeleniu prihláseného používateľa
                    if ((int)$downtime['oddelenie_id'] !== (int)$activeOddelenieId) { // Použijeme activeOddelenieId
                        $_SESSION['error'] = "Nemáte oprávnenie mazať odstávku iného oddelenia.";
                        header("Location: machines.php");
                        exit;
                    }

                    $start_date_obj = new DateTimeImmutable($downtime['start_date']);
                    $end_date_obj = new DateTimeImmutable($downtime['end_date']); // Načítame aj end_date
                } catch (Exception $e) {
                    // Logujeme, ale môžeme pokračovať, ak $start_date_obj je platný pre mazanie od budúcnosti
                    error_log("Invalid date format for downtime ID $delete_id during deletion.");
                }

                // --- ZAČIATOK ZMIEN: Kontrola minulosti a logika delenia ---
                if ($end_date_obj && $end_date_obj < $today) {
                     $pdo->commit(); // Nič sa nemení, len ukončíme transakciu
                     $_SESSION['error'] = "Nie je možné mazať odstávky, ktoré už skončili v minulosti.";
                     header("Location: machines.php");
                     exit;
                }

                // 2. Rozhodni, či mazať alebo skrátiť
                $deletedOrShortened = false;
                 if ($start_date_obj && $start_date_obj < $today) {
                     // Odstávka začala v minulosti, treba ju skrátiť
                     $yesterday = $today->modify('-1 day');
                     if ($end_date_obj > $yesterday) { // Skrátime, len ak naozaj zasahuje do dneška/budúcnosti
                         $stmtUpdate = $pdo->prepare("UPDATE machine_downtime SET end_date = ? WHERE id = ?");
                         $stmtUpdate->execute([$yesterday->format('Y-m-d'), $delete_id]);
                         // Mažeme budúce dáta od DNES
                     $deleteFutureSuccess = deleteFutureDataFromDate($today, $activeOddelenieId, $pdo); // Použijeme activeOddelenieId
                         if (!$deleteFutureSuccess) throw new Exception("Odstávka bola skrátená, ale nepodarilo sa vymazať budúce priradenia.");
                         $_SESSION['message'] = "Odstávka stroja bola skrátená (ukončená včera).";
                         $deletedOrShortened = true;
                     } else {
                          // Nemá zmysel mazať/upravovať, lebo už skončila
                          $_SESSION['error'] = "Odstávka už skončila v minulosti, nebola upravená.";
                          $deletedOrShortened = false; // Explicitne, aj keď už bola
                     }
                 } elseif ($start_date_obj) {
                     // Odstávka začína dnes alebo v budúcnosti, môžeme ju zmazať celú
                     $stmtDelete = $pdo->prepare("DELETE FROM machine_downtime WHERE id = ?");
                     $stmtDelete->execute([$delete_id]);
                     if ($stmtDelete->rowCount() > 0) {
                         // Mažeme budúce dáta od PÔVODNÉHO ZAČIATKU odstávky
                         $deleteFutureSuccess = deleteFutureDataFromDate($start_date_obj, $activeOddelenieId, $pdo); // Použijeme activeOddelenieId
                         if (!$deleteFutureSuccess) throw new Exception("Odstávka bola zmazaná, ale nepodarilo sa vymazať budúce priradenia.");
                         $_SESSION['message'] = "Odstávka stroja bola úspešne odstránená.";
                         $deletedOrShortened = true;
                     } else {
                          $_SESSION['error'] = "Nepodarilo sa odstrániť odstávku (ID: $delete_id). Možno už bola zmazaná.";
                     }
                 } else {
                     // Neplatný dátum začiatku - nemôžeme bezpečne pokračovať
                      throw new Exception("Neplatný dátum začiatku pre odstávku ID $delete_id.");
                 }
                // --- KONIEC ZMIEN ---

            } else {
                 $_SESSION['error'] = "Odstávka s ID $delete_id nebola nájdená.";
            }
            // Commit len ak nenastala chyba
            $pdo->commit();

        } catch (Exception $e) {
            if ($pdo->inTransaction()) $pdo->rollBack();
            error_log("Error during downtime deletion process for ID $delete_id: " . $e->getMessage());
            $_SESSION['error'] = "Nastala neočakávaná chyba pri spracovaní mazania odstávky: " . $e->getMessage();
        }
        // Presmerujeme späť
        header("Location: machines.php");
        exit;
    } else {
        $_SESSION['error'] = "Neplatné ID pre mazanie odstávky.";
        header("Location: machines.php");
        exit;
    }
}

// Získanie správ zo session (po presmerovaní)
if (isset($_SESSION['message'])) { $message = $_SESSION['message']; unset($_SESSION['message']); }
if (isset($_SESSION['error'])) { $error = $_SESSION['error']; unset($_SESSION['error']); }

// Načítanie budúcich odstávok
$futureDowntimes = getFutureDowntimes($pdo, $activeOddelenieId); // Použijeme activeOddelenieId
$today_for_input = date('Y-m-d');

?>

<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Nastavenie Strojov - Odstávky</title>
    <link rel="stylesheet" href="style.css">
    <style>
        /* Štýly zostávajú rovnaké */
        .downtime-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .downtime-table th, .downtime-table td { border: 1px solid #444; padding: 8px 12px; text-align: left; }
        .downtime-table th { background-color: #3a3a3a; }
        .downtime-table td a { color: #ff8c8c; }
        .downtime-table td a:hover { color: #ff4d4d; text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">Nastavenie Strojov - Odstávky (Oddelenie: <?php echo htmlspecialchars($activeOddelenieNazov); ?>)</div>

        <?php if ($message): ?>
            <div class="message success"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>
        <?php if ($error): ?>
            <div class="message error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <form action="machines.php" method="POST">
            <h3>Pridať Novú Odstávku</h3>

            <label for="machine_number">Stroj:</label>
            <select id="machine_number" name="machine_number" required>
                <option value="">--- Vyberte stroj ---</option>
                <?php
                    $num_machines = 3; // Predvolené
                    if ($activeOddelenieNazov === 'ID3') { // Použijeme aktívny názov oddelenia
                        $num_machines = 2;
                    }
                    // Pridajte ďalšie podmienky pre iné oddelenia
                    for ($i = 1; $i <= $num_machines; $i++):
                ?>
                    <option value="<?php echo $i; ?>">Stroj <?php echo $i; ?></option>
                <?php endfor; ?>
            </select>

            <label for="start_date">Dátum začiatku:</label>
            <input type="date" id="start_date" name="start_date" value="<?php echo $today_for_input; ?>" required>

            <label for="end_date">Dátum ukončenia:</label>
            <input type="date" id="end_date" name="end_date" value="<?php echo $today_for_input; ?>" required>

            <label for="reason">Dôvod (voliteľné):</label>
            <input type="text" id="reason" name="reason" maxlength="255" placeholder="Napr. Údržba, Porucha...">

            <button type="submit" name="add_downtime">Pridať Odstávku</button>
        </form>

        <hr style="border-color: #444; margin: 30px 0;">

        <h3>Plánované Odstávky (Dnes a Budúcnosť)</h3>
        <?php if (empty($futureDowntimes)): ?>
            <p style="text-align: center; color: #aaa;">Aktuálne nie sú naplánované žiadne odstávky strojov.</p>
        <?php else: ?>
             <div class="table-responsive"> <table class="downtime-table">
                    <thead>
                        <tr>
                            <th>Stroj</th>
                            <th>Od</th>
                            <th>Do</th>
                            <th>Dôvod</th>
                            <th>Akcia</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($futureDowntimes as $downtime): ?>
                        <tr>
                            <td>Stroj <?php echo htmlspecialchars($downtime['machine_number']); ?></td>
                            <td><?php echo htmlspecialchars(date("d.m.Y", strtotime($downtime['start_date']))); ?></td>
                            <td><?php echo htmlspecialchars(date("d.m.Y", strtotime($downtime['end_date']))); ?></td>
                            <td><?php echo htmlspecialchars($downtime['reason'] ?: '---'); ?></td>
                            <td>
                                <a href="machines.php?delete_id=<?php echo $downtime['id']; ?>"
                                   onclick="return confirm('Naozaj chcete odstrániť túto odstávku (Stroj <?php echo $downtime['machine_number']; ?> od <?php echo date("d.m.Y", strtotime($downtime['start_date'])); ?>)?');"
                                   title="Odstrániť túto odstávku">
                                    Odstrániť
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>

         <div class="buttons">
             <button onclick="location.href='dashboard.php'" class="back-button">Späť na Dashboard</button>
        </div>

    </div> </body>
</html>