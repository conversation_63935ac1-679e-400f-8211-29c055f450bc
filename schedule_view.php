<?php
require_once 'config.php';
require_once 'functions.php';

requireLogin();
$pdo            = getDbConnection();
$loggedInUserId = $_SESSION['user_id'] ?? null;

// --- KĽÚČOVÁ ZMENA: Načítanie aktívneho oddelenia ---
// Prioritu má oddelenie zvolené v dropdowne na dashboarde, potom defaultné oddelenie používateľa.
$activeOddelenieId = $_SESSION['selected_oddelenie_id'] ?? $_SESSION['oddelenie_id'] ?? null;
$activeOddelenieNazov = $_SESSION['selected_oddelenie_nazov'] ?? $_SESSION['oddelenie_nazov'] ?? null;

// Kontrola, či je oddelenie zvolené
if ($activeOddelenieId === null) {
    $_SESSION['message'] = "Prosím, vyberte oddelenie na Dashboarde pre zobrazenie mesačného rozpisu.";
    header("Location: dashboard.php");
    exit;
}

/* ── dátum ───────────────────────────────────────── */
$requested = $_GET['date'] ?? date('Y-m-d');
try { $ref = new DateTimeImmutable($requested); } catch (Exception $e) { $ref = new DateTimeImmutable(); }
$startOfMonth      = $ref->modify('first day of this month'); $endOfMonth = $ref->modify('last day of this month');
$prevMonthDateStr  = $startOfMonth->modify('-1 month')->format('Y-m-d'); $nextMonthDateStr  = $startOfMonth->modify('+1 month')->format('Y-m-d');
$skMonths=[1=>'Január',2=>'Február',3=>'Marec',4=>'Apríl',5=>'Máj',6=>'Jún',7=>'Júl',8=>'August',9=>'September',10=>'Október',11=>'November',12=>'December'];
$displayMonthStr=$skMonths[(int)$ref->format('n')].' '.$ref->format('Y');
if ($activeOddelenieNazov) { // Použijeme aktívny názov oddelenia
    $displayMonthStr .= ' - Oddelenie: ' . htmlspecialchars($activeOddelenieNazov);
}
$today_date_str_for_compare = date('Y-m-d');

/* ── zoznam strojníkov ──────────────────────────── */
$strojnikOperators=[]; $stmtOps = $pdo->query("SELECT id, name FROM users WHERE strojnik_order IS NOT NULL ORDER BY strojnik_order");
if ($activeOddelenieId) { // Použijeme activeOddelenieId
    $stmtOps = $pdo->prepare("SELECT id, name FROM users WHERE strojnik_order IS NOT NULL AND is_active = 1 AND oddelenie_id = ? ORDER BY strojnik_order");
    $stmtOps->execute([$activeOddelenieId]);
    $strojnikOperators = $stmtOps->fetchAll(PDO::FETCH_KEY_PAIR);
} else {
    // Fallback alebo chybový stav, ak oddelenie nie je definované
    // Tento error_log by sa už nemal spustiť, lebo $activeOddelenieId je kontrolované vyššie
    // error_log("Schedule View: Logged in user has no department ID, cannot fetch strojnik operators.");
    $strojnikOperators = [];
}

/* ── priradenia pre mesiac ──────────────────────── */
$assignmentsData = []; 
$q = $pdo->prepare("SELECT a.assignment_date, a.user_id, a.machine_number 
                    FROM assignments a
                    JOIN users u ON a.user_id = u.id
                    WHERE a.assigned_role='strojnik' 
                    AND u.oddelenie_id = :oddelenie_id
                    AND a.assignment_date BETWEEN :start_date AND :end_date");
$q->execute([':oddelenie_id' => $activeOddelenieId, ':start_date' => $startOfMonth->format('Y-m-d'), ':end_date' => $endOfMonth->format('Y-m-d')]);
$assignments = $q->fetchAll(PDO::FETCH_ASSOC);

// Debug výpisy
if (isset($_SESSION['is_admin']) && $_SESSION['is_admin']) {
    echo '<div style="background: #f5f5f5; padding: 10px; margin: 10px; border: 1px solid #ddd;">';
    echo '<h4>Debug Informácie:</h4>';
    echo '<pre>';
    echo "Načítané priradenia:\n";
    print_r($assignments);
    echo "\n\nŠtruktúra assignmentsData:\n";
    print_r($assignmentsData);
    echo '</pre>';
    echo '</div>';
}

// Preorganizujeme dáta do požadovanej štruktúry
foreach ($assignments as $assignment) {
    $date = $assignment['assignment_date'];
    $userId = $assignment['user_id'];
    $machine = $assignment['machine_number'];
    $assignmentsData[$date][$userId] = $machine;
    
    // Debug pre konkrétneho používateľa
    if ($userId == 123) { // nahraďte 123 skutočným ID používateľa
        if (isset($_SESSION['is_admin']) && $_SESSION['is_admin']) {
            echo "<div style='background: #fff3cd; padding: 5px; margin: 5px;'>";
            echo "Používateľ - Dátum: $date, Stroj: $machine";
            echo "</div>";
        }
    }
}

// Debug výpis pre každý deň v cykle
foreach($strojnikOperators as $uid=>$nm){
    // Najprv získame dátum a či je pracovný deň
    $currentDate = new DateTimeImmutable(); // alebo použite relevantný dátum
    $work = isWorkingDay($currentDate, $pdo);
    
    if ($work) {
        $dstr = $currentDate->format('Y-m-d');
        $machine = $assignmentsData[$dstr][$uid] ?? null;
        if ($machine !== null) {
            if ($machine === '0' || $machine === 0) {
                $cellContent = 'N';
                $cellClasses[] = 'status-absence';
                
                // Debug pre nulu
                if (isset($_SESSION['is_admin']) && $_SESSION['is_admin']) {
                    echo "<div style='background: #d4edda; padding: 2px; margin: 2px; font-size: 0.8em;'>";
                    echo "Nulový stroj - Deň: $dstr, User: $uid";
                    echo "</div>";
                }
            } elseif (in_array($machine, [1, 2, 3], true)) {
                $cellContent = $machine;
            }
        }
    }
}
// Kontrola, či je prihlásený Radis
$isRadisLoggedIn = (isset($_SESSION['username']) && $_SESSION['username'] === 'radis');
?>
<!DOCTYPE html>
<html lang="sk">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Mesačný Prehľad Priradení Strojov</title>
<link rel="stylesheet" href="style.css">
</head>

<body>
<div class="container container-wide"> <div class="header">Mesačný Prehľad Priradení Strojov</div>
  <div class="navigation">
    <a href="schedule_view.php?date=<?php echo $prevMonthDateStr; ?>">&laquo; Predošlý mesiac</a>
    <span><?=htmlspecialchars($displayMonthStr)?></span>
    <a href="schedule_view.php?date=<?=$nextMonthDateStr?>">Nasledujúci mesiac &raquo;</a>
  </div>

  <div class="table-responsive">
    <table class="overview-table monthly-data-table"> <!-- Pridaná trieda overview-table -->
        <thead>
            <tr>
                <th class="date-cell"></th>
                <?php foreach ($strojnikOperators as $uid => $name): ?>
                    <?php
                    $headerClass = ($uid == $loggedInUserId) ? 'highlight-user vertical-header' : 'vertical-header';
                    $displayName = $name;
                    
                    // Špeciálne prípady pre Jany
                    if ($displayName === 'Mrázková Jana') {
                        $displayName = 'Janka M.';
                    } elseif ($displayName === 'Andrášková Jana') {
                        $displayName = 'Janka A.';
                    } else {
                        // Pôvodná logika pre ostatných
                        $surname = explode(' ', $displayName);
                        $displayName = end($surname);
                    }
                    ?>
                    <th class="<?php echo trim($headerClass); ?>">
                         <div><?php echo htmlspecialchars($displayName); ?></div>
                    </th>
                <?php endforeach; ?>
            </tr>
        </thead>
        <tbody>
      <?php
        $wd=['Po','Ut','St','Št','Pi','So','Ne']; // Dni v týždni
        for($d=$startOfMonth;$d<=$endOfMonth;$d=$d->modify('+1 day')):
            $dstr=$d->format('Y-m-d');
            $work=isWorkingDay($d,$pdo);
            $isToday = ($dstr === $today_date_str_for_compare);
            $rowClass = $work ? '' : 'non-working-day';
            if ($isToday) { $rowClass .= ' highlight-today'; }
            echo '<tr class="'.trim($rowClass).'">';
            // Zobrazenie dňa v týždni a dňa v mesiaci
            echo '<td class="date-cell">' . htmlspecialchars($d->format('j')) . '</td>';

            foreach($strojnikOperators as $uid=>$nm){
                $isCurrentUserColumn = ($uid == $loggedInUserId);
                $cellClasses = [];
                $dataAttributes = ''; // Inicializácia dátových atribútov
                // Určenie počtu strojov pre oddelenie tohto používateľa (všetci v $strojnikOperators sú z $loggedInUserOddelenieId)
                $numMachinesForUserDept = 3; // Predvolené
                if ($activeOddelenieNazov === 'ID3') { // Použijeme aktívny názov oddelenia
                    $numMachinesForUserDept = 2;
                }
                // Pridajte ďalšie podmienky pre iné oddelenia, ak je to potrebné


                if ($isToday && $isCurrentUserColumn) { $cellClasses[] = 'highlight-intersection'; }
                elseif ($isCurrentUserColumn) { $cellClasses[] = 'highlight-user'; }

                $cellContent = $work ? '' : '-'; // Predvolený obsah
                if ($work) {
                    $machine = $assignmentsData[$dstr][$uid] ?? null;
                    if ($machine !== null) {
                        if ($machine === '0' || $machine === 0) {
                            $cellContent = 'N'; // 'N' pre neprítomnosť
                            $cellClasses[] = 'status-absence'; // Pridáme triedu pre zvýraznenie
                        } elseif (in_array($machine, [1, 2, 3], true)) {
                            $cellContent = $machine;
                        }
                    }
                }

                // Pridanie triedy a dátových atribútov pre Radisa
                if ($isRadisLoggedIn) {
                    $cellClasses[] = 'manual-assign-cell';
                    $dataAttributes = " data-date=\"{$dstr}\" data-userid=\"{$uid}\" data-current-machine=\"".htmlspecialchars($cellContent)."\" data-num-machines=\"{$numMachinesForUserDept}\"";
                }

                $finalCellClass = implode(' ', array_unique($cellClasses));
                echo "<td class='" . trim($finalCellClass) . "'" . $dataAttributes . ">" . htmlspecialchars((string)$cellContent) . "</td>";
            }
            echo '</tr>';
        endfor;?>
      </tbody>
    </table>
  </div>

  <div class="buttons">
    <button onclick="location.href='dashboard.php'" class="back-button">Späť na Dashboard</button>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    const table = document.querySelector('.monthly-data-table');
    if (!table) return;

    

    // --- NOVÝ KÓD PRE MANUÁLNE PRIRADENIE ---
    let currentDropdown = null; // Na sledovanie aktuálne otvoreného dropdownu

    // Funkcia na zatvorenie dropdownu
    function closeDropdown() {
        if (currentDropdown) {
            currentDropdown.remove();
            currentDropdown = null;
        }
    }

    table.addEventListener('click', (event) => {
        const targetCell = event.target.closest('td.manual-assign-cell');

        if (targetCell) {
            event.stopPropagation(); // Zabráni aktivácii rozširovania stĺpca pri kliku na bunku
            closeDropdown(); // Zatvorí existujúci dropdown, ak nejaký je

            const date = targetCell.dataset.date;
            const userId = targetCell.dataset.userid;
            //const currentMachine = targetCell.dataset.currentMachine; // Ak by ste chceli zobraziť aktuálny

            // Vytvorenie dropdownu
            const dropdown = document.createElement('div');
            dropdown.className = 'machine-dropdown'; // Pridajte štýly v style.css
            dropdown.style.position = 'absolute';
            // Upravené pozicovanie - berie do úvahy skrolovanie
            const rect = targetCell.getBoundingClientRect();
            dropdown.style.left = `${window.scrollX + rect.left}px`;
            dropdown.style.top = `${window.scrollY + rect.bottom}px`; // Pod bunku
            dropdown.style.backgroundColor = '#333';
            dropdown.style.border = '1px solid #555';
            dropdown.style.borderRadius = '4px';
            dropdown.style.zIndex = '10';
            dropdown.style.boxShadow = '0 2px 5px rgba(0,0,0,0.5)';
            
            const numMachines = parseInt(targetCell.dataset.numMachines, 10) || 3; // Fallback na 3, ak atribút chýba
            let dropdownHTML = '';
            for (let i = 1; i <= numMachines; i++) {
                dropdownHTML += `<div class="dropdown-option" data-machine="${i}">Stroj ${i}</div>`;
            }
            dropdownHTML += `<div class="dropdown-option" data-machine="0">Stroj 0 (žiadny)</div>`;
            dropdownHTML += `<div class="dropdown-option" data-machine="null">Zrušiť priradenie</div>`;
            dropdown.innerHTML = dropdownHTML;

            document.body.appendChild(dropdown);
            currentDropdown = dropdown;


            // Pridanie listenera na voľby v dropdowne
            dropdown.addEventListener('click', (dropdownEvent) => {
                if (dropdownEvent.target.classList.contains('dropdown-option')) {
                    const selectedMachine = dropdownEvent.target.dataset.machine;
                    saveMachineAssignment(date, userId, selectedMachine, targetCell);
                    closeDropdown();
                }
            });

        } else if (!event.target.closest('.machine-dropdown')) {
            // Ak klikli mimo bunky s možnosťou priradenia A zároveň mimo dropdownu, zatvor dropdown
            closeDropdown();
        }
    });

    // Listener na kliknutie kdekoľvek v dokumente pre zatvorenie dropdownu
    document.addEventListener('click', (event) => {
        // Zatvorí, len ak existuje dropdown A klik nebol na bunku na priradenie A klik nebol dovnútra dropdownu
        if (currentDropdown && !event.target.closest('td.manual-assign-cell') && !currentDropdown.contains(event.target)) {
            closeDropdown();
        }
    });


    // Funkcia na odoslanie dát na server
    function saveMachineAssignment(date, userId, machineNumber, cellElement) {
        const formData = new FormData();
        formData.append('date', date);
        formData.append('user_id', userId);
        formData.append('machine_number', machineNumber);
        formData.append('action', 'save_manual_assignment'); // Identifikátor akcie

        fetch('save_manual_assignment.php', { // Názov nového PHP súboru
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                // Pokus získať chybovú správu z tela odpovede
                 return response.json().then(errData => {
                    throw new Error(errData.error || `Chyba servera: ${response.status}`);
                 }).catch(() => {
                     // Ak telo nie je JSON alebo je prázdne
                     throw new Error(`Chyba servera: ${response.status}`);
                 });
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // Aktualizácia obsahu bunky v tabuľke
                if (machineNumber === 'null') {
                    cellElement.textContent = ''; // Prázdna bunka pre zrušené priradenie
                } else {
                    cellElement.textContent = machineNumber; // Zobrazí číslo stroja (vrátane 0)
                }
                
                // Vizuálna spätná väzba
                cellElement.style.transition = 'background-color 0.1s ease-in-out';
                cellElement.style.backgroundColor = '#4CAF50';
                setTimeout(() => {
                    cellElement.style.backgroundColor = '';
                    setTimeout(() => { cellElement.style.transition = ''; }, 150);
                }, 500);
            } else {
                alert('Chyba pri ukladaní: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Fetch chyba:', error);
            alert('Nepodarilo sa uložiť priradenie: ' + error.message);
        });
    }
});
</script>
</body>
</html>
