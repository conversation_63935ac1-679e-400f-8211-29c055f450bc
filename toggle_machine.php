<?php
session_start();
require_once 'config.php';
require_once 'functions.php';

header('Content-Type: application/json'); // Nastavíme typ odpovede hneď na začiatku

// Kontrola, či je používateľ prihlásený
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Nie ste prihlásený.']);
    exit;
}

// Kontrola oprávnení - napr. len 'veduci' alebo 'schifflieder'
$pdo = getDbConnection(); // Potrebujeme PDO pre getUserById
$loggedUser = getUserById($_SESSION['user_id'], $pdo);
if (!in_array($loggedUser['role1'] ?? '', ['veduci', 'schifflieder'], true)) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Nemáte oprávnenie na túto akciu.']);
    exit;
}

// Načítanie aktívneho oddelenia, pre ktoré sa operácia vykonáva
$activeOddelenieId = $_SESSION['selected_oddelenie_id'] ?? null;
$activeOddelenieNazov = $_SESSION['selected_oddelenie_nazov'] ?? null; // Pre validáciu počtu strojov

if ($activeOddelenieId === null || $activeOddelenieNazov === null) {
    echo json_encode(['success' => false, 'message' => 'Nie je vybrané žiadne oddelenie. Prosím, vyberte oddelenie na Dashboarde.']);
    exit;
}

// Kontrola, či boli odoslané všetky potrebné parametre
if (!isset($_POST['machine'], $_POST['date'], $_POST['action'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Chýbajúce parametre.']);
    exit;
}

// Získanie parametrov
$machineNumber = (int)$_POST['machine'];
$dateStr = $_POST['date'];
$action = $_POST['action']; // 'activate' alebo 'deactivate'

// Validácia dátumu
try {
    $date = new DateTimeImmutable($dateStr);
    $today = new DateTimeImmutable('today');
    
    // Kontrola, či dátum nie je v minulosti
    if ($date < $today) {
        echo json_encode(['success' => false, 'message' => 'Nie je možné meniť stav strojov v minulosti.']);
        exit;
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Neplatný formát dátumu.']);
    exit;
}

// Validácia čísla stroja na základe oddelenia
$max_machines_for_department = 3; // Predvolený počet
if ($activeOddelenieNazov === 'ID3') {
    $max_machines_for_department = 2;
}
// Tu môžete pridať ďalšie `else if` bloky pre iné oddelenia

if ($machineNumber < 1 || $machineNumber > $max_machines_for_department) {
    echo json_encode(['success' => false, 'message' => "Neplatné číslo stroja '{$machineNumber}'. Pre oddelenie {$activeOddelenieNazov} sú povolené stroje 1-" . $max_machines_for_department . "."]);
    exit;
}

try {
    // PDO už máme z kontroly oprávnení
    $pdo->beginTransaction();
    
    // Zistíme, či už existuje odstávka pre tento stroj, deň A ODDELENIE
    $stmt = $pdo->prepare("
        SELECT id FROM machine_downtime 
        WHERE machine_number = ? 
        AND oddelenie_id = ?
        AND ? BETWEEN start_date AND end_date
    ");
    $stmt->execute([$machineNumber, $activeOddelenieId, $date->format('Y-m-d')]);
    $existingDowntime = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($action === 'deactivate') {
        // Ak už existuje odstávka, nerobíme nič
        if ($existingDowntime) {
            $pdo->commit();
            echo json_encode(['success' => true, 'message' => 'Stroj už je v odstávke.']);
            exit;
        }
        
        // Pridáme novú odstávku len pre jeden deň
        $stmt = $pdo->prepare("
            INSERT INTO machine_downtime (machine_number, start_date, end_date, reason, oddelenie_id)
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $machineNumber, 
            $date->format('Y-m-d'), 
            $date->format('Y-m-d'), 
            'Manuálna odstávka',
            $activeOddelenieId
        ]);
        
        // Vymažeme budúce priradenia od tohto dátumu
        $deleteFutureSuccess = deleteFutureDataFromDate($date, $activeOddelenieId, $pdo);
        if (!$deleteFutureSuccess) {
            throw new Exception("Stroj bol odstavený, ale nepodarilo sa vymazať budúce priradenia.");
        }
        
        $message = "Stroj $machineNumber bol úspešne odstavený pre deň " . $date->format('d.m.Y') . ".";
    } else if ($action === 'activate') {
        // Ak neexistuje odstávka, nerobíme nič
        if (!$existingDowntime) {
            $pdo->commit();
            echo json_encode(['success' => true, 'message' => 'Stroj už je aktívny.']);
            exit;
        }
        
        // Zistíme detaily existujúcej odstávky pre dané oddelenie
        $stmt = $pdo->prepare("
            SELECT id, start_date, end_date FROM machine_downtime 
            WHERE id = ? AND oddelenie_id = ?
        ");
        $stmt->execute([$existingDowntime['id'], $activeOddelenieId]);
        $downtime = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$downtime) {
            throw new Exception("Nepodarilo sa načítať detaily odstávky pre zvolené oddelenie.");
        }
        
        $startDate = new DateTimeImmutable($downtime['start_date']);
        $endDate = new DateTimeImmutable($downtime['end_date']);
        
        // Rozhodneme, ako upraviť existujúcu odstávku
        if ($startDate->format('Y-m-d') === $endDate->format('Y-m-d') && $startDate->format('Y-m-d') === $date->format('Y-m-d')) {
            // Ak je odstávka len na jeden deň a je to presne náš deň, zmažeme ju (pre dané oddelenie)
            $stmt = $pdo->prepare("DELETE FROM machine_downtime WHERE id = ? AND oddelenie_id = ?");
            $stmt->execute([$existingDowntime['id'], $activeOddelenieId]);
        } else if ($startDate->format('Y-m-d') === $date->format('Y-m-d')) {
            // Ak odstávka začína naším dňom, posunieme začiatok o deň
            $newStartDate = $date->modify('+1 day');
            $stmt = $pdo->prepare("UPDATE machine_downtime SET start_date = ? WHERE id = ? AND oddelenie_id = ?");
            $stmt->execute([$newStartDate->format('Y-m-d'), $existingDowntime['id'], $activeOddelenieId]);
        } else if ($endDate->format('Y-m-d') === $date->format('Y-m-d')) {
            // Ak odstávka končí naším dňom, skrátime koniec o deň
            $newEndDate = $date->modify('-1 day');
            $stmt = $pdo->prepare("UPDATE machine_downtime SET end_date = ? WHERE id = ? AND oddelenie_id = ?");
            $stmt->execute([$newEndDate->format('Y-m-d'), $existingDowntime['id'], $activeOddelenieId]);
        } else {
            // Ak je náš deň uprostred odstávky, rozdelíme ju na dve časti
            $newEndDate1 = $date->modify('-1 day');
            
            // Vytvoríme nový dátum pre druhú časť (musíme vytvoriť nový objekt)
            $newStartDate2 = new DateTimeImmutable($date->format('Y-m-d'));
            $newStartDate2 = $newStartDate2->modify('+1 day');
            
            // Aktualizujeme existujúcu odstávku (prvá časť)
            $stmt = $pdo->prepare("UPDATE machine_downtime SET end_date = ? WHERE id = ? AND oddelenie_id = ?");
            $stmt->execute([$newEndDate1->format('Y-m-d'), $existingDowntime['id'], $activeOddelenieId]);
            
            // Vytvoríme novú odstávku (druhá časť)
            $stmt = $pdo->prepare("
                INSERT INTO machine_downtime (machine_number, start_date, end_date, reason, oddelenie_id)
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $machineNumber, 
                $newStartDate2->format('Y-m-d'), 
                $endDate->format('Y-m-d'), 
                'Druhá časť rozdelenej odstávky',
                $activeOddelenieId
            ]);
        }
        
        // Vymažeme budúce priradenia od tohto dátumu
        $deleteFutureSuccess = deleteFutureDataFromDate($date, $activeOddelenieId, $pdo);
        if (!$deleteFutureSuccess) {
            throw new Exception("Stroj bol aktivovaný, ale nepodarilo sa vymazať budúce priradenia.");
        }
        
        $message = "Stroj $machineNumber bol úspešne aktivovaný pre deň " . $date->format('d.m.Y') . ".";
    } else {
        throw new Exception("Neplatná akcia.");
    }
    
    // Prepočítame priradenia pre tento deň a uložíme ich
    // Predpokladáme, že funkcie calculateDailyAssignments a saveAssignments sú dostupné (napr. z functions.php)
    // a pracujú s PDO transakciami správne (alebo ich nepotrebujú, ak sú volané v rámci existujúcej transakcie).
    if (function_exists('calculateDailyAssignments') && function_exists('saveAssignments')) {
        $new_assignments = calculateDailyAssignments($date, $activeOddelenieId, $pdo);
        saveAssignments($date, $new_assignments, $activeOddelenieId, $pdo);
    } else {
        throw new Exception("Funkcie pre prepočítanie priradení (calculateDailyAssignments/saveAssignments) nie sú dostupné.");
    }
    
    $pdo->commit();
    
    echo json_encode(['success' => true, 'message' => $message]);
    
} catch (Exception $e) {
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }
    error_log("Error toggling machine status: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Chyba: ' . $e->getMessage()]);
}
