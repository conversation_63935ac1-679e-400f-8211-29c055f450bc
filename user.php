<?php
// Vytvor súbor napríklad add_user.php, spusti ho raz a potom ho vymaž z bez<PERSON>čnostných dôvodov
require_once 'config.php';

$username = 'da<PERSON><PERSON>';
$passwordPlain = 'hes<PERSON>';
$name = '<PERSON><PERSON>';

// Zahashuj heslo
$passwordHash = password_hash($passwordPlain, PASSWORD_DEFAULT);

try {
    // Začni transakciu
    $pdo->beginTransaction();

    // Pridaj používateľa
    $stmt = $pdo->prepare("INSERT INTO users (username, password, name) VALUES (?, ?, ?)");
    $stmt->execute([$username, $passwordHash, $name]);

    // Získaj ID nového používateľa
    $newUserId = $pdo->lastInsertId();

    // Automaticky vytvor predvolené nastavenia notifikácií
    $stmtNotifications = $pdo->prepare("INSERT INTO user_notification_settings (user_id, notifications_enabled, notification_time, workdays_only) VALUES (?, 1, '07:00:00', 1)");
    $stmtNotifications->execute([$newUserId]);

    // Potvrď transakciu
    $pdo->commit();

    echo "Používateľ bol pridaný s ID: $newUserId a automaticky vytvorené nastavenia notifikácií.";

} catch (PDOException $e) {
    // Vráť transakciu pri chybe
    $pdo->rollBack();
    echo "Chyba pri pridávaní používateľa: " . $e->getMessage();
}
?>