<?php
// test_krok2_calculate_assignments.php - Test pre Krok 2: Úprava hlavnej výpočtovej logiky

require_once 'config.php';
require_once 'functions.php';

try {
    echo "=== Test Krok 2: Úprava hlavnej výpočtovej logiky ===\n\n";
    
    $testDate = new DateTimeImmutable('2025-07-01');
    $testOddelenieId = 1; // Predpokladáme, že existuje oddelenie s ID 1
    $testForcedStartOrder = 3; // Testujeme štart s poradím 3
    
    echo "1. Test calculateDailyAssignments s forced_start_order parametrom...\n";
    $assignments1 = calculateDailyAssignments($testDate, $testOddelenieId, $pdo, $testForcedStartOrder);
    echo "   ✓ Výpočet s vynúteným štartovacím poradím $testForcedStartOrder dokončený\n";
    echo "   Počet priradení: " . count($assignments1) . "\n\n";
    
    echo "2. Test uloženia forced_start_order do metadát...\n";
    $signature = md5(json_encode($assignments1));
    upsertAssignmentMetadataMeta($testDate, $signature, null, null, $testOddelenieId, $pdo, (string)$testForcedStartOrder);
    echo "   ✓ Metadáta s forced_start_order uložené\n\n";
    
    echo "3. Test calculateDailyAssignments s forced_start_order z metadát...\n";
    $assignments2 = calculateDailyAssignments($testDate, $testOddelenieId, $pdo, null);
    echo "   ✓ Výpočet s forced_start_order z metadát dokončený\n";
    echo "   Počet priradení: " . count($assignments2) . "\n\n";
    
    echo "4. Test zachovania forced_start_order pri regenerateAssignmentsForDates...\n";
    regenerateAssignmentsForDates($testDate, $testOddelenieId, $pdo);
    
    $metaAfterRegenerate = getAssignmentMetadataMeta($testDate, $testOddelenieId, $pdo);
    if ($metaAfterRegenerate && $metaAfterRegenerate['forced_start_order'] === (string)$testForcedStartOrder) {
        echo "   ✓ forced_start_order bolo zachované pri regenerácii!\n";
    } else {
        echo "   ✗ CHYBA: forced_start_order nebolo zachované pri regenerácii!\n";
        echo "     Očakávané: " . (string)$testForcedStartOrder . "\n";
        echo "     Skutočné: " . ($metaAfterRegenerate['forced_start_order'] ?? 'NULL') . "\n";
    }
    
    echo "\n5. Test recalculateAssignmentsWithForcedStart...\n";
    $testDate2 = new DateTimeImmutable('2025-07-02');
    $testForcedStartOrder2 = 2;
    
    recalculateAssignmentsWithForcedStart($testDate2, $testForcedStartOrder2, $testOddelenieId, $pdo);
    
    $metaAfterForced = getAssignmentMetadataMeta($testDate2, $testOddelenieId, $pdo);
    if ($metaAfterForced && $metaAfterForced['forced_start_order'] === (string)$testForcedStartOrder2) {
        echo "   ✓ recalculateAssignmentsWithForcedStart správne uložilo forced_start_order!\n";
    } else {
        echo "   ✗ CHYBA: recalculateAssignmentsWithForcedStart neuložilo forced_start_order správne!\n";
        echo "     Očakávané: " . (string)$testForcedStartOrder2 . "\n";
        echo "     Skutočné: " . ($metaAfterForced['forced_start_order'] ?? 'NULL') . "\n";
    }
    
    // Vyčistenie testovacích dát
    echo "\n6. Čistenie testovacích dát...\n";
    $cleanupStmt = $pdo->prepare("DELETE FROM assignment_metadata WHERE assignment_date IN (?, ?) AND oddelenie_id = ?");
    $cleanupStmt->execute([$testDate->format('Y-m-d'), $testDate2->format('Y-m-d'), $testOddelenieId]);
    
    $cleanupAssignments = $pdo->prepare("DELETE a FROM assignments a JOIN users u ON a.user_id = u.id WHERE a.assignment_date IN (?, ?) AND u.oddelenie_id = ?");
    $cleanupAssignments->execute([$testDate->format('Y-m-d'), $testDate2->format('Y-m-d'), $testOddelenieId]);
    echo "   ✓ Testovacie dáta vyčistené\n";
    
    echo "\n=== Test Krok 2 dokončený úspešne! ===\n";
    
} catch (Exception $e) {
    echo "CHYBA pri teste: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
