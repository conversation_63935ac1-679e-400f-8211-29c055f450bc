
document.addEventListener('DOMContentLoaded', function() {
    // Pridanie podpory pre swipe gestá v mesačnom kalendári
    const calendarContainer = document.querySelector('.calendar-container');
    if (calendarContainer) {
        let startX, endX;
        
        calendarContainer.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
        });
        
        calendarContainer.addEventListener('touchend', function(e) {
            endX = e.changedTouches[0].clientX;
            handleCalendarSwipe();
        });
        
        function handleCalendarSwipe() {
            const threshold = 100; // minimálna vzdialenosť pre detekciu swipe
            
            // Získanie URL parametrov pre navigáciu
            const nextMonthLink = document.querySelector('.calendar-navigation a:last-child');
            const prevMonthLink = document.querySelector('.calendar-navigation a:first-child');
            
            if (startX - endX > threshold && nextMonthLink) {
                // Swipe doľava - nasledujúci mesiac
                loadCalendarMonth(nextMonthLink.getAttribute('href'));
            }
            
            if (endX - startX > threshold && prevMonthLink) {
                // Swipe doprava - predchádzajúci mesiac
                loadCalendarMonth(prevMonthLink.getAttribute('href'));
            }
        }
    }
    
    // Funkcia na načítanie kalendára cez AJAX
    window.loadCalendarMonth = function(url) {
        // Extrahujeme parameter calendar_month z URL
        const urlParams = new URLSearchParams(url.split('?')[1]);
        const calendarMonth = urlParams.get('calendar_month');
        const currentDate = urlParams.get('date');
        
        if (!calendarMonth) return;
        
        // Zobrazíme indikátor načítavania
        const calendarSection = document.querySelector('.monthly-calendar-section');
        if (calendarSection) {
            calendarSection.classList.add('loading');
            
            // Vytvoríme AJAX požiadavku
            const xhr = new XMLHttpRequest();
            xhr.open('GET', 'calendar_ajax.php?calendar_month=' + calendarMonth + '&date=' + currentDate, true);
            
            xhr.onload = function() {
                if (this.status === 200) {
                    // Aktualizujeme obsah kalendára
                    calendarSection.innerHTML = this.responseText;
                    calendarSection.classList.remove('loading');
                    
                    // Aktualizujeme URL v prehliadači bez načítania stránky
                    history.pushState({}, '', url);
                    
                    // Znovu pripojíme event listenery pre nové elementy
                    attachCalendarEventListeners();
                } else {
                    console.error('Chyba pri načítaní kalendára:', this.status);
                    calendarSection.classList.remove('loading');
                }
            };
            
            xhr.onerror = function() {
                console.error('Chyba pri načítaní kalendára');
                calendarSection.classList.remove('loading');
            };
            
            xhr.send();
        }
    };
    
    // Funkcia na pripojenie event listenerov pre kalendár
    function attachCalendarEventListeners() {
        // Pripojíme event listenery pre navigačné odkazy
        const calendarNavLinks = document.querySelectorAll('.calendar-navigation a');
        calendarNavLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                loadCalendarMonth(this.getAttribute('href'));
            });
        });
        
        // Pripojíme event listenery pre swipe gestá
        const newCalendarContainer = document.querySelector('.calendar-container');
        if (newCalendarContainer) {
            let newStartX, newEndX;
            
            newCalendarContainer.addEventListener('touchstart', function(e) {
                newStartX = e.touches[0].clientX;
            });
            
            newCalendarContainer.addEventListener('touchend', function(e) {
                newEndX = e.changedTouches[0].clientX;
                handleNewCalendarSwipe();
            });
            
            function handleNewCalendarSwipe() {
                const threshold = 100;
                
                const newNextMonthLink = document.querySelector('.calendar-navigation a:last-child');
                const newPrevMonthLink = document.querySelector('.calendar-navigation a:first-child');
                
                if (newStartX - newEndX > threshold && newNextMonthLink) {
                    loadCalendarMonth(newNextMonthLink.getAttribute('href'));
                }
                
                if (newEndX - newStartX > threshold && newPrevMonthLink) {
                    loadCalendarMonth(newPrevMonthLink.getAttribute('href'));
                }
            }
        }
    }
    
    // Inicializácia event listenerov pre kalendár
    attachCalendarEventListeners();
});
