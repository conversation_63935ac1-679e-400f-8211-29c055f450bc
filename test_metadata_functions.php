<?php
// test_metadata_functions.php - Test pre nové funkcie metadát

require_once 'config.php';
require_once 'functions.php';

try {
    echo "=== Test funkcií pre metadáta s forced_start_order ===\n\n";
    
    $testDate = new DateTimeImmutable('2025-07-01');
    $testOddelenieId = 1; // Predpokladáme, že existuje oddelenie s ID 1
    $testSignature = 'test_signature_' . time();
    $testForcedOrder = '2,3,1';
    
    echo "1. Test upsertAssignmentMetadataMeta s forced_start_order...\n";
    upsertAssignmentMetadataMeta($testDate, $testSignature, null, null, $testOddelenieId, $pdo, $testForcedOrder);
    echo "   ✓ Metadáta uložené\n\n";
    
    echo "2. Test getAssignmentMetadataMeta - načítanie forced_start_order...\n";
    $meta = getAssignmentMetadataMeta($testDate, $testOddelenieId, $pdo);
    
    if ($meta) {
        echo "   ✓ Metadáta načítané:\n";
        echo "     - signature: " . $meta['signature'] . "\n";
        echo "     - forced_start_order: " . ($meta['forced_start_order'] ?? 'NULL') . "\n";
        echo "     - updated_at: " . $meta['updated_at'] . "\n";
        
        if ($meta['forced_start_order'] === $testForcedOrder) {
            echo "   ✓ forced_start_order sa správne uložilo a načítalo!\n";
        } else {
            echo "   ✗ CHYBA: forced_start_order sa nezhoduje!\n";
            echo "     Očakávané: $testForcedOrder\n";
            echo "     Skutočné: " . ($meta['forced_start_order'] ?? 'NULL') . "\n";
        }
    } else {
        echo "   ✗ CHYBA: Metadáta sa nepodarilo načítať!\n";
    }
    
    echo "\n3. Test aktualizácie forced_start_order...\n";
    $newForcedOrder = '3,1,2';
    upsertAssignmentMetadataMeta($testDate, $testSignature, null, null, $testOddelenieId, $pdo, $newForcedOrder);
    
    $updatedMeta = getAssignmentMetadataMeta($testDate, $testOddelenieId, $pdo);
    if ($updatedMeta && $updatedMeta['forced_start_order'] === $newForcedOrder) {
        echo "   ✓ forced_start_order sa správne aktualizovalo!\n";
    } else {
        echo "   ✗ CHYBA: forced_start_order sa neaktualizovalo správne!\n";
    }
    
    echo "\n4. Test s NULL forced_start_order...\n";
    upsertAssignmentMetadataMeta($testDate, $testSignature, null, null, $testOddelenieId, $pdo, null);
    
    $nullMeta = getAssignmentMetadataMeta($testDate, $testOddelenieId, $pdo);
    if ($nullMeta && $nullMeta['forced_start_order'] === null) {
        echo "   ✓ NULL forced_start_order sa správne uložilo!\n";
    } else {
        echo "   ✗ CHYBA: NULL forced_start_order sa neuložilo správne!\n";
    }
    
    // Vyčistenie testovacích dát
    echo "\n5. Čistenie testovacích dát...\n";
    $cleanupStmt = $pdo->prepare("DELETE FROM assignment_metadata WHERE assignment_date = ? AND oddelenie_id = ?");
    $cleanupStmt->execute([$testDate->format('Y-m-d'), $testOddelenieId]);
    echo "   ✓ Testovacie dáta vyčistené\n";
    
    echo "\n=== Test dokončený úspešne! ===\n";
    
} catch (Exception $e) {
    echo "CHYBA pri teste: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
