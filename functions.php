<?php
// functions.php
require_once 'config.php'; // Načíta $pdo a nastavenia

// --- Autentifikácia ---
function isLoggedIn(): bool {
    return isset($_SESSION['user_id']);
}

function requireLogin(): void {
    // Ak je používateľ už prihlásený cez session, skontrolujeme, či má všetky potrebné údaje
    if (!isLoggedIn()) {
        // Ak nie je prihlásený cez session, skúsime "Remember Me" cookie
        // checkRememberMeCookie vracia true a nastaví session, ak je cookie platná
        if (checkRememberMeCookie(getDbConnection())) {
            // Používateľ bol úspešne prihlásený cez cookie, session je teraz nastavená.
            // loadUserDataIntoSession je volaná vnútri checkRememberMeCookie.
            return; // Pokračujeme
        } else {
            // Ak ani session ani cookie nefungovali, presmerujeme na login
            header("Location: login.php");
            exit;
        }
    } else {
        // Používateľ je prihlásený cez session.
        // Zaistíme, že všetky potrebné údaje používateľa sú v session (napr. po session_regenerate_id).
        if (!isset($_SESSION['user_name']) && isset($_SESSION['user_id'])) {
            loadUserDataIntoSession($_SESSION['user_id'], getDbConnection());
        }
        // Pokračujeme, používateľ je prihlásený cez session.
    }
}


function formatUserDisplayName(string $rawName): string {
    if (empty(trim($rawName)) || $rawName === '---') {
        return $rawName; // Vráti pôvodnú hodnotu, ak je prázdna alebo placeholder
    }

    $fullName = $rawName;
    $suffix = '';

    // Detekcia a oddelenie sufixu "(Zástup)"
    $zastupSuffix = ' (Zástup)';
    if (substr($fullName, -strlen($zastupSuffix)) === $zastupSuffix) {
        $fullName = trim(substr($fullName, 0, strlen($fullName) - strlen($zastupSuffix)));
        $suffix = $zastupSuffix;
    }

    $formattedName = '';
    // Špecifické prípady pre "Priezvisko Meno"
    if ($fullName === 'Mrázková Jana') {
        $formattedName = 'Jana M.';
    } elseif ($fullName === 'Andrášková Jana') {
        $formattedName = 'Jana A.';
    } else {
        // Všeobecný prípad: extrakcia krstného mena
        $nameParts = explode(' ', trim($fullName));
        if (count($nameParts) > 1) {
            // Predpokladáme formát "Priezvisko Meno", takže krstné meno je posledná časť
            $formattedName = end($nameParts);
        } elseif (count($nameParts) === 1 && !empty($nameParts[0])) {
            // Ak je len jedna časť, predpokladáme, že je to krstné meno
            $formattedName = $nameParts[0];
        } else {
            $formattedName = $fullName; // Fallback na celé meno, ak je formát neočakávaný
        }
    }

    return $formattedName . $suffix;
}




// --- Práca s Dátumom ---

/**
 * Skontroluje, či je dátum víkend (Sobota alebo Nedeľa).
 */
function isWeekend(DateTimeInterface $date): bool {
    $dayOfWeek = $date->format('N'); // 1 (pre Pondelok) až 7 (pre Nedeľu)
    return $dayOfWeek >= 6;
}

/**
 * Načíta sviatky z databázy pre daný rok (s cache).
 */
function getHolidaysFromDb(int $year, PDO $pdo): array {
    static $holidaysCache = []; // Statická cache pre sviatky
    if (!isset($holidaysCache[$year])) {
        try {
            $stmt = $pdo->prepare("SELECT `date` FROM holidays WHERE year = ?");
            $stmt->execute([$year]);
            $holidaysCache[$year] = $stmt->fetchAll(PDO::FETCH_COLUMN);
        } catch (PDOException $e) {
            error_log("Chyba pri načítavaní sviatkov pre rok $year: " . $e->getMessage());
            $holidaysCache[$year] = [];
        }
    }
    return $holidaysCache[$year];
}

/**
 * Skontroluje, či je dátum sviatok podľa DB.
 */
function isHoliday(DateTimeInterface $date, PDO $pdo): bool {
    $year = (int)$date->format('Y');
    $dateStr = $date->format('Y-m-d');
    $holidays = getHolidaysFromDb($year, $pdo);
    return in_array($dateStr, $holidays);
}

/**
 * Skontroluje, či je dátum pracovný deň.
 */
function isWorkingDay(DateTimeInterface $date, PDO $pdo): bool {
    if (isWeekend($date)) {
        return false;
    }
    if (isHoliday($date, $pdo)) {
        return false;
    }
    return true;
}

/**
 * Nájde nasledujúci pracovný deň.
 */
function findNextWorkingDay(DateTimeInterface $currentDate, PDO $pdo): DateTime {
    // Použijeme meniteľnú kópiu pre modifikáciu
    $date = DateTime::createFromInterface($currentDate);
    do {
        $date->modify('+1 day');
    } while (!isWorkingDay($date, $pdo));
    return $date;
}

/**
 * Nájde predchádzajúci pracovný deň.
 */
function findPreviousWorkingDay(DateTimeInterface $currentDate, PDO $pdo): DateTime {
    $date = DateTime::createFromInterface($currentDate);
    do {
        $date->modify('-1 day');
    } while (!isWorkingDay($date, $pdo));
    return $date;
}

/**
 * Vypočíta počet pracovných dní medzi dvoma dátumami.
 */
function countWorkingDaysBetween(DateTimeInterface $startDate, DateTimeInterface $endDate, PDO $pdo): int {
    $start = DateTimeImmutable::createFromInterface($startDate);
    $end = DateTimeImmutable::createFromInterface($endDate);
    $diffSign = ($start <= $end) ? 1 : -1;
    if ($diffSign < 0) {
        list($start, $end) = [$end, $start];
    }
    $workingDays = 0;
    $current = $start;
    while ($current < $end) {
        if (isWorkingDay($current, $pdo)) {
            $workingDays++;
        }
        $current = $current->modify('+1 day');
    }
    return $workingDays * $diffSign;
}

/**
 * Určí cieľový dátum z GET parametra alebo použije dnešný pracovný deň.
 */
function determineTargetDate(?string $requestedDateStr, PDO $pdo): DateTime {
    $targetDate = null;
    if ($requestedDateStr) {
        try {
            $d = DateTime::createFromFormat('Y-m-d', $requestedDateStr);
            if ($d && $d->format('Y-m-d') === $requestedDateStr) {
                 $targetDate = $d->setTime(0, 0, 0);
            }
        } catch (Exception $e) { /* Ignorujeme neplatný dátum */ }
    }
     if ($targetDate === null) {
         $targetDate = new DateTime('today');
     }
    if (!isWorkingDay($targetDate, $pdo)) {
        $targetDate = findPreviousWorkingDay($targetDate, $pdo);
    }
    return $targetDate;
}

// --- Načítanie Dát z DB ---
function getAllUsers(PDO $pdo, ?int $oddelenie_id = null): array {
    try {
        $sql = "SELECT u.*, o.nazov AS oddelenie_nazov FROM users u LEFT JOIN Oddelenia o ON u.oddelenie_id = o.oddelenie_id WHERE u.is_active = 1";
        $params = [];
        if ($oddelenie_id !== null) {
            $sql .= " AND u.oddelenie_id = ?";
            $params[] = $oddelenie_id;
        }
        $sql .= " ORDER BY u.name ASC";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $log_oddelenie = $oddelenie_id ?? 'všetky oddelenia';
        error_log("Chyba pri načítavaní používateľov pre oddelenie '$log_oddelenie': " . $e->getMessage());
        return [];
    }
}

function getUserById(int $userId, PDO $pdo): ?array {
    try {
        $stmt = $pdo->prepare("SELECT u.*, o.nazov AS oddelenie_nazov FROM users u LEFT JOIN Oddelenia o ON u.oddelenie_id = o.oddelenie_id WHERE u.id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        return $user ?: null;
    } catch (PDOException $e) {
         error_log("Chyba pri načítavaní používateľa podľa ID $userId: " . $e->getMessage());
        return null;
    }
}

/**
 * Získa všetkých aktívnych používateľov s rolou strojníka pre dané oddelenie.
 */
function getAllMachinists(PDO $pdo, ?int $oddelenie_id): array {
    if ($oddelenie_id === null) {
        return [];
    }
    try {
        $stmt = $pdo->prepare("SELECT id, name, strojnik_order FROM users WHERE strojnik_order IS NOT NULL AND is_active = 1 AND oddelenie_id = ? ORDER BY strojnik_order ASC");
        $stmt->execute([$oddelenie_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Chyba pri načítavaní strojníkov pre oddelenie $oddelenie_id: " . $e->getMessage());
        return [];
    }
}

/**
 * Načíta základné údaje o používateľovi a uloží ich do session.
 */
function loadUserDataIntoSession(int $userId, PDO $pdo): void {
    $user = getUserById($userId, $pdo);
    if ($user) {
        $_SESSION['user_name'] = $user['name'] ?? 'Neznámy';
        $_SESSION['username'] = $user['username'] ?? 'neznamy';
        $_SESSION['oddelenie_id'] = $user['oddelenie_id'] ?? null;
        $_SESSION['oddelenie_nazov'] = $user['oddelenie_nazov'] ?? 'Neznáme oddelenie';
        // Ak používate selected_oddelenie_id, inicializujte ho tu, ak ešte nie je nastavené
        if (!isset($_SESSION['selected_oddelenie_id']) && isset($user['oddelenie_id'])) {
            $_SESSION['selected_oddelenie_id'] = $user['oddelenie_id'];
            $_SESSION['selected_oddelenie_nazov'] = $user['oddelenie_nazov'];
        }
    } else {
        error_log("loadUserDataIntoSession: Používateľ s ID $userId nebol nájdený.");
    }
}


function getPresentUsers(DateTimeInterface $date, ?int $oddelenie_id, PDO $pdo): array {
    $dateStr = $date->format('Y-m-d');
    try {
        $absencesOnDateMap = [];
        if ($oddelenie_id !== null) {
            $stmtAbsences = $pdo->prepare("
                SELECT a.user_id, a.absence_type
                FROM absences a
                JOIN users u ON a.user_id = u.id
                WHERE u.oddelenie_id = :oddelenie_id AND :current_date BETWEEN a.start_date AND a.end_date
            ");
            $stmtAbsences->execute([':oddelenie_id' => $oddelenie_id, ':current_date' => $dateStr]);
            while ($row = $stmtAbsences->fetch(PDO::FETCH_ASSOC)) {
                $absencesOnDateMap[(int)$row['user_id']] = $row['absence_type'];
            }
        }

        $usersInDepartment = getAllUsers($pdo, $oddelenie_id);
        $presentUsers = [];

        foreach ($usersInDepartment as $user) {
            $userId = (int)$user['id'];
            if (isset($absencesOnDateMap[$userId])) {
                if ($absencesOnDateMap[$userId] === 'Poobede') {
                    $presentUsers[$userId] = $user;
                }
            } else {
                $presentUsers[$user['id']] = $user;
            }
        }
        return $presentUsers;
    } catch (PDOException $e) {
        $log_oddelenie = $oddelenie_id ?? 'N/A';
        error_log("Chyba pri načítavaní prítomných používateľov pre dátum $dateStr, oddelenie $log_oddelenie: " . $e->getMessage());
        return [];
    }
}

function getActiveMachines(DateTimeInterface $date, ?int $oddelenie_id, PDO $pdo): array {
    if ($oddelenie_id === null) {
        error_log("getActiveMachines volané bez oddelenie_id pre dátum " . $date->format('Y-m-d'));
        return [];
    }
    $stmtDept = $pdo->prepare("SELECT nazov FROM Oddelenia WHERE oddelenie_id = ?");
    $stmtDept->execute([$oddelenie_id]);
    $department_name = $stmtDept->fetchColumn();
    if (!$department_name) {
        error_log("Nepodarilo sa načítať názov oddelenia pre ID: $oddelenie_id v getActiveMachines.");
        return [];
    }
    $max_machines_for_department = 3;
    if ($department_name === 'ID3') {
        $max_machines_for_department = 2;
    }
    $allMachinesForDepartment = range(1, $max_machines_for_department);
    $dateStr = $date->format('Y-m-d');
    try {
        $stmtDowntime = $pdo->prepare("
            SELECT DISTINCT machine_number 
            FROM machine_downtime 
            WHERE oddelenie_id = :oddelenie_id 
              AND :current_date BETWEEN start_date AND end_date
        ");
        $stmtDowntime->execute([':oddelenie_id' => $oddelenie_id, ':current_date' => $dateStr]);
        $inactiveMachineNumbers = array_map('intval', $stmtDowntime->fetchAll(PDO::FETCH_COLUMN));
        return array_values(array_diff($allMachinesForDepartment, $inactiveMachineNumbers));
    } catch (PDOException $e) {
        error_log("Chyba pri načítavaní aktívnych strojov pre dátum $dateStr, oddelenie $oddelenie_id: " . $e->getMessage());
        return $allMachinesForDepartment;
    }
}

function getAssignmentsForDate(DateTimeInterface $date, ?int $oddelenie_id, PDO $pdo): array {
    $dateStr = $date->format('Y-m-d');
    $assignments = [];
    if ($oddelenie_id === null) {
        error_log("getAssignmentsForDate volané bez oddelenie_id pre dátum $dateStr.");
        return [];
    }
    try {
        $stmt = $pdo->prepare("
            SELECT a.*, u.name
            FROM assignments a
            JOIN users u ON a.user_id = u.id
            WHERE a.assignment_date = :assignment_date AND u.oddelenie_id = :oddelenie_id
        ");
        $stmt->execute([':assignment_date' => $dateStr, ':oddelenie_id' => $oddelenie_id]);
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $assignments[(int)$row['user_id']] = $row;
        }
        return $assignments;
    } catch (PDOException $e) {
        $log_oddelenie = $oddelenie_id ?? 'N/A';
        error_log("Chyba pri načítavaní priradení pre dátum $dateStr, oddelenie $log_oddelenie: " . $e->getMessage());
        return [];
    }
}

function getLastMachineAssignment(int $userId, PDO $pdo, DateTimeInterface $targetDate): ?int {
    try {
        $stmt = $pdo->prepare("
            SELECT machine_number
            FROM assignments
            WHERE user_id = :userId
              AND assigned_role = 'strojnik'
              AND machine_number > 0
              AND assignment_date < :targetDate
            ORDER BY assignment_date DESC
            LIMIT 1
        ");
        $stmt->bindValue(':userId', $userId, PDO::PARAM_INT);
        $stmt->bindValue(':targetDate', $targetDate->format('Y-m-d'), PDO::PARAM_STR);
        $stmt->execute();
        $result = $stmt->fetchColumn();
        return $result !== false ? (int)$result : null;
    } catch (PDOException $e) {
        error_log("Chyba pri načítavaní posledného priradenia stroja pre používateľa $userId: " . $e->getMessage());
        return null;
    }
}

function getRecentMachineHistory(int $userId, PDO $pdo, DateTimeInterface $targetDate, int $limit): array {
    if ($limit <= 0) return [];
    try {
        $stmt = $pdo->prepare("
            SELECT machine_number
            FROM assignments
            WHERE user_id = :userId
              AND assigned_role = 'strojnik'
              AND machine_number > 0
              AND assignment_date < :targetDate
            ORDER BY assignment_date DESC
            LIMIT :limitValue
        ");
        $stmt->bindValue(':userId', $userId, PDO::PARAM_INT);
        $stmt->bindValue(':targetDate', $targetDate->format('Y-m-d'), PDO::PARAM_STR);
        $stmt->bindValue(':limitValue', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return array_map('intval', $stmt->fetchAll(PDO::FETCH_COLUMN));
    } catch (PDOException $e) {
        error_log("Chyba pri načítavaní nedávnej histórie strojov pre používateľa $userId: " . $e->getMessage());
        return [];
    }
}

function getDaysSinceLastOnMachine(int $userId, int $machineNumber, PDO $pdo, DateTimeInterface $targetDate): int {
    try {
        $stmt = $pdo->prepare("
            SELECT assignment_date
            FROM assignments
            WHERE user_id = :userId
              AND machine_number = :machineNumber
              AND assigned_role = 'strojnik'
              AND assignment_date < :targetDate
            ORDER BY assignment_date DESC
            LIMIT 1
        ");
        $stmt->bindValue(':userId', $userId, PDO::PARAM_INT);
        $stmt->bindValue(':machineNumber', $machineNumber, PDO::PARAM_INT);
        $stmt->bindValue(':targetDate', $targetDate->format('Y-m-d'), PDO::PARAM_STR);
        $stmt->execute();
        $lastDateStr = $stmt->fetchColumn();
        if ($lastDateStr) {
            $lastDate = new DateTimeImmutable($lastDateStr);
            return countWorkingDaysBetween($lastDate, $targetDate, $pdo);
        }
        return 999;
    } catch (PDOException $e) {
        error_log("Chyba pri načítavaní dní od posledného priradenia stroja $machineNumber pre používateľa $userId: " . $e->getMessage());
        return 999;
    }
}

function getMachineCountInHistory(int $userId, int $machineNumber, PDO $pdo, DateTimeInterface $targetDate, int $assignmentLookback = 20): int {
    if ($assignmentLookback <= 0) return 0;
    try {
        $stmtHistory = $pdo->prepare("
            SELECT machine_number
            FROM assignments
            WHERE user_id = :userId
              AND assigned_role = 'strojnik'
              AND machine_number > 0
              AND assignment_date < :targetDate
            ORDER BY assignment_date DESC
            LIMIT :limitValue
        ");
        $stmtHistory->bindValue(':userId', $userId, PDO::PARAM_INT);
        $stmtHistory->bindValue(':targetDate', $targetDate->format('Y-m-d'), PDO::PARAM_STR);
        $stmtHistory->bindValue(':limitValue', $assignmentLookback, PDO::PARAM_INT);
        $stmtHistory->execute();
        $recentMachines = array_map('intval', $stmtHistory->fetchAll(PDO::FETCH_COLUMN));
        $count = 0;
        foreach ($recentMachines as $recentMachine) {
            if ($recentMachine === $machineNumber) $count++;
        }
        return $count;
    } catch (PDOException $e) {
        error_log("Chyba pri načítavaní počtu priradení stroja $machineNumber pre používateľa $userId v histórii: " . $e->getMessage());
        return 0;
    }
}

function calculateDailyAssignments(DateTimeInterface $targetDate, ?int $oddelenie_id, PDO $pdo, ?int $forcedStartOrder = null): array {
    if (!isWorkingDay($targetDate, $pdo)) {
        error_log("Dátum " . $targetDate->format('Y-m-d') . " nie je pracovný deň. Oddelenie: " . ($oddelenie_id ?? 'N/A'));
        return [];
    }
    $dateStr = $targetDate->format('Y-m-d');
    $log_oddelenie_id = $oddelenie_id ?? 'N/A';
    error_log("--- Výpočet priradení pre pracovný deň: $dateStr, Oddelenie ID: $log_oddelenie_id ---");
    if ($oddelenie_id === null) {
        error_log("Chyba: calculateDailyAssignments volané bez oddelenie_id pre $dateStr.");
        return [];
    }

    // Načítanie metadát pre tento deň (potrebné pre forced_start_order)
    $meta = getAssignmentMetadataMeta($targetDate, $oddelenie_id, $pdo);
    $presentUsers = getPresentUsers($targetDate, $oddelenie_id, $pdo);
    $assignments = []; $busyIds = [];
    $department_name_stmt = $pdo->prepare("SELECT nazov FROM Oddelenia WHERE oddelenie_id = ?");
    $department_name_stmt->execute([$oddelenie_id]);
    $department_name = $department_name_stmt->fetchColumn();
    $activeMachineNumbers = getActiveMachines($targetDate, $oddelenie_id, $pdo);
    $veduciId = null; $veduciIsPresent = false; $veduciName = '';
    foreach ($presentUsers as $u) {
        if (($u['role1'] ?? null) === 'veduci') {
            $veduciId = (int)$u['id']; $veduciIsPresent = true; $veduciName = $u['name'];
            error_log("Vedúci (prítomný): ID $veduciId ($veduciName)"); break;
        }
    }
    if (!$veduciIsPresent) error_log("Vedúci (" . ($veduciName ?: 'N/A') . ") je neprítomný alebo nebol nájdený v oddelení $log_oddelenie_id pre dátum $dateStr.");
    $schiffId = null; $bruder = null; $domi = null;
    $stmtAllSchiffUsers = $pdo->query("SELECT id, username, role1, name FROM users WHERE role1 = 'schifflieder' AND is_active = 1");
    while ($u = $stmtAllSchiffUsers->fetch(PDO::FETCH_ASSOC)) {
        if ($u['username'] === 'radis') $bruder = $u;
        if ($u['username'] === 'dominika') $domi = $u;
    }
    if (!$bruder || !$domi) error_log("Chyba: Nepodarilo sa načítať oboch Schiffliederov (radis, dominika) z DB.");
    else {
        try {
            $stmtRef = $pdo->prepare("SELECT reference_date, starts_with_radis FROM schifflieder_reference WHERE id = 1");
            $stmtRef->execute(); $schiffRefData = $stmtRef->fetch(PDO::FETCH_ASSOC);
            if (!$schiffRefData) throw new Exception("Referenčné dáta pre Schifflieder nenájdené.");
            $refDate = new DateTimeImmutable($schiffRefData['reference_date']);
            $startsRadis = (bool)$schiffRefData['starts_with_radis'];
            $wDiff = countWorkingDaysBetween($refDate, $targetDate, $pdo);
            error_log("Schifflieder: RefDate={$refDate->format('Y-m-d')}, StartsRadis=" .($startsRadis?'true':'false'). ", wDiff=$wDiff");
            $isRadisTurnToday = $startsRadis ? ($wDiff % 2 === 0) : ($wDiff % 2 !== 0);
            $prefSchiff = $isRadisTurnToday ? $bruder : $domi; $altSchiff = $isRadisTurnToday ? $domi : $bruder;
            error_log("Schifflieder: Preferovaný dnes je " . ($prefSchiff['name'] ?? 'N/A'));
            $assignedRegularSchiff = false;
            if ($prefSchiff && isset($presentUsers[$prefSchiff['id']]) && !in_array($prefSchiff['id'], $busyIds, true)) {
                $schiffId = $prefSchiff['id']; $assignedRegularSchiff = true;
                error_log("Schifflieder (Preferovaný): ID $schiffId ({$prefSchiff['name']})");
            } elseif ($altSchiff && isset($presentUsers[$altSchiff['id']]) && !in_array($altSchiff['id'], $busyIds, true)) {
                $schiffId = $altSchiff['id']; $assignedRegularSchiff = true;
                error_log("Schifflieder (Alternatívny): ID $schiffId ({$altSchiff['name']})");
            }
            if (!$assignedRegularSchiff) {
                if ($veduciIsPresent && !in_array($veduciId, $busyIds, true) && $veduciId !== ($bruder['id'] ?? null) && $veduciId !== ($domi['id'] ?? null)) {
                    $schiffId = $veduciId; error_log("Schifflieder (Fallback Vedúci): ID $schiffId ($veduciName)");
                } else error_log("Rola Schifflieder nemohla byť priradená pre $dateStr.");
            }
        } catch (Exception $e) {
            error_log("Chyba výpočtu Schifflieder pre $dateStr: " . $e->getMessage());
            if ($veduciIsPresent && !in_array($veduciId, $busyIds, true) && $veduciId !== ($bruder['id'] ?? null) && $veduciId !== ($domi['id'] ?? null)) {
                $schiffId = $veduciId; error_log("Schifflieder (Fallback Vedúci po chybe): ID $schiffId ($veduciName)");
            }
        }
    }
    if ($schiffId) {
        $order = null;
        if ($bruder && $schiffId === $bruder['id']) $order = 1;
        elseif ($domi && $schiffId === $domi['id']) $order = 2;
        elseif ($schiffId === $veduciId) $order = 9;
        $schiffAlreadyAssigned = false; foreach($assignments as $exAs) if ($exAs['assigned_role']==='schifflieder') $schiffAlreadyAssigned=true;
        if (!$schiffAlreadyAssigned) {
            $assignments[] = ['user_id' => $schiffId, 'assigned_role' => 'schifflieder', 'machine_number' => null, 'assignment_order' => $order];
            $busyIds[] = $schiffId;
        } else error_log("Schifflieder už bol priradený, nepriraďuje sa znova: ID $schiffId");
    }
    $skladnikId = null; $skladnikUsersData_loc = []; $assignedRegularSkladnik = false;
    try {
        $stmtAllSkl_loc = $pdo->prepare("SELECT id, name FROM users WHERE role1 = 'skladnik' AND is_active = 1 AND oddelenie_id = ? ORDER BY name ASC");
        $stmtAllSkl_loc->execute([$oddelenie_id]); $skladnikUsersData_loc = $stmtAllSkl_loc->fetchAll(PDO::FETCH_ASSOC);
        $skladnici_loc = array_column($skladnikUsersData_loc, 'id'); $countSkladnici_loc = count($skladnici_loc);
        if ($countSkladnici_loc > 0) {
            $stmtSkladnikRef_loc = $pdo->prepare("SELECT skladnik_ref_date, skladnik_ref_starts_s1 FROM Oddelenia WHERE oddelenie_id = ?");
            $stmtSkladnikRef_loc->execute([$oddelenie_id]); $skladnikRefData_loc = $stmtSkladnikRef_loc->fetch(PDO::FETCH_ASSOC);
            $refDateStr_loc = $skladnikRefData_loc['skladnik_ref_date'] ?? '2025-04-14';
            $startsS1_loc = (bool)($skladnikRefData_loc['skladnik_ref_starts_s1'] ?? true);
            $refDate_loc = new DateTimeImmutable($refDateStr_loc);
            $targetDateForDiff_loc = new DateTimeImmutable($targetDate->format('Y-m-d'));
            $daysDiff_loc = $refDate_loc->diff($targetDateForDiff_loc)->days; $blockIndex_loc = floor($daysDiff_loc / 14);
            $startIndex_loc = $startsS1_loc ? 0 : 1;
            $preferredSkladnikIndex_loc = ($startIndex_loc + $blockIndex_loc) % $countSkladnici_loc;
            error_log("Výpočet Skladníka: RefDate=$refDateStr_loc, StartsS1=" . ($startsS1_loc?'true':'false') . ", DaysDiff=$daysDiff_loc, Block=$blockIndex_loc, StartIdx=$startIndex_loc, PrefIndex=$preferredSkladnikIndex_loc");
            for ($i = 0; $i < $countSkladnici_loc; $i++) {
                $tryIdx_loc = ($preferredSkladnikIndex_loc + $i) % $countSkladnici_loc; $tryId_loc = $skladnici_loc[$tryIdx_loc];
                if (isset($presentUsers[$tryId_loc]) && !in_array($tryId_loc, $busyIds, true)) {
                    $skladnikId = $tryId_loc; $assignedRegularSkladnik = true;
                    $skladnikNameFound_loc = ''; foreach($skladnikUsersData_loc as $skud_loc) if($skud_loc['id'] == $tryId_loc) $skladnikNameFound_loc = $skud_loc['name'];
                    error_log("Skladník (Cyklus): ID $skladnikId ($skladnikNameFound_loc) (Offset $i od pref. indexu $preferredSkladnikIndex_loc)"); break;
                }
            }
        }
    } catch (Exception $e) { error_log("Chyba výpočtu Skladníka pre $dateStr: " . $e->getMessage()); }
    if (!$assignedRegularSkladnik) error_log("Rola Skladník nemohla byť priradená pre $dateStr.");
    if ($skladnikId) {
        $assignments[] = ['user_id' => $skladnikId, 'assigned_role' => 'skladnik', 'machine_number' => null];
        $busyIds[] = $skladnikId;
    }
    $strojnikAssignments = []; $strojnikCandidatesForActiveMachines = []; $allPotentialMachinists = [];
    try {
        $q_cands = $pdo->prepare("SELECT id, name, strojnik_order FROM users WHERE strojnik_order IS NOT NULL AND is_active = 1 AND oddelenie_id = ? ORDER BY strojnik_order");
        $q_cands->execute([$oddelenie_id]);
        while ($r = $q_cands->fetch(PDO::FETCH_ASSOC)) $allPotentialMachinists[(int)$r['strojnik_order']] = ['id' => (int)$r['id'], 'name' => $r['name']];
    } catch (PDOException $e) { error_log("Chyba DB pri načítavaní kandidátov strojníkov: " . $e->getMessage()); if (empty($allPotentialMachinists)) goto process_final_roles; }
    if (empty($allPotentialMachinists)) { error_log("Nie sú definovaní kandidáti strojníkov pre oddelenie $log_oddelenie_id."); goto process_final_roles; }
    $numberOfActiveMachines = count($activeMachineNumbers);
    if ($numberOfActiveMachines == 0) error_log("Žiadne aktívne stroje pre $dateStr v oddelení $log_oddelenie_id.");
    else {
        error_log("Aktívne stroje pre $dateStr v oddelení $log_oddelenie_id: " . implode(', ', $activeMachineNumbers));
        $startOrder = null; // Inicializácia

        // Logika určenia štartovacieho poradia s prioritami:
        // 1. Vynútené poradie z parametra funkcie (pri prvotnom manuálnom reštarte)
        if ($forcedStartOrder !== null) {
            $startOrder = $forcedStartOrder;
            error_log("Používa sa vynútené štartovacie poradie strojníkov: $startOrder pre oddelenie $log_oddelenie_id dňa " . $targetDate->format('Y-m-d'));
        // 2. Vynútené poradie z metadát (pri prepočte dňa, ktorý už bol manuálne reštartovaný)
        } elseif ($meta && !empty($meta['forced_start_order'])) {
            $startOrder = (int)$meta['forced_start_order'];
            error_log("Používa sa vynútené poradie z METADÁT: $startOrder pre oddelenie $log_oddelenie_id dňa " . $targetDate->format('Y-m-d'));
        // 3. Automatický výpočet podľa predchádzajúceho dňa (štandardné správanie)
        } else {
            $prevDayStrojnikOrders = [];
            try {
                $prevDay = findPreviousWorkingDay($targetDate, $pdo); $prevDayStr = $prevDay->format('Y-m-d');
                $prevStmt = $pdo->prepare("SELECT u.strojnik_order FROM assignments a JOIN users u ON a.user_id = u.id WHERE a.assignment_date = ? AND a.assigned_role = 'strojnik' AND u.strojnik_order IS NOT NULL AND u.oddelenie_id = ?");
                $prevStmt->execute([$prevDayStr, $oddelenie_id]);
                $prevDayStrojnikOrders = array_map('intval', $prevStmt->fetchAll(PDO::FETCH_COLUMN));
                $prevDayStrojnikOrders = array_unique($prevDayStrojnikOrders); sort($prevDayStrojnikOrders);
            } catch (Exception $e) { error_log("Chyba načítania priradení z predch. dňa: " . $e->getMessage()); }

            $startOrder = 1;
            if (!empty($prevDayStrojnikOrders)) {
                $maxOrderPrevDay = max($prevDayStrojnikOrders); $blockEnd = $maxOrderPrevDay;
                $maxStrojnikOrderInSystem = !empty($allPotentialMachinists) ? max(array_keys($allPotentialMachinists)) : 9;
                $currentCheckOrder = ($maxOrderPrevDay % $maxStrojnikOrderInSystem) + 1; $safetyLoopCheck = 0;
                while (in_array($currentCheckOrder, $prevDayStrojnikOrders) && $safetyLoopCheck < ($maxStrojnikOrderInSystem + 5)) {
                    $blockEnd = $currentCheckOrder; $currentCheckOrder = ($currentCheckOrder % $maxStrojnikOrderInSystem) + 1; $safetyLoopCheck++;
                }
                $startOrder = ($blockEnd % $maxStrojnikOrderInSystem) + 1;
            }
        }
        $machinistKeys = array_keys($allPotentialMachinists); sort($machinistKeys);
        $startIndexInKeys = array_search($startOrder, $machinistKeys);
        if ($startIndexInKeys === false) { $startIndexInKeys = 0; if (!empty($machinistKeys)) $startOrder = $machinistKeys[0]; }
        $numMachinistKeys = count($machinistKeys); $presentCandidatesFoundCount = 0;
        if ($numMachinistKeys > 0) {
            for ($i = 0; $i < $numMachinistKeys; $i++) {
                if ($presentCandidatesFoundCount >= $numberOfActiveMachines) break;
                $currentKeyIndex = ($startIndexInKeys + $i) % $numMachinistKeys;
                $currentEvalOrder = $machinistKeys[$currentKeyIndex]; $machinistData = $allPotentialMachinists[$currentEvalOrder];
                $userId = $machinistData['id'];
                if (in_array($userId, $busyIds, true)) continue;
                if (!isset($presentUsers[$userId])) {
                    $strojnikAssignments[] = ['user_id' => $userId, 'assigned_role' => 'strojnik', 'machine_number' => 0];
                    $busyIds[] = $userId;
                } else {
                    if ($presentCandidatesFoundCount < $numberOfActiveMachines) {
                        $strojnikCandidatesForActiveMachines[] = ['user_id' => $userId, 'name' => $machinistData['name'], 'strojnik_order' => $currentEvalOrder];
                        $presentCandidatesFoundCount++;
                    }
                }
            }
        }
        if (!empty($strojnikCandidatesForActiveMachines) && !empty($activeMachineNumbers)) {
            $numMachinesToAssignThisRun = min(count($strojnikCandidatesForActiveMachines), count($activeMachineNumbers));
            if ($numMachinesToAssignThisRun > 0) {
                $candidatesForPermutation = array_slice($strojnikCandidatesForActiveMachines, 0, $numMachinesToAssignThisRun);
                $machinesToUseInPermutation = array_slice($activeMachineNumbers, 0, $numMachinesToAssignThisRun);
                $permutations = [];
                $generatePermutations = function (array $elements, callable $callback, array $currentPermutation = []) use (&$generatePermutations, $numMachinesToAssignThisRun) {
                    if (count($currentPermutation) === $numMachinesToAssignThisRun) { $callback($currentPermutation); return; }
                    if (empty($elements)) return;
                    foreach ($elements as $key => $element) {
                        $remainingElements = $elements; unset($remainingElements[$key]);
                        $newPermutation = $currentPermutation; $newPermutation[] = $element;
                        $generatePermutations(array_values($remainingElements), $callback, $newPermutation);
                    }
                };
                if(!empty($machinesToUseInPermutation)) $generatePermutations($machinesToUseInPermutation, function($p) use (&$permutations) { $permutations[] = $p; });
                $bestPermutation = null; $lowestPenaltyScore = PHP_INT_MAX;
                if (empty($permutations) && $numMachinesToAssignThisRun > 0) $bestPermutation = $machinesToUseInPermutation;
                foreach ($permutations as $currentMachinePermutation) {
                    if (count($currentMachinePermutation) !== $numMachinesToAssignThisRun) continue;
                    $currentTotalPenalty = 0;
                    for ($j = 0; $j < $numMachinesToAssignThisRun; $j++) {
                        $candidate = $candidatesForPermutation[$j]; $userId = $candidate['user_id'];
                        $assignedMachine = $currentMachinePermutation[$j]; $penaltyForPair = 0;
                        $lastMachine = getLastMachineAssignment($userId, $pdo, $targetDate);
                        if ($lastMachine !== null && $assignedMachine == $lastMachine) $penaltyForPair += 1000;
                        $numRecentHistoryCheck = count($machinesToUseInPermutation) - 1;
                        if ($numRecentHistoryCheck > 0) {
                            $recentHistory = getRecentMachineHistory($userId, $pdo, $targetDate, $numRecentHistoryCheck);
                            if (in_array($assignedMachine, $recentHistory)) $penaltyForPair += 200;
                        }
                        $daysSince = getDaysSinceLastOnMachine($userId, $assignedMachine, $pdo, $targetDate);
                        $penaltyForPair += round(50 / ($daysSince + 1));
                        $countInRecentHistory = getMachineCountInHistory($userId, $assignedMachine, $pdo, $targetDate, 20);
                        $penaltyForPair += $countInRecentHistory * 10;
                        $currentTotalPenalty += $penaltyForPair;
                    }
                    if ($currentTotalPenalty < $lowestPenaltyScore) {
                        $lowestPenaltyScore = $currentTotalPenalty; $bestPermutation = $currentMachinePermutation;
                    }
                }
                if ($bestPermutation) {
                    for ($j = 0; $j < $numMachinesToAssignThisRun; $j++) {
                        $candidate = $candidatesForPermutation[$j]; $assignedMachine = $bestPermutation[$j];
                        $isAlreadyAssignedStrojnik = false;
                        foreach($strojnikAssignments as $exAs) if($exAs['user_id'] == $candidate['user_id'] && $exAs['assigned_role'] == 'strojnik') $isAlreadyAssignedStrojnik=true;
                        if(!$isAlreadyAssignedStrojnik) {
                             $strojnikAssignments[] = ['user_id' => $candidate['user_id'], 'assigned_role' => 'strojnik', 'machine_number' => $assignedMachine];
                            $busyIds[] = $candidate['user_id'];
                        }
                    }
                }
            }
        }
    }
    $finalStrojnikUserIds = [];
    foreach($strojnikAssignments as $sa_final) {
        if(!in_array($sa_final['user_id'], $finalStrojnikUserIds)) {
            $assignments[] = $sa_final; $finalStrojnikUserIds[] = $sa_final['user_id'];
        }
    }
    process_final_roles:
    $mainSchiffUserId = null;
    foreach($assignments as $assign) if ($assign['assigned_role']==='schifflieder') $mainSchiffUserId=$assign['user_id'];
    if ($mainSchiffUserId !== null && $bruder && $domi) {
        $otherSchiffUser = null;
        if ($mainSchiffUserId === $bruder['id']) $otherSchiffUser = $domi;
        elseif ($mainSchiffUserId === $domi['id']) $otherSchiffUser = $bruder;
        if ($otherSchiffUser && isset($presentUsers[$otherSchiffUser['id']]) && !in_array($otherSchiffUser['id'], $busyIds, true)) {
            $busyIds[] = $otherSchiffUser['id'];
            error_log("Druhý Schifflieder (ID {$otherSchiffUser['id']}, {$otherSchiffUser['name']}) označený ako zaneprázdnený.");
        }
    }
    error_log("--- Priradenie roly Kontrola ---"); $kontrolaCount = 0;
    foreach ($presentUsers as $uid => $u) {
        if (!in_array((int)$uid, $busyIds, true) && ($veduciId === null || (int)$uid !== $veduciId) ) {
            $isVeduciAndSchiff = ($veduciId !== null && (int)$uid === $veduciId && $schiffId === $veduciId);
            if (!$isVeduciAndSchiff) {
                $assignments[] = ['user_id' => (int)$uid, 'assigned_role' => 'kontrola', 'machine_number' => null];
                $kontrolaCount++; error_log("Priradená Kontrola: $uid ({$u['name']})");
            }
        }
    }
    error_log("Rola Kontrola priradená $kontrolaCount používateľom.");
    error_log("--- Výpočet priradení ukončený pre $dateStr. Celkový počet: " . count($assignments) . " ---");
    return $assignments;
}

function saveAssignments(DateTimeInterface $date, array $assignments, ?int $oddelenie_id, PDO $pdo): void {
    $dateStr = $date->format('Y-m-d');
    error_log("--- Ukladanie priradení pre $dateStr ---");
    $isOuterTransactionActive = $pdo->inTransaction();
    try {
        if (!$isOuterTransactionActive) $pdo->beginTransaction();
        if ($oddelenie_id !== null) {
            $stmtDel = $pdo->prepare("DELETE a FROM assignments a JOIN users u ON a.user_id = u.id WHERE a.assignment_date = :assignment_date AND u.oddelenie_id = :oddelenie_id");
            $stmtDel->execute([':assignment_date' => $dateStr, ':oddelenie_id' => $oddelenie_id]);
            error_log("Vymazaných " . $stmtDel->rowCount() . " predchádzajúcich priradení pre $dateStr, oddelenie $oddelenie_id.");
        } else error_log("Upozornenie: saveAssignments volané bez oddelenie_id pre $dateStr.");
        $stmtIns = $pdo->prepare("INSERT INTO assignments (assignment_date, user_id, assigned_role, machine_number, assignment_order) VALUES (?, ?, ?, ?, ?)");
        $stmtUpdateUser = $pdo->prepare("UPDATE users SET last_machine_assigned = ?, last_assignment_date = ? WHERE id = ?");
        $insertedCount = 0;
        foreach ($assignments as $assignment) {
            if (!isset($assignment['user_id'], $assignment['assigned_role'])) { error_log("Preskakuje sa neplatný záznam: " . print_r($assignment, true)); continue; }
            $machineNumber = $assignment['machine_number'] ?? null; if ($machineNumber === '') $machineNumber = null;
            $assignmentOrder = $assignment['assignment_order'] ?? null;
            $stmtIns->execute([$dateStr, $assignment['user_id'], $assignment['assigned_role'], $machineNumber, $assignmentOrder]);
            $insertedCount++;
            if ($assignment['assigned_role'] === 'strojnik' && $machineNumber !== null && $machineNumber > 0) {
                try { $stmtUpdateUser->execute([$machineNumber, $dateStr, $assignment['user_id']]); }
                catch (PDOException $e) { error_log("Upozornenie DB: Nepodarilo sa aktualizovať last_machine_assigned pre {$assignment['user_id']} dňa $dateStr: " . $e->getMessage()); }
            }
        }
        error_log("Vložených $insertedCount nových priradení pre $dateStr.");
        if (!$isOuterTransactionActive) $pdo->commit();
        error_log("--- Priradenia úspešne uložené pre $dateStr ---");
    } catch (Exception $e) {
        if (!$isOuterTransactionActive && $pdo->inTransaction()) try { $pdo->rollBack(); } catch (PDOException $re) { error_log("Chyba pri rollbacku: " . $re->getMessage()); }
        error_log("!!! KRITICKÁ CHYBA pri ukladaní priradení pre $dateStr: " . $e->getMessage() . " !!!");
        throw new Exception("Nepodarilo sa uložiť priradenia pre $dateStr.", 0, $e);
    }
}
 
function deleteFutureDataFromDate(DateTimeInterface $date, ?int $oddelenie_id, PDO $pdo): bool {
    try {
        $dateStr = $date->format('Y-m-d'); $log_oddelenie_id = $oddelenie_id ?? 'VŠETKY';
        error_log("Mazanie budúcich dát od: $dateStr pre oddelenie ID: $log_oddelenie_id");
        $sqlDeleteAssignments = "DELETE a FROM assignments a JOIN users u ON a.user_id = u.id WHERE a.assignment_date >= ?";
        $paramsDeleteAssignments = [$dateStr];
        if ($oddelenie_id !== null) { $sqlDeleteAssignments .= " AND u.oddelenie_id = ?"; $paramsDeleteAssignments[] = $oddelenie_id; }
        $stmtDeleteAssignments = $pdo->prepare($sqlDeleteAssignments); $stmtDeleteAssignments->execute($paramsDeleteAssignments);
        error_log("Vymazaných " . $stmtDeleteAssignments->rowCount() . " záznamov z assignments.");
        $sqlDeleteMetadata = "DELETE FROM assignment_metadata WHERE assignment_date >= ?";
        $paramsDeleteMetadata = [$dateStr];
        if ($oddelenie_id !== null) { $sqlDeleteMetadata .= " AND oddelenie_id = ?"; $paramsDeleteMetadata[] = $oddelenie_id; }
        $stmtDeleteMetadata = $pdo->prepare($sqlDeleteMetadata); $stmtDeleteMetadata->execute($paramsDeleteMetadata);
        error_log("Vymazaných " . $stmtDeleteMetadata->rowCount() . " záznamov z assignment_metadata.");
        if ($oddelenie_id !== null && isset($_SESSION['username']) && $_SESSION['username'] === 'radis') {
            error_log("Používateľ 'radis' - spúšťam prepočítanie od $dateStr pre oddelenie $oddelenie_id");
            try {
                $today = new DateTimeImmutable('today'); $currentDate = new DateTimeImmutable($dateStr);
                while ($currentDate <= $today) {
                    if (isWorkingDay($currentDate, $pdo)) {
                        error_log("Prepočítavam pre: " . $currentDate->format('Y-m-d') . ", odd. $oddelenie_id");
                        regenerateAssignmentsForDates($currentDate, $oddelenie_id, $pdo);
                    }
                    $currentDate = $currentDate->modify('+1 day');
                }
            } catch (Exception $e) { error_log("Chyba pri prepočítavaní: " . $e->getMessage()); }
        }
        return true;
    } catch (Exception $e) { error_log("Chyba pri mazaní budúcich dát: " . $e->getMessage()); return false; }
}

function getAssignmentMetadataMeta(DateTimeInterface $date, ?int $oddelenie_id, PDO $pdo): ?array {
    if ($oddelenie_id === null) { error_log("getAssignmentMetadataMeta volané bez oddelenie_id."); return null; }
    $stmt = $pdo->prepare("SELECT signature, updated_at, last_absence, last_downtime, forced_start_order FROM assignment_metadata WHERE assignment_date = ? AND oddelenie_id = ?");
    $stmt->execute([$date->format('Y-m-d'), $oddelenie_id]);
    return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
}

function upsertAssignmentMetadataMeta(DateTimeInterface $date, string $signature, ?string $lastAbs, ?string $lastDown, ?int $oddelenie_id, PDO $pdo, ?string $forcedStartOrder = null): void {
    if ($oddelenie_id === null) { error_log("upsertAssignmentMetadataMeta volané bez oddelenie_id."); return; }
    $stmt = $pdo->prepare("INSERT INTO assignment_metadata (assignment_date, oddelenie_id, signature, updated_at, last_absence, last_downtime, forced_start_order) VALUES (?, ?, ?, NOW(), ?, ?, ?) ON DUPLICATE KEY UPDATE signature = VALUES(signature), updated_at = NOW(), last_absence = VALUES(last_absence), last_downtime = VALUES(last_downtime), forced_start_order = VALUES(forced_start_order)");
    $stmt->execute([$date->format('Y-m-d'), $oddelenie_id, $signature, $lastAbs, $lastDown, $forcedStartOrder]);
}

function assignMachineToCandidateInternal(?int $lastMachineAssigned, array $availableMachineNumbers, array $assignedMachineNumbersOnThisRun): ?int {
    $preferredMachine = match ($lastMachineAssigned) { 1 => 2, 2 => 3, 3 => 1, default => 1, };
    $eligibleMachines = array_diff($availableMachineNumbers, $assignedMachineNumbersOnThisRun);
    if (empty($eligibleMachines)) return null;
    if (in_array($preferredMachine, $eligibleMachines)) return $preferredMachine;
    else return reset($eligibleMachines);
}
 
function getMachineUsageStats(int $userId, PDO $pdo, int $lookback = 2): array {
    $since = (new DateTimeImmutable())->modify("-{$lookback} days")->format('Y-m-d');
    $stmt = $pdo->prepare("SELECT machine_number, COUNT(*) AS c FROM assignments WHERE user_id = :uid AND assignment_date >= :since AND machine_number BETWEEN 1 AND 3 GROUP BY machine_number");
    $stmt->bindValue(':uid', $userId, PDO::PARAM_INT); $stmt->bindValue(':since', $since, PDO::PARAM_STR);
    $stmt->execute(); return $stmt->fetchAll(PDO::FETCH_KEY_PAIR) ?: [];
}

function assignBalancedMachine(int $userId, ?int $lastMachineAssigned, array $availableMachines, array $assignedToday, PDO $pdo, int $lookback = 3 ): ?int {
    $free = array_values(array_diff($availableMachines, $assignedToday)); if (!$free) return null;
    $stats = getMachineUsageStats($userId, $pdo, $lookback);
    foreach ([1, 2, 3] as $m) if (!isset($stats[$m])) $stats[$m] = 0;
    $candidates = array_intersect_key($stats, array_flip($free)); asort($candidates);
    $leastUsed = array_keys($candidates, min($candidates), true);
    $preferredNext = match ($lastMachineAssigned) { 1 => 2, 2 => 3, 3 => 1, default => 1, };
    if (in_array($preferredNext, $leastUsed, true)) return $preferredNext;
    return reset($leastUsed);
}

function processAssignmentsForDisplay(array $assignments, array $allPresentUsers, ?string $departmentName = null): array {
    $schiffliederText = '---'; $skladnik = null; $machines = [1 => null, 2 => null, 3 => null];
    $kontrola = []; $assignedSchiffId = null; $veduciAssignedAsSchiff = false;
    $num_machines_to_display = 3; if ($departmentName === 'ID3') $num_machines_to_display = 2;
    foreach ($assignments as $assignment) {
         if (!is_array($assignment) || !isset($assignment['user_id'])) continue;
         $userId = (int)$assignment['user_id']; $role = $assignment['assigned_role'] ?? '';
        if ($role === 'schifflieder') {
            $assignedSchiffId = $userId;
            if (isset($allPresentUsers[$userId]) && $allPresentUsers[$userId]['role1'] === 'veduci') $veduciAssignedAsSchiff = true;
        }
    }
     $radisUser = null; $domiUser = null;
     foreach ($allPresentUsers as $uid => $uData) {
         if (($uData['username'] ?? '') === 'radis') $radisUser = $uData;
         if (($uData['username'] ?? '') === 'dominika') $domiUser = $uData;
     }
     if ($veduciAssignedAsSchiff && isset($allPresentUsers[$assignedSchiffId])) $schiffliederText = htmlspecialchars($allPresentUsers[$assignedSchiffId]['name']) . ' (Zástup)';
     elseif ($assignedSchiffId !== null && isset($allPresentUsers[$assignedSchiffId])) {
          $assignedName = htmlspecialchars($allPresentUsers[$assignedSchiffId]['name']); $otherUser = null;
          if ($radisUser && $radisUser['id'] == $assignedSchiffId) { if ($domiUser && isset($allPresentUsers[$domiUser['id']])) $otherUser = $domiUser; }
          elseif ($domiUser && $domiUser['id'] == $assignedSchiffId) { if ($radisUser && isset($allPresentUsers[$radisUser['id']])) $otherUser = $radisUser; }
          $schiffliederText = "<strong>" . $assignedName . "</strong>";
          if ($otherUser) $schiffliederText .= "<br>" . htmlspecialchars($otherUser['name']);
     } elseif ($assignedSchiffId !== null) $schiffliederText = "Schifflieder (ID: $assignedSchiffId) - neprítomný?";
    foreach ($assignments as $assignment) {
        if (!is_array($assignment) || !isset($assignment['user_id'], $assignment['assigned_role'])) continue;
        $userId = (int)$assignment['user_id']; $role = $assignment['assigned_role'];
        $machineNum = isset($assignment['machine_number']) ? (int)$assignment['machine_number'] : null;
        $userName = $allPresentUsers[$userId]['name'] ?? $assignment['name'] ?? 'Neznámy (' . $userId . ')';
        switch ($role) {
            case 'skladnik': $skladnik = $userName; break;
            case 'strojnik': if ($machineNum !== null && $machineNum >= 1 && $machineNum <= 3) $machines[$machineNum] = $userName; break;
            case 'kontrola': $kontrola[] = $userName; break;
        }
    }
    sort($kontrola); $relevant_machines = [];
    for ($i = 1; $i <= $num_machines_to_display; $i++) $relevant_machines[$i] = $machines[$i] ?? null;
    $machines = $relevant_machines;
    return [$schiffliederText, $skladnik, $machines, $kontrola];
}

/**
 * Prepočíta priradenia od zadaného dátumu s vynúteným štartovacím poradím strojníkov.
 * Spustí reťazovú reakciu prepočtu pre nasledujúce dni.
 */
function recalculateAssignmentsWithForcedStart(DateTimeInterface $startDate, int $forcedStartOrder, int $oddelenieId, PDO $pdo): void {
    $today = new DateTimeImmutable('today');
    // Prepočítame priradenia na 60 dní dopredu, aby sme pokryli budúcnosť
    $endDate = $today->modify('+60 days');

    $currentDate = DateTime::createFromInterface($startDate); // Použijeme meniteľný DateTime pre cyklus
    $isFirstDay = true;

    error_log("--- Spúšťam vynútený prepočet priradení pre oddelenie $oddelenieId ---");
    error_log("Dátum začiatku: " . $currentDate->format('Y-m-d'));
    error_log("Vynútené štartovacie poradie pre prvý deň: $forcedStartOrder");
    error_log("Dátum konca prepočtu: " . $endDate->format('Y-m-d'));

    while ($currentDate <= $endDate) {
        $currentDateImmutable = DateTimeImmutable::createFromMutable($currentDate);
        if (isWorkingDay($currentDateImmutable, $pdo)) {
            if ($isFirstDay) {
                // Pre prvý deň: použiť vynútené poradie a uložiť forced_start_order do metadát
                error_log("Prepočítavam priradenia pre: " . $currentDate->format('Y-m-d') . " (vynútený štart: $forcedStartOrder)");

                $assignments = calculateDailyAssignments($currentDateImmutable, $oddelenieId, $pdo, $forcedStartOrder);
                saveAssignments($currentDateImmutable, $assignments, $oddelenieId, $pdo);

                // Uloženie metadát s forced_start_order pre prvý deň
                $signature = md5(json_encode($assignments));
                upsertAssignmentMetadataMeta($currentDateImmutable, $signature, null, null, $oddelenieId, $pdo, (string)$forcedStartOrder);

                $isFirstDay = false; // Vynútenie platí len pre prvý deň v cykle
            } else {
                // Pre nasledujúce dni: vymazať existujúce priradenia a metadáta, potom prepočítať s novým automatickým cyklom
                error_log("Prepočítavam priradenia pre: " . $currentDate->format('Y-m-d') . " (nový automatický cyklus)");

                // Vymazanie existujúcich priradení a metadát
                $pdo->prepare("DELETE a FROM assignments a JOIN users u ON a.user_id = u.id WHERE a.assignment_date = ? AND u.oddelenie_id = ?")->execute([$currentDate->format('Y-m-d'), $oddelenieId]);
                $pdo->prepare("DELETE FROM assignment_metadata WHERE assignment_date = ? AND oddelenie_id = ?")->execute([$currentDate->format('Y-m-d'), $oddelenieId]);

                // Prepočítanie s automatickým cyklom (bez forced_start_order)
                $assignments = calculateDailyAssignments($currentDateImmutable, $oddelenieId, $pdo, null);
                saveAssignments($currentDateImmutable, $assignments, $oddelenieId, $pdo);

                // Uloženie metadát bez forced_start_order (automatický cyklus)
                $signature = md5(json_encode($assignments));
                upsertAssignmentMetadataMeta($currentDateImmutable, $signature, null, null, $oddelenieId, $pdo, null);
            }
        }
        $currentDate->modify('+1 day');
    }
    error_log("--- Vynútený prepočet priradení dokončený ---");
}


function regenerateAssignmentsForDates($dates, ?int $oddelenie_id, PDO $pdo): void {
    $dates = is_array($dates) ? $dates : [ $dates ];
    if ($oddelenie_id === null) { error_log("regenerateAssignmentsForDates volané bez oddelenie_id."); return; }
    foreach ($dates as $date) {
        try {
            $dstr = $date->format('Y-m-d'); error_log("Regenerujem priradenia pre: $dstr, odd. ID: $oddelenie_id");

            // Načítanie existujúcich metadát pre zachovanie forced_start_order
            $existingMeta = getAssignmentMetadataMeta($date, $oddelenie_id, $pdo);
            $preservedForcedStartOrder = $existingMeta['forced_start_order'] ?? null;

            // Vymazanie LEN priradení (metadáta zostávajú zachované)
            $pdo->prepare("DELETE a FROM assignments a JOIN users u ON a.user_id = u.id WHERE a.assignment_date = ? AND u.oddelenie_id = ?")->execute([$dstr, $oddelenie_id]);

            // Prepočítanie nových priradení
            $new_assignments = calculateDailyAssignments($date, $oddelenie_id, $pdo);
            saveAssignments($date, $new_assignments, $oddelenie_id, $pdo);
            $sig = md5(json_encode($new_assignments));

            // Aktualizácia LEN signature a updated_at v metadátach (forced_start_order zostáva nezmenené)
            if ($existingMeta) {
                // Ak metadáta existujú, aktualizuj len signature a updated_at
                $updateStmt = $pdo->prepare("UPDATE assignment_metadata SET signature = ?, updated_at = NOW() WHERE assignment_date = ? AND oddelenie_id = ?");
                $updateStmt->execute([$sig, $dstr, $oddelenie_id]);
                error_log("Regenerácia pre $dstr, odd. ID: $oddelenie_id dokončená (metadáta aktualizované)" . ($preservedForcedStartOrder ? " (zachované forced_start_order: $preservedForcedStartOrder)" : ""));
            } else {
                // Ak metadáta neexistujú, vytvor nové bez forced_start_order
                upsertAssignmentMetadataMeta($date, $sig, null, null, $oddelenie_id, $pdo, null);
                error_log("Regenerácia pre $dstr, odd. ID: $oddelenie_id dokončená (nové metadáta vytvorené)");
            }
        } catch (Exception $e) { error_log("Chyba pri regenerácii pre " . $date->format('Y-m-d') . ", odd. ID: $oddelenie_id: " . $e->getMessage()); }
    }
}

function getAbsencesForUser(int $userId, PDO $pdo): array {
     try { $stmt = $pdo->prepare("SELECT * FROM absences WHERE user_id = ? ORDER BY start_date DESC"); $stmt->execute([$userId]); return $stmt->fetchAll(PDO::FETCH_ASSOC); }
     catch (PDOException $e) { return []; }
}

function getFutureAbsences(PDO $pdo, ?int $oddelenie_id = null): array {
    $today = date('Y-m-d'); $sql = "SELECT a.*, u.name as user_name FROM absences a JOIN users u ON a.user_id = u.id WHERE a.end_date >= :today";
    $params = [':today' => $today];
    if ($oddelenie_id !== null) { $sql .= " AND u.oddelenie_id = :oddelenie_id"; $params[':oddelenie_id'] = $oddelenie_id; }
    $sql .= " ORDER BY a.start_date ASC, u.name ASC";
    try { $stmt = $pdo->prepare($sql); $stmt->execute($params); return $stmt->fetchAll(PDO::FETCH_ASSOC); }
    catch (PDOException $e) { $log_oddelenie = $oddelenie_id ?? 'VŠETKY'; error_log("Chyba pri načítavaní budúcich absencií pre oddelenie '$log_oddelenie': " . $e->getMessage()); return []; }
}

function deleteAbsence(int $absenceId, PDO $pdo): bool {
     try { $stmt = $pdo->prepare("DELETE FROM absences WHERE id = ?"); return $stmt->execute([$absenceId]); }
     catch (PDOException $e) { return false; }
}

function getFutureDowntimes(PDO $pdo, ?int $oddelenie_id): array {
     $today = date('Y-m-d'); $sql = "SELECT * FROM machine_downtime WHERE end_date >= :today";
    $params = [':today' => $today];
    if ($oddelenie_id !== null) { $sql .= " AND oddelenie_id = :oddelenie_id"; $params[':oddelenie_id'] = $oddelenie_id; }
    $sql .= " ORDER BY start_date ASC, machine_number ASC";
     try { $stmt = $pdo->prepare($sql); $stmt->execute($params); return $stmt->fetchAll(PDO::FETCH_ASSOC); }
     catch (PDOException $e) { return []; }
}

function deleteDowntime(int $downtimeId, PDO $pdo): bool {
      try { $stmt = $pdo->prepare("DELETE FROM machine_downtime WHERE id = ?"); return $stmt->execute([$downtimeId]); }
      catch (PDOException $e) { return false; }
}

function getAbsentUsersForDate(DateTimeInterface $date, ?int $oddelenie_id, PDO $pdo) {
    $dateStr = $date->format('Y-m-d'); $absentUsers = [];
    if ($oddelenie_id === null) { error_log("getAbsentUsersForDate volané bez oddelenie_id."); return []; }
    try {
        $sql = "SELECT a.user_id, a.absence_type, u.name as user_name FROM absences a JOIN users u ON a.user_id = u.id WHERE :current_date BETWEEN a.start_date AND a.end_date AND a.absence_type IN ('Celý deň', 'Iné', 'Ráno')";
        $params = [':current_date' => $dateStr];
        if ($oddelenie_id !== null) { $sql .= " AND u.oddelenie_id = :oddelenie_id"; $params[':oddelenie_id'] = $oddelenie_id; }
        $sql .= " ORDER BY u.name ASC";
        $stmt = $pdo->prepare($sql); $stmt->execute($params); $absentUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) { error_log("Chyba pri získavaní neprítomných: " . $e->getMessage()); }
    return $absentUsers;
}

function getHolidaysFromApi(int $year, string $countryCode = 'SK'): array {
    $url = "https://date.nager.at/api/v3/PublicHolidays/{$year}/{$countryCode}";
    $caCertPath = __DIR__ . '/cacert.pem';
    $contextOptions = ['ssl' => ['verify_peer' => true, 'verify_peer_name' => true, 'cafile' => $caCertPath], 'http' => ['timeout' => 10]];
    if (!file_exists($caCertPath)) {
        error_log("Upozornenie: CA súbor '$caCertPath' neexistuje. SSL overenie môže zlyhať.");
        // Fallback na systémové CA, ak lokálny neexistuje
        // $contextOptions['ssl']['cafile'] = '/etc/ssl/certs/ca-certificates.crt'; // Príklad pre Linux
        // Ak ani to nepomôže, zvážte dočasné vypnutie overenia pre vývoj (neodporúča sa pre produkciu)
        // $contextOptions['ssl']['verify_peer'] = false; $contextOptions['ssl']['verify_peer_name'] = false;
    }
    $context = stream_context_create($contextOptions);
    $response = @file_get_contents($url, false, $context);
    if ($response === false) { $error = error_get_last(); error_log("Chyba API sviatkov: " . ($error['message'] ?? 'Neznáma chyba')); return []; }
    $holidays = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) { error_log("Chyba JSON dekódovania sviatkov: " . json_last_error_msg()); return []; }
    return array_map(fn($h) => ['date' => $h['date'], 'name' => $h['name']], $holidays ?: []);
}

function isActiveMachine(int $machineNumber, DateTimeInterface $date, ?int $oddelenie_id, PDO $pdo): bool {
    $dateStr = $date->format('Y-m-d');
    try {
        $sql = "SELECT COUNT(*) FROM machine_downtime WHERE machine_number = :machine_number AND :current_date BETWEEN start_date AND end_date";
        $params = [':machine_number' => $machineNumber, ':current_date' => $dateStr];
        if ($oddelenie_id !== null) { $sql .= " AND oddelenie_id = :oddelenie_id"; $params[':oddelenie_id'] = $oddelenie_id; }
        $stmt = $pdo->prepare($sql); $stmt->execute($params);
        return (int)$stmt->fetchColumn() === 0;
    } catch (Exception $e) { error_log("Chyba kontroly stavu stroja: " . $e->getMessage()); return true; }
}

function insertHolidaysForYearInternal(int $year, PDO $pdo): int {
    $stmtCheck = $pdo->prepare("SELECT COUNT(*) FROM holidays WHERE year = ?"); $stmtCheck->execute([$year]);
    if ($stmtCheck->fetchColumn() > 0) { error_log("Sviatky pre rok $year už sú v DB."); return 0; }
    error_log("Načítavam sviatky pre rok $year z API...");
    $holidays = getHolidaysFromApi($year, 'SK');
    if (empty($holidays)) { error_log("Nepodarilo sa načítať sviatky pre rok $year z API."); return 0; }
    $insertStmt = $pdo->prepare("INSERT IGNORE INTO holidays (year, `date`, localName, name, countryCode) VALUES (?, ?, ?, ?, ?)");
    $insertCount = 0; $pdo->beginTransaction();
    try {
        foreach ($holidays as $holiday) {
            if (isset($holiday['date'], $holiday['localName'], $holiday['name'], $holiday['countryCode'])) {
                $d = DateTime::createFromFormat('Y-m-d', $holiday['date']);
                if ($d && $d->format('Y-m-d') === $holiday['date']) {
                     $insertStmt->execute([$year, $holiday['date'], $holiday['localName'], $holiday['name'], $holiday['countryCode']]);
                    if ($insertStmt->rowCount() > 0) $insertCount++;
                } else error_log("Neplatný formát dátumu '{$holiday['date']}' pre '{$holiday['name']}' v roku $year.");
            } else error_log("Neúplné dáta pre sviatok v roku $year: " . print_r($holiday, true));
        }
        $pdo->commit(); error_log("Úspešne vložených sviatkov pre rok $year: $insertCount."); return $insertCount;
    } catch (Exception $e) { if ($pdo->inTransaction()) $pdo->rollBack(); error_log("Chyba pri vkladaní sviatkov pre rok $year: " . $e->getMessage()); throw $e; }
}

function checkAndLoadHolidays(PDO $pdo): array {
    $messages = []; $currentYear = (int)date('Y'); $nextYear = $currentYear + 1;
    $yearsToCheck = [$currentYear, $nextYear];
    foreach ($yearsToCheck as $year) {
        try {
             $stmtCheck = $pdo->prepare("SELECT COUNT(*) FROM holidays WHERE year = ?"); $stmtCheck->execute([$year]);
            if ($stmtCheck->fetchColumn() == 0) {
                $inserted = insertHolidaysForYearInternal($year, $pdo);
                if ($inserted > 0) $messages[] = "Auto. načítané sviatky pre rok $year ($inserted).";
                elseif (empty(getHolidaysFromApi($year, 'SK'))) $messages[] = "Nepodarilo sa auto. načítať sviatky pre rok $year (API problém?).";
                else $messages[] = "Sviatky pre rok $year sa nepodarilo auto. vložiť.";
            }
        } catch (Exception $e) {
            $messages[] = "Kritická chyba pri spracovaní sviatkov pre rok $year: " . $e->getMessage();
            error_log("FATÁLNA chyba pri spracovaní sviatkov pre rok $year: " . $e->getMessage());
        }
    }
    return $messages;
}

function getHolidaysForMonth($month, $year, PDO $pdo) {
    $startDate = sprintf('%04d-%02d-01', $year, $month); $endDate = date('Y-m-t', strtotime($startDate));
    $stmt = $pdo->prepare("SELECT date, localName FROM holidays WHERE date BETWEEN ? AND ? ORDER BY date");
    $stmt->execute([$startDate, $endDate]); $holidays = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) $holidays[$row['date']] = $row['localName'];
    return $holidays;
}

function getUserAbsencesForMonthWithType(int $userId, int $month, int $year, PDO $pdo): array {
    $absences = []; $startDateOfMonth = new DateTimeImmutable("$year-$month-01");
    $endDateOfMonth = $startDateOfMonth->modify('last day of this month');
    $stmt = $pdo->prepare("SELECT start_date, end_date, absence_type FROM absences WHERE user_id = :user_id AND start_date <= :month_end AND end_date >= :month_start");
    $stmt->execute([':user_id' => $userId, ':month_start' => $startDateOfMonth->format('Y-m-d'), ':month_end' => $endDateOfMonth->format('Y-m-d')]);
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $current = new DateTimeImmutable($row['start_date']); $last = new DateTimeImmutable($row['end_date']);
        while ($current <= $last) {
            if ($current->format('n') == $month && $current->format('Y') == $year) $absences[$current->format('Y-m-d')] = $row['absence_type'];
            $current = $current->modify('+1 day');
        }
    }
    return $absences;
}

function getUserVacationsForMonth($userId, $month, $year, PDO $pdo) {
    $startDate = sprintf('%04d-%02d-01', $year, $month); $endDate = date('Y-m-t', strtotime($startDate));
    $stmt = $pdo->prepare("SELECT a.start_date, a.end_date, a.absence_type FROM absences a WHERE a.user_id = ? AND ((a.start_date BETWEEN ? AND ?) OR (a.end_date BETWEEN ? AND ?) OR (a.start_date <= ? AND a.end_date >= ?)) ORDER BY a.start_date");
    $stmt->execute([$userId, $startDate, $endDate, $startDate, $endDate, $startDate, $endDate]); $vacations = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $start = new DateTime($row['start_date']); $end = new DateTime($row['end_date']);
        $interval = new DateInterval('P1D'); $dateRange = new DatePeriod($start, $interval, $end->modify('+1 day'));
        foreach ($dateRange as $date) {
            $dateStr = $date->format('Y-m-d');
            if ($date->format('Y-m') == sprintf('%04d-%02d', $year, $month)) $vacations[$dateStr] = $row['absence_type'];
        }
    }
    return $vacations;
}

/* ==================================================
   PERSISTENT LOGIN (REMEMBER ME) FUNCTIONS
   ================================================== */

/**
 * Generuje bezpečný náhodný token.
 * @param int $length Dĺžka tokenu v bajtoch (výsledná hex dĺžka bude 2x väčšia).
 * @return string Hexadecimálny reťazec tokenu.
 * @throws Exception Ak sa nepodarí vygenerovať dostatočne náhodné bajty.
 */
function generateToken(int $length = 32): string {
    if ($length < 16) { // Minimálna odporúčaná dĺžka
        throw new Exception("Dĺžka tokenu musí byť aspoň 16 bajtov.");
    }
    return bin2hex(random_bytes($length));
}

/**
 * Uloží nový persistentný login token do databázy.
 * @param int $userId ID používateľa.
 * @param string $series Unikátny identifikátor série.
 * @param string $token Nehashovaný token.
 * @param PDO $pdo PDO spojenie.
 * @param string $duration Trvanie platnosti tokenu (napr. '+1 month').
 * @return bool True pri úspechu, False pri zlyhaní.
 */
function storePersistentLogin(int $userId, string $series, string $token, PDO $pdo, string $duration = '+1 month'): bool {
    try {
        $tokenHash = hash('sha256', $token); // Hashujeme token
        $expiresAt = (new DateTimeImmutable())->modify($duration)->format('Y-m-d H:i:s');

        $stmtDeleteExpired = $pdo->prepare("DELETE FROM persistent_logins WHERE user_id = ? AND expires_at < NOW()");
        $stmtDeleteExpired->execute([$userId]);

        $stmt = $pdo->prepare("INSERT INTO persistent_logins (user_id, series, token_hash, expires_at) VALUES (?, ?, ?, ?)");
        return $stmt->execute([$userId, $series, $tokenHash, $expiresAt]);
    } catch (PDOException $e) {
        error_log("Chyba pri ukladaní persistentného prihlásenia pre používateľa $userId: " . $e->getMessage());
        return false;
    } catch (Exception $e) {
        error_log("Chyba pri generovaní tokenu alebo dátumu: " . $e->getMessage());
        return false;
    }
}

/**
 * Overí persistentný login cookie a prihlási používateľa, ak je platná.
 * Vykoná rotáciu tokenu pri úspešnom prihlásení cez cookie.
 * @param PDO $pdo PDO spojenie.
 * @return bool True, ak bol používateľ úspešne prihlásený cez cookie, False inak.
 */
function checkRememberMeCookie(PDO $pdo): bool {
    if (!isset($_COOKIE['remember_me'])) {
        return false;
    }

    $cookieValue = $_COOKIE['remember_me'];
    $parts = explode('|', $cookieValue);

    if (count($parts) !== 3) {
        deletePersistentLoginCookie(); return false;
    }

    list($userId, $series, $token) = $parts;

    if (!is_numeric($userId) || empty($series) || empty($token)) {
        deletePersistentLoginCookie(); return false;
    }
    $userId = (int)$userId;

    try {
        $stmt = $pdo->prepare("SELECT * FROM persistent_logins WHERE user_id = ? AND series = ? AND expires_at > NOW()");
        $stmt->execute([$userId, $series]);
        $loginData = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($loginData && hash_equals($loginData['token_hash'], hash('sha256', $token))) {
            $_SESSION['user_id'] = $userId;
            loadUserDataIntoSession($userId, $pdo); // Načítanie ostatných údajov
            session_regenerate_id(true);

            $newToken = generateToken();
            $newTokenHash = hash('sha256', $newToken);
            $stmtUpdate = $pdo->prepare("UPDATE persistent_logins SET token_hash = ?, created_at = NOW(), expires_at = ? WHERE id = ?");
            $stmtUpdate->execute([$newTokenHash, (new DateTimeImmutable())->modify('+1 month')->format('Y-m-d H:i:s'), $loginData['id']]);
            setPersistentLoginCookie($userId, $series, $newToken);
            return true;
        } else {
            if ($loginData) { // Sériu sme našli, ale token bol zlý
                 deletePersistentLoginsForUser($userId, $pdo);
                 error_log("Nesúlad persistentného tokenu pre používateľa $userId, séria $series. Všetky tokeny pre používateľa zmazané.");
            }
            deletePersistentLoginCookie();
            return false;
        }
    } catch (Exception $e) { // Zachytí PDOException aj Exception z generateToken
         error_log("Chyba počas kontroly persistentného prihlásenia: " . $e->getMessage());
         deletePersistentLoginCookie();
         return false;
    }
}

/**
 * Nastaví persistentný login cookie v prehliadači.
 * @param int $userId ID používateľa.
 * @param string $series Identifikátor série.
 * @param string $token Nehashovaný token.
 * @param int $expirySeconds Doba platnosti cookie v sekundách.
 */
function setPersistentLoginCookie(int $userId, string $series, string $token, int $expirySeconds = 60 * 60 * 24 * 30): void { // 30 dní
    $cookieValue = $userId . '|' . $series . '|' . $token;
    $secure = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';
    // Získanie cesty z configu, ak je definovaná, inak default '/'
    $cookiePath = defined('APP_BASE_PATH') ? rtrim(APP_BASE_PATH, '/') . '/' : '/';

    setcookie('remember_me', $cookieValue, [
        'expires' => time() + $expirySeconds,
        'path' => $cookiePath,
        'domain' => $_SERVER['HTTP_HOST'], // Nastavte správnu doménu, ak je to potrebné
        'secure' => $secure,
        'httponly' => true,
        'samesite' => 'Lax',
    ]);
}

/**
 * Zmaže persistentný login cookie z prehliadača.
 */
function deletePersistentLoginCookie(): void {
    $secure = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';
    $cookiePath = defined('APP_BASE_PATH') ? rtrim(APP_BASE_PATH, '/') . '/' : '/';
    setcookie('remember_me', '', [
        'expires' => time() - 3600,
        'path' => $cookiePath,
        'domain' => $_SERVER['HTTP_HOST'], // Nastavte správnu doménu
        'secure' => $secure,
        'httponly' => true,
        'samesite' => 'Lax',
    ]);
}

/**
 * Zmaže všetky persistentné login záznamy pre daného používateľa.
 * @param int $userId ID používateľa.
 * @param PDO $pdo PDO spojenie.
 * @return bool True pri úspechu, False pri zlyhaní.
 */
function deletePersistentLoginsForUser(int $userId, PDO $pdo): bool {
    try {
        $stmt = $pdo->prepare("DELETE FROM persistent_logins WHERE user_id = ?");
        return $stmt->execute([$userId]);
    } catch (PDOException $e) {
        error_log("Chyba pri mazaní všetkých persistentných prihlásení pre používateľa $userId: " . $e->getMessage());
        return false;
    }
}

/* ==================================================
   END PERSISTENT LOGIN FUNCTIONS
   ================================================== */

?>
