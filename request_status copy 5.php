<?php
// ======================================================================
// ===== Začiatok PHP Kódu ==============================================
// ======================================================================

// Potlačenie niektorých PHP upozornení (môžete upraviť podľa potreby servera)
error_reporting(E_ALL & ~E_DEPRECATED & ~E_NOTICE);
ini_set('display_errors', '1'); // Zapnutie zobrazenia chýb počas vývoja - V produkcii nastavte na 0!

// Základné nastavenia a funkcie - uistite sa, že tieto súbory existujú a sú správne
require_once 'config.php'; // Váš konfiguračný súbor (napr. pre DB)
require_once 'functions.php'; // Súbor s vašimi pomocnými funkciami (napr. getUserById, requireLogin, getDbConnection)

// Overenie prihlásenia používateľa (z functions.php)
requireLogin();

// Získanie PDO spojenia (z functions.php)
$pdo = getDbConnection();

// Načítanie údajov prihláseného používateľa (z functions.php)
$loggedInUserId = $_SESSION['user_id'] ?? 0;
$loggedUser = null;
if (function_exists('getUserById')) { // Kontrola existencie funkcie
    $loggedUser = getUserById($loggedInUserId, $pdo);
} else {
    // Záložná možnosť, ak funkcia neexistuje, ale meno je v session
    $loggedUser = ['name' => $_SESSION['user_name'] ?? 'Neznámy používateľ'];
}

// Inicializácia premenných pre spracovanie a zobrazenie
$externalStatusResult = null; // Výsledok z minv.sk
$errorMessage = null;         // Chybové hlášky pre používateľa
$successMessage = null;       // Úspešné hlášky pre používateľa (napr. o nájdení variácie)
$uploadedImagePath = null;    // Cesta k nahranému obrázku (ak bol nahraný cez PHP)
$originalRequestNumberForDisplay = ''; // Na uchovanie pôvodne zadaného/OCR čísla pre zobrazenie vo formulári

// --- Spracovanie Formulára (po odoslaní metódou POST) ---
if ($_SERVER['REQUEST_METHOD'] === 'POST') {

    // Uchováme číslo zadané vo formulári pre prípadné opätovné zobrazenie
    $originalRequestNumberForDisplay = isset($_POST['request_number']) ? htmlspecialchars(trim($_POST['request_number']), ENT_QUOTES, 'UTF-8') : '';

    // 1. Spracovanie nahrávania obrázku (ak bol súbor vybraný)
    if (isset($_FILES['request_image']) && $_FILES['request_image']['error'] === UPLOAD_ERR_OK) {
        $uploadDir = 'uploads/'; // Adresár pre nahrávané súbory
        // Vytvorenie adresára, ak neexistuje
        if (!file_exists($uploadDir)) {
            if (!mkdir($uploadDir, 0775, true) && !is_dir($uploadDir)) { // Používame 0775 pre lepšiu bezpečnosť
                 $errorMessage = "Chyba: Nepodarilo sa vytvoriť adresár pre nahrávanie súborov: {$uploadDir}";
            }
        }
        // Kontrola zapisovateľnosti adresára
        if (is_writable($uploadDir)) {
            $fileName = basename($_FILES['request_image']['name']); // Očistenie mena súboru
            $fileType = $_FILES['request_image']['type'];
            $fileTmpName = $_FILES['request_image']['tmp_name'];
            $fileSize = $_FILES['request_image']['size'];
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif']; // Povolené typy súborov

            // Validácia typu a veľkosti súboru
            if (!in_array($fileType, $allowedTypes)) {
                $errorMessage = "Neplatný formát súboru. Povolené formáty sú: JPG, PNG, GIF.";
            } elseif ($fileSize > 5 * 1024 * 1024) { // 5MB limit
                $errorMessage = "Súbor je príliš veľký. Maximálna veľkosť je 5MB.";
            } else {
                // Vytvorenie unikátneho a bezpečného mena súboru
                $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
                $newFileName = uniqid('img_', true) . '.' . $fileExtension;
                $destination = $uploadDir . $newFileName;

                // Presunutie nahraného súboru
                if (move_uploaded_file($fileTmpName, $destination)) {
                    $uploadedImagePath = $destination; // Cesta k úspešne nahranému súboru
                } else {
                    $errorMessage = "Nastala chyba pri presúvaní nahraného súboru na server.";
                }
            }
        } else {
             $errorMessage = "Chyba: Do adresára '{$uploadDir}' sa nedá zapisovať. Skontrolujte oprávnenia servera.";
        }
    } elseif (isset($_FILES['request_image']) && $_FILES['request_image']['error'] !== UPLOAD_ERR_NO_FILE) {
        // Spracovanie iných chýb pri nahrávaní
        $uploadErrors = [
            UPLOAD_ERR_INI_SIZE   => 'Nahraný súbor prekročil direktívu upload_max_filesize v php.ini.',
            UPLOAD_ERR_FORM_SIZE  => 'Nahraný súbor prekročil direktívu MAX_FILE_SIZE specifikovanú v HTML formulári.',
            UPLOAD_ERR_PARTIAL    => 'Súbor bol nahraný len čiastočne.',
            UPLOAD_ERR_NO_TMP_DIR => 'Chýba dočasný adresár.',
            UPLOAD_ERR_CANT_WRITE => 'Zlyhal zápis súboru na disk.',
            UPLOAD_ERR_EXTENSION  => 'PHP rozšírenie zastavilo nahrávanie súboru.',
        ];
        $errorCode = $_FILES['request_image']['error'];
        $errorMessage = $uploadErrors[$errorCode] ?? "Neznáma chyba pri nahrávaní obrázka (kód: {$errorCode}).";
    }

    // 2. Spracovanie stlačenia tlačidla "Zisti stav žiadosti"
    if (isset($_POST['check_status'])) {
        $requestNumberToCheck = $originalRequestNumberForDisplay; // Použijeme číslo z formulára (mohlo byť zadané alebo z OCR)

        if (empty($requestNumberToCheck)) {
            $errorMessage = "Prosím, zadajte alebo nechajte rozpoznať číslo žiadosti.";
        } else {
            // a) Prvý pokus o získanie stavu
            try {
                $initialResult = getExternalRequestStatus($requestNumberToCheck);
                $externalStatusResult = $initialResult; // Predvolene nastavíme tento výsledok

                // b) Skontrolujeme, či prvý pokus vrátil PRESNE požadovaný reťazec "neevidovaná" a či má číslo správnu dĺžku
                $exactNotFoundString = "Žiadosť o vydanie dokladu s uvedeným číslom nie je v systéme evidovaná.";
                $expectedLength = 19; // UPRAVTE podľa skutočného formátu čísla žiadosti!

                if (
                    isset($initialResult['status']) &&
                    $initialResult['status'] === $exactNotFoundString &&
                    strlen($requestNumberToCheck) === $expectedLength
                ) {
                    // ----- Spustíme logiku testovania 2/Z variácií -----
                    $baseNumber = substr($requestNumberToCheck, 0, -3);
                    $originalSuffix = substr($requestNumberToCheck, -3);

                    // Vygenerujeme variácie len ak suffix obsahuje '2' alebo 'Z'
                    if (strpos($originalSuffix, '2') !== false || strpos($originalSuffix, 'Z') !== false) {

                        $variations = generateSuffixVariations($originalSuffix);
                        $successfulVariationResult = null; // Uchováme prvý úspešný výsledok
                        $workingVariationNumber = null;    // A jeho číslo
                        $checkedCount = 0;                 // Počítadlo pokusov

                        foreach ($variations as $variation) {
                            if ($variation === $originalSuffix) continue; // Pôvodné sme už skúsili

                            $checkedCount++;
                            if ($checkedCount > 8) break; // Bezpečnostná brzda (max 8 kombinácií)

                            $newRequestNumber = $baseNumber . $variation;
                            usleep(250000); // Pauza 250ms pred ďalším requestom

                            try {
                                $tempResult = getExternalRequestStatus($newRequestNumber);
                                // Skontrolujeme, či výsledok variácie NIE JE presne ten istý "not found" reťazec
                                if (isset($tempResult['status']) && $tempResult['status'] !== $exactNotFoundString) {
                                    // Našli sme úspešnú variáciu!
                                    // Ak je to prvá úspešná, uložíme si ju
                                    if ($successfulVariationResult === null) {
                                        $successfulVariationResult = $tempResult;
                                        $workingVariationNumber = $newRequestNumber;
                                    }
                                    // Pokračujeme v testovaní ostatných variácií podľa zadania
                                }
                            } catch (Exception $e) {
                                error_log("Chyba pri skúšaní variácie '$newRequestNumber': " . $e->getMessage());
                                // Ignorujeme chybu pri variácii a pokračujeme
                            }
                        } // Koniec foreach

                        // Vyhodnotenie po skončení testovania variácií
                        if ($successfulVariationResult !== null) {
                            $externalStatusResult = $successfulVariationResult; // Použijeme výsledok prvej úspešnej variácie
                            $successMessage = "Stav bol nájdený pre upravené číslo: " . htmlspecialchars($workingVariationNumber);
                            $originalRequestNumberForDisplay = $workingVariationNumber; // Aktualizujeme číslo vo formulári
                            $errorMessage = null; // Vymažeme pôvodnú "not found" hlášku
                        } else {
                            // Ak žiadna variácia neuspela, zobrazíme špecifickú chybovú hlášku
                            $errorMessage = "Žiadosť nebola nájdená ani po skontrolovaní 2/Z variácií v posledných 3 znakoch.";
                            // $externalStatusResult zostáva nastavený na pôvodný výsledok ($initialResult)
                        }
                    } // Koniec if (obsahuje suffix 2 alebo Z)
                    // Ak suffix neobsahuje 2 ani Z, tak sa variácie netestujú a zostáva pôvodný "not found" výsledok

                } // Koniec if (podmienka pre spustenie variácií)

            } catch (Exception $e) {
                // Chyba pri prvotnom volaní getExternalRequestStatus
                $errorMessage = "Nastala chyba pri zisťovaní stavu žiadosti na minv.sk: " . $e->getMessage();
                $externalStatusResult = null; // Resetneme výsledok
            }
        } // Koniec else (validácia vstupného čísla)
    } // Koniec if (isset($_POST['check_status']))
} // Koniec if ($_SERVER['REQUEST_METHOD'] === 'POST')

// --- Definície PHP funkcií ---

/**
 * Získa stav žiadosti z externého systému minv.sk
 * @param string $requestNumber Číslo žiadosti
 * @return array Asociatívne pole s kľúčmi 'status' a voliteľne 'details'
 * @throws Exception Ak nastane chyba komunikácie alebo server vráti chybu
 */
function getExternalRequestStatus($requestNumber) {
    $url = 'https://www.minv.sk/?zistenie-stavu-spracovania-ziadosti';
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, "cislo=" . urlencode($requestNumber));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36'); // Aktualizovaný User Agent
    curl_setopt($ch, CURLOPT_TIMEOUT, 20);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10); // Timeout pre nadviazanie spojenia
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
    // curl_setopt($ch, CURLOPT_COOKIEFILE, ''); // Povolí session cookies, ak by bolo treba

    $response = curl_exec($ch);
    $curlErrorNum = curl_errno($ch);
    $curlErrorMsg = curl_error($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($curlErrorNum) throw new Exception("Chyba pri komunikácii so serverom minv.sk (cURL Error {$curlErrorNum}): " . $curlErrorMsg);
    if ($httpCode !== 200) throw new Exception("Server minv.sk vrátil neočakávaný stavový kód: {$httpCode}");
    if ($response === false || $response === '') throw new Exception("Server minv.sk vrátil prázdnu alebo neplatnú odpoveď.");

    // Extrakcia výsledku z HTML
    $result = ['status' => null, 'details' => null];
    $statusFound = false;
    $exactNotFoundString = "Žiadosť o vydanie dokladu s uvedeným číslom nie je v systéme evidovaná.";

    // Hľadanie stavu v <h4>
    if (preg_match('/<h4[^>]*>(.*?)<\/h4>/is', $response, $matches)) {
        $statusText = trim(strip_tags($matches[1]));
        // Normalizácia na presný reťazec, ak ide o "not found"
        if (mb_stripos($statusText, 'nie je v systéme evidovaná') !== false || mb_stripos($statusText, 'nebola nájdená') !== false) {
            $result['status'] = $exactNotFoundString;
        } else {
            $result['status'] = $statusText;
        }
        $statusFound = true;
    }

    // Ak H4 neobsahovalo status, skúsime nájsť známe texty v celom response
    if (!$statusFound) {
         if (mb_stripos($response, 'nie je v systéme evidovaná') !== false || mb_stripos($response, 'nebola nájdená') !== false) {
              $result['status'] = $exactNotFoundString;
              $statusFound = true;
         } elseif (mb_stripos($response, 'nesprávny formát') !== false) {
              $result['status'] = "Zadané číslo žiadosti má nesprávny formát.";
              $statusFound = true;
         }
    }

     // Ak sa stále nenašiel žiadny rozpoznateľný stav
     if (!$statusFound) {
         $result['status'] = "Nepodarilo sa zistiť stav žiadosti (neznáma odpoveď zo servera).";
         // error_log("MINV Response (Unknown): " . substr(strip_tags($response), 0, 500));
     }

    // Hľadanie detailov v <div class="text">
    if (preg_match('/<div class="text"[^>]*>(.*?)<\/div>/is', $response, $matches_details)) {
        $details = trim(strip_tags($matches_details[1], '<br><p><a>')); // Ponecháme aj linky
        $details = preg_replace('/\s+/', ' ', $details); // Nahradíme viacnásobné medzery
        $details = preg_replace('/<br\s*\/?>/i', "\n", $details); // Nahradíme <br> za nový riadok pre nl2br
        $result['details'] = trim($details);
    }

    return $result;
}

/**
 * Generuje všetky možné variácie posledných 3 znakov zámenou '2' za 'Z' a 'Z' za '2'.
 * @param string $suffix Pôvodné posledné 3 znaky.
 * @return array Pole unikátnych variácií (vrátane pôvodnej).
 */
function generateSuffixVariations($suffix) {
    if (strlen($suffix) !== 3) return [$suffix]; // Kontrola dĺžky
    $variations = [];
    $queue = [$suffix];
    $processed = [$suffix => true];
    while (!empty($queue)) {
        $current = array_shift($queue);
        $variations[] = $current;
        for ($i = 0; $i < 3; $i++) {
            $char = $current[$i];
            $swappedChar = null;
            if ($char === '2') $swappedChar = 'Z';
            elseif ($char === 'Z') $swappedChar = '2';
            if ($swappedChar !== null) {
                $nextVariation = substr_replace($current, $swappedChar, $i, 1);
                if (!isset($processed[$nextVariation])) {
                    $queue[] = $nextVariation;
                    $processed[$nextVariation] = true;
                }
            }
        }
    }
    return array_unique($variations); // Vráti unikátne hodnoty
}

// --- Určenie návratovej URL ---
$returnUrl = $_GET['return'] ?? 'dashboard.php';
// Jednoduchá validácia, aby sa predišlo XSS cez return parameter
if (!preg_match('/^[a-zA-Z0-9_\-]+\.php$/', basename($returnUrl))) {
     $returnUrl = 'dashboard.php'; // Ak formát nesedí, vrátime na dashboard
}

?>
<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Zistenie stavu žiadosti</title>
    <script src="https://cdn.jsdelivr.net/npm/tesseract.js@5/dist/tesseract.min.js"></script> <style>
        /* Vložené CSS štýly */
        :root {
            --bg-color: #1e1e1e;
            --container-bg: #2a2a2a;
            --input-bg: #333;
            --border-color: #444;
            --text-color: #f0f0f0;
            --text-muted: #bbb;
            --primary-color: #3a6ea5;
            --primary-hover: #4a8ec5;
            --secondary-color: #555;
            --secondary-hover: #666;
            --success-color: #66bb6a;
            --success-bg: rgba(102, 187, 106, 0.15);
            --success-border: rgba(102, 187, 106, 0.4);
            --error-color: #ef5350;
            --error-bg: rgba(239, 83, 80, 0.1);
            --error-border: rgba(239, 83, 80, 0.3);
            --info-color: #8ab4f8;
            --debug-bg: #303030;
        }
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; background-color: var(--bg-color); color: var(--text-color); margin: 0; padding: 15px; line-height: 1.6; }
        .container { max-width: 600px; margin: 0 auto; }
        .header { padding: 15px; background-color: var(--input-bg); text-align: center; margin-bottom: 20px; border-radius: 8px; font-size: 1.2em;}
        .request-form { max-width: 500px; margin: 0 auto; padding: 20px; background-color: var(--container-bg); border-radius: 8px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3); margin-bottom: 20px; }
        .external-status-result { max-width: 500px; margin: 20px auto; padding: 0 10px; }
        .error-message { color: var(--error-color); background-color: var(--error-bg); border: 1px solid var(--error-border); padding: 12px 15px; border-radius: 4px; margin-bottom: 15px; text-align: center; }
        .success-message { color: var(--success-color); background-color: var(--success-bg); border: 1px solid var(--success-border); padding: 12px 15px; border-radius: 4px; margin-bottom: 15px; text-align: center; }
        .status-success-display { color: var(--success-color); background-color: var(--success-bg); border: 1px solid var(--success-border); padding: 15px 20px; border-radius: 8px; margin-bottom: 20px; text-align: center; font-size: 1.2em; font-weight: bold; }
        .status-other-display { color: var(--text-color); background-color: var(--container-bg); border: 1px solid var(--border-color); padding: 15px 20px; border-radius: 8px; margin-bottom: 20px; text-align: center; font-size: 1.1em; }
        .status-details { background-color: var(--container-bg); padding: 15px; border-radius: 8px; margin-top: 15px; border: 1px solid var(--border-color); }
        .status-details strong { color: var(--info-color); display: block; margin-bottom: 8px; font-size: 1em; }
        .status-details p { color: var(--text-color); margin: 0; line-height: 1.6; font-size: 0.95em; word-wrap: break-word; }
        .status-details a { color: var(--info-color); text-decoration: underline; }
        .buttons { display: flex; flex-wrap: wrap; justify-content: space-between; margin-top: 25px; gap: 10px;}
        .back-button, .check-button { flex-grow: 1; text-align: center; color: white; border: none; padding: 12px 15px; border-radius: 4px; cursor: pointer; transition: background-color 0.3s; text-decoration: none; display: inline-block; font-size: 1em; font-weight: 500;}
        .back-button { background-color: var(--secondary-color); }
        .check-button { background-color: var(--primary-color); }
        .back-button:hover { background-color: var(--secondary-hover); }
        .check-button:hover { background-color: var(--primary-hover); }
        .file-upload { margin-top: 20px; margin-bottom: 15px; }
        .file-upload > label { display: block; margin-bottom: 8px; color: var(--text-color); font-weight: 500;} /* Hlavný label */
        .file-upload-wrapper { position: relative; margin-bottom: 10px; }
        .file-upload input[type="file"] { width: 100%; padding: 10px; background-color: var(--input-bg); border: 1px dashed var(--border-color); border-radius: 4px; color: var(--text-muted); cursor: pointer; box-sizing: border-box; font-size: 0.95em;}
        .file-info { color: var(--text-muted); font-size: 0.85em; margin-top: 5px; }
        .image-preview { margin-top: 15px; text-align: center; border: 1px solid var(--border-color); background-color: var(--input-bg); padding: 10px; border-radius: 4px;}
        .image-preview img { max-width: 100%; max-height: 200px; display: block; margin: 5px auto 0;}
        .image-preview p { margin-bottom: 5px; color: var(--text-color); font-size: 0.9em; }
        .loading-indicator { display: none; text-align: center; margin: 15px 0; color: var(--info-color); }
        .loading-spinner { display: inline-block; width: 20px; height: 20px; border: 3px solid rgba(138, 180, 248, 0.3); border-radius: 50%; border-top-color: var(--info-color); animation: spin 1s ease-in-out infinite; margin-right: 10px; vertical-align: middle; }
        @keyframes spin { to { transform: rotate(360deg); } }
        .ocr-debug { margin-top: 10px; padding: 10px; background-color: var(--input-bg); border: 1px solid var(--border-color); border-radius: 4px; color: var(--text-color); font-family: monospace; white-space: pre-wrap; word-break: break-all; max-height: 150px; overflow-y: auto; font-size: 0.85em; }
        .ocr-debug h4 { margin: 0 0 8px 0; color: var(--info-color); border-bottom: 1px solid var(--border-color); padding-bottom: 5px; font-size: 1em; font-weight: 500;}
        .ocr-error { color: var(--error-color); }
        .ocr-matches { color: var(--success-color); }
        .ocr-matches span { display: block; margin-bottom: 3px; padding: 2px 4px; border-radius: 3px;}
        .ocr-matches span:hover { background-color: var(--border-color); }
        label { color: var(--text-color); margin-bottom: 5px; display: block; font-weight: 500;}
        input[type="text"] { width: 100%; padding: 10px; margin-bottom: 15px; background-color: var(--input-bg); border: 1px solid var(--border-color); border-radius: 4px; color: var(--text-color); box-sizing: border-box; font-size: 1em;}
        .preprocessing-controls { border: 1px solid var(--border-color); padding: 15px; margin-top: 20px; border-radius: 8px; background-color: var(--debug-bg); }
        .control-group { margin-bottom: 15px; padding-bottom: 15px; border-bottom: 1px solid var(--border-color); }
        .control-group:last-child { margin-bottom: 0; padding-bottom: 0; border-bottom: none; }
        .control-header { display: flex; align-items: center; margin-bottom: 10px; cursor: pointer;} /* Klikateľný celý header */
        .control-header label { margin-bottom: 0; margin-left: 10px; cursor: pointer; flex-grow: 1; user-select: none;} /* Lepšie pre mobil */
        .control-header input[type="checkbox"] { width: 18px; height: 18px; cursor: pointer; flex-shrink: 0; margin: 0;}
        .slider-container { padding-left: 28px; transition: opacity 0.3s; }
        .slider-container label { font-size: 0.9em; color: var(--text-muted); margin-bottom: 3px; }
        .slider-container input[type="range"] { width: 100%; cursor: pointer; margin-top: 5px; height: 8px; appearance: none; background: var(--secondary-color); border-radius: 5px; outline: none;}
        .slider-container input[type="range"]::-webkit-slider-thumb { appearance: none; width: 18px; height: 18px; background: var(--info-color); border-radius: 50%; cursor: pointer; }
        .slider-container input[type="range"]::-moz-range-thumb { width: 18px; height: 18px; background: var(--info-color); border-radius: 50%; cursor: pointer; border: none;}
        .slider-container span { font-weight: bold; color: var(--info-color); margin-left: 5px;}
        .disabled-slider { opacity: 0.5; pointer-events: none; } /* Znefunkční aj klikanie */
    </style>
</head>
<body>
    <div class="container">
        <div class="header">Zistenie stavu žiadosti</div>

         <?php if ($externalStatusResult): ?>
            <div class="external-status-result">
                 <?php
                 // Zobrazíme najprv úspešnú hlášku, ak existuje (napr. o nájdení variácie)
                 if ($successMessage): ?>
                     <div class="success-message"><?= htmlspecialchars($successMessage) ?></div>
                 <?php endif;

                 $statusText = $externalStatusResult['status'] ?? 'Neznámy stav';
                 $exactNotFoundString = "Žiadosť o vydanie dokladu s uvedeným číslom nie je v systéme evidovaná.";
                 $isConsideredSuccess = ($statusText !== $exactNotFoundString); // Úspech je všetko okrem presnej "not found" hlášky
                 $statusClass = $isConsideredSuccess ? 'status-success-display' : 'status-other-display';
                 ?>
                 <div class="<?= $statusClass ?>">
                      <?= htmlspecialchars($statusText) ?>
                 </div>

                 <?php if (!empty($externalStatusResult['details'])): ?>
                     <div class="status-details">
                         <strong>Detaily:</strong>
                         <p><?= nl2br(htmlspecialchars($externalStatusResult['details'])) ?></p>
                     </div>
                 <?php endif; ?>
             </div>
         <?php endif; ?>

        <?php if ($errorMessage && !$successMessage): ?>
            <div class="error-message"><?= htmlspecialchars($errorMessage) ?></div>
        <?php endif; ?>

        <div class="request-form">
             <form method="post" action="<?= htmlspecialchars($_SERVER['PHP_SELF']) ?><?= !empty($returnUrl) ? '?return=' . urlencode($returnUrl) : '' ?>" enctype="multipart/form-data" id="main-form">
                <label for="request_number">Číslo žiadosti:</label>
                <input type="text" id="request_number" name="request_number" required
                       value="<?= htmlspecialchars($originalRequestNumberForDisplay) ?>"
                       placeholder="Napr. DS-JPPO1-123456-ABC">

                <div class="file-upload">
                    <label for="request_image">Nahrať obrázok žiadosti (voliteľné):</label>
                    <div class="file-upload-wrapper">
                        <input type="file" id="request_image" name="request_image" accept="image/jpeg,image/png,image/gif">
                    </div>
                    <div class="file-info">Povolené: JPG, PNG, GIF. Max: 5MB</div>

                    <div class="preprocessing-controls">
                        <div class="control-group">
                            <div class="control-header" onclick="document.getElementById('enableThreshold').click();"> <input type="checkbox" id="enableThreshold" name="enableThreshold" checked>
                                <label for="enableThreshold">Thresholding (Binarizácia)</label>
                            </div>
                            <div class="slider-container" id="thresholdSliderContainer">
                                <label for="thresholdSlider">Prahová hodnota: <span id="thresholdValueDisplay">128</span></label>
                                <input type="range" id="thresholdSlider" name="thresholdSlider" min="0" max="255" value="128">
                            </div>
                        </div>
                        <div class="control-group">
                            <div class="control-header" onclick="document.getElementById('enableGradient').click();"> <input type="checkbox" id="enableGradient" name="enableGradient">
                                <label for="enableGradient">Gradient Map (Sobel - Experimentálne)</label>
                            </div>
                            <div class="slider-container" id="gradientSliderContainer">
                                <label for="gradientThresholdSlider">Prah gradientu: <span id="gradientThresholdValueDisplay">50</span></label>
                                <input type="range" id="gradientThresholdSlider" name="gradientThresholdSlider" min="0" max="255" value="50">
                            </div>
                        </div>
                    </div>

                    <div class="loading-indicator" id="ocr-loading">
                        <div class="loading-spinner"></div>
                        <span>Spracúvam obrázok...</span>
                    </div>

                    <div class="image-preview" id="js-image-preview" style="display: none;"><p>Náhľad:</p><img id="js-preview-img" src="#" alt="Náhľad obrázka"></div>
                    <?php if ($uploadedImagePath && !(isset($_FILES['request_image']) && $_FILES['request_image']['error'] === UPLOAD_ERR_OK && !$errorMessage)): ?>
                    <div class="image-preview" id="uploaded-image-preview" >
                        <p>Naposledy nahraný obrázok:</p>
                        <img src="<?= htmlspecialchars($uploadedImagePath) ?>" alt="Nahraný obrázok">
                    </div>
                    <?php endif; ?>

                    <div id="ocr-debug-container" style="display: none;">
                        <div id="ocr-recognized-text" class="ocr-debug"><h4>Rozpoznaný text:</h4><div id="ocr-text-content"></div></div>
                        <div id="ocr-matches-container" class="ocr-debug"><h4>Nájdené čísla (kliknite pre vloženie):</h4><div id="ocr-matches-content"></div></div>
                        <div id="ocr-error-container" class="ocr-debug" style="display: none;"><h4>Chyba OCR:</h4><div id="ocr-error-content" class="ocr-error"></div></div>
                    </div>
                </div>

                <div class="buttons">
                    <a href="<?= htmlspecialchars($returnUrl) ?>" class="back-button">Späť</a>
                    <button type="submit" name="check_status" class="check-button">Zistiť Stav</button>
                </div>
            </form>
        </div>

        <div style="text-align: center; margin-top: 20px; font-size: 0.9em; color: var(--text-muted);">
            Prihlásený ako: <?php echo htmlspecialchars($loggedUser['name'] ?? 'Neznámy'); ?>
        </div>
    </div>

    <script>
        // --- Plný JavaScript Kód ---
        (function() { // IIFE - Immediately Invoked Function Expression
            "use strict"; // Prísny režim

            // Získanie referencií na DOM elementy
            const requestImageInput = document.getElementById('request_image');
            const jsPreviewContainer = document.getElementById('js-image-preview');
            const jsPreviewImage = document.getElementById('js-preview-img');
            const uploadedPreviewContainer = document.getElementById('uploaded-image-preview');
            const enableThresholdCheckbox = document.getElementById('enableThreshold');
            const thresholdSlider = document.getElementById('thresholdSlider');
            const thresholdValueDisplay = document.getElementById('thresholdValueDisplay');
            const thresholdSliderContainer = document.getElementById('thresholdSliderContainer');
            const enableGradientCheckbox = document.getElementById('enableGradient');
            const gradientThresholdSlider = document.getElementById('gradientThresholdSlider');
            const gradientThresholdValueDisplay = document.getElementById('gradientThresholdValueDisplay');
            const gradientSliderContainer = document.getElementById('gradientSliderContainer');
            const loadingIndicator = document.getElementById('ocr-loading');
            const debugContainer = document.getElementById('ocr-debug-container');
            const textContentDiv = document.getElementById('ocr-text-content');
            const matchesContentDiv = document.getElementById('ocr-matches-content');
            const errorContainer = document.getElementById('ocr-error-container');
            const errorContentDiv = document.getElementById('ocr-error-content');
            const requestNumberInput = document.getElementById('request_number');

            let currentFile = null; // Uchováme si referenciu na aktuálne vybraný súbor

            // --- Event Listeners ---

            // Listener pre výber súboru
            requestImageInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                currentFile = file; // Uložíme súbor
                if (file) {
                    if (!validateFile(file)) {
                        this.value = ''; // Reset inputu
                        currentFile = null;
                        jsPreviewContainer.style.display = 'none';
                        return;
                    }
                    displayAndProcessImage(file);
                } else {
                    currentFile = null;
                    jsPreviewContainer.style.display = 'none';
                    if (uploadedPreviewContainer) uploadedPreviewContainer.style.display = 'block';
                }
            });

            // Helper funkcia pre UI aktualizáciu sliderov a checkboxov
            function setupSliderControl(checkbox, slider, display, container) {
                 if (!checkbox || !slider || !display || !container) return; // Bezpečnostná kontrola

                 const updateUI = () => {
                     const isEnabled = checkbox.checked;
                     display.textContent = slider.value;
                     slider.disabled = !isEnabled;
                     container.classList.toggle('disabled-slider', !isEnabled);
                 };

                 slider.addEventListener('input', updateUI);
                 checkbox.addEventListener('change', updateUI);
                 // Pridanie listenera aj na label (cez onclick v HTML)
                 // alebo priamo tu, ak by sme odstránili onclick z HTML:
                 // container.querySelector('.control-header label').addEventListener('click', () => checkbox.click());

                 updateUI(); // Počiatočný stav
            }

            // Nastavenie listenerov pre oba ovládacie prvky
            setupSliderControl(enableThresholdCheckbox, thresholdSlider, thresholdValueDisplay, thresholdSliderContainer);
            setupSliderControl(enableGradientCheckbox, gradientThresholdSlider, gradientThresholdValueDisplay, gradientSliderContainer);


             // --- Pomocné Funkcie ---
            function validateFile(file) {
                 const fileType = file.type;
                 const validImageTypes = ['image/jpeg', 'image/png', 'image/gif'];
                 if (!validImageTypes.includes(fileType)) {
                     alert('Neplatný formát súboru. Povolené formáty sú: JPG, PNG, GIF.');
                     return false;
                 }
                 if (file.size > 5 * 1024 * 1024) { // 5MB limit
                     alert('Súbor je príliš veľký. Maximálna veľkosť je 5MB.');
                     return false;
                 }
                 return true;
             }

            function displayAndProcessImage(file) {
                  const reader = new FileReader();
                  reader.onload = function(event) {
                       if (uploadedPreviewContainer) uploadedPreviewContainer.style.display = 'none';
                       jsPreviewImage.src = event.target.result;
                       jsPreviewContainer.style.display = 'block';
                       resetOcrDebug();
                       recognizeTextFromImage(event.target.result);
                  };
                  reader.onerror = function() {
                      console.error("FileReader error.");
                      alert("Chyba pri načítavaní súboru.");
                  }
                  reader.readAsDataURL(file);
             }

            function resetOcrDebug() {
                 debugContainer.style.display = 'none';
                 textContentDiv.textContent = '';
                 matchesContentDiv.innerHTML = '';
                 errorContainer.style.display = 'none';
                 errorContentDiv.textContent = '';
             }

            // --- Funkcie pre predspracovanie obrazu ---
            function preprocessImage(imageDataUrl, enableThreshold, thresholdValue, enableGradient, gradientThresholdValue) {
                 return new Promise((resolve, reject) => {
                     const img = new Image();
                     img.onload = () => {
                         const canvas = document.createElement('canvas');
                         const width = img.naturalWidth; // Použijeme naturalWidth/Height
                         const height = img.naturalHeight;
                         // Obmedzenie veľkosti pre výkon (voliteľné)
                         const MAX_DIM = 1500;
                         let scale = 1;
                         if (width > MAX_DIM || height > MAX_DIM) {
                             scale = MAX_DIM / Math.max(width, height);
                             canvas.width = Math.round(width * scale);
                             canvas.height = Math.round(height * scale);
                             console.log(`Resizing image from ${width}x${height} to ${canvas.width}x${canvas.height}`);
                         } else {
                             canvas.width = width;
                             canvas.height = height;
                         }

                         const ctx = canvas.getContext('2d', { willReadFrequently: true });
                         if (!ctx) return reject(new Error('Canvas 2D context is not supported.'));

                         ctx.drawImage(img, 0, 0, canvas.width, canvas.height); // Vykreslíme (prípadne zmenšený) obrázok
                         let imageDataObj;
                         try {
                            imageDataObj = ctx.getImageData(0, 0, canvas.width, canvas.height);
                         } catch (e) {
                            console.error("Error getting ImageData:", e);
                            return reject(new Error('Could not get image data. Check browser compatibility or image source.'));
                         }
                         let data = imageDataObj.data;
                         const currentWidth = canvas.width; // Použijeme aktuálnu šírku/výšku canvasu
                         const currentHeight = canvas.height;

                         // 1. Konverzia na stupne šedi
                         let grayData = new Uint8ClampedArray(currentWidth * currentHeight);
                         for (let i = 0, j = 0; i < data.length; i += 4, j++) {
                             grayData[j] = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
                         }

                         // 2. Aplikácia efektov
                         let finalPixelDataUsed = false;
                         if (enableGradient) {
                             console.log(`Applying Gradient Map with threshold: ${gradientThresholdValue}`);
                             const sobelData = applySobelFilter(grayData, currentWidth, currentHeight);
                             for (let i = 0, j = 0; i < data.length; i += 4, j++) {
                                 const newValue = sobelData[j] > gradientThresholdValue ? 255 : 0;
                                 data[i] = data[i + 1] = data[i + 2] = newValue;
                                 data[i + 3] = 255; // Alpha
                             }
                              finalPixelDataUsed = true;
                         } else if (enableThreshold) {
                             console.log(`Applying standard Thresholding with value: ${thresholdValue}`);
                             for (let i = 0, j = 0; i < data.length; i += 4, j++) {
                                 const newValue = grayData[j] > thresholdValue ? 255 : 0;
                                 data[i] = data[i + 1] = data[i + 2] = newValue;
                                 data[i + 3] = 255; // Alpha
                             }
                              finalPixelDataUsed = true;
                         }

                         if (!finalPixelDataUsed) {
                              console.log("Applying Grayscale only (no effects enabled)");
                              for (let i = 0, j = 0; i < data.length; i += 4, j++) {
                                   data[i] = data[i + 1] = data[i + 2] = grayData[j];
                                   data[i + 3] = 255; // Alpha
                              }
                         }

                         // 3. Vloženie upravených dát späť
                         ctx.putImageData(imageDataObj, 0, 0);
                         resolve(canvas.toDataURL('image/png')); // Export ako PNG
                     };
                     img.onerror = (err) => reject(new Error('Image could not be loaded for preprocessing.'));
                     img.src = imageDataUrl;
                 });
            }

            function applySobelFilter(grayData, width, height) {
                 const kernelX = [[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]];
                 const kernelY = [[-1, -2, -1], [0, 0, 0], [1, 2, 1]];
                 const magnitudeData = new Float32Array(width * height);
                 let maxMagnitude = 0;

                 for (let y = 1; y < height - 1; y++) {
                     for (let x = 1; x < width - 1; x++) {
                         let pixelX = 0;
                         let pixelY = 0;
                         const centerIndex = y * width + x;
                         for (let ky = -1; ky <= 1; ky++) {
                             for (let kx = -1; kx <= 1; kx++) {
                                 pixelX += grayData[centerIndex + ky * width + kx] * kernelX[ky + 1][kx + 1];
                                 pixelY += grayData[centerIndex + ky * width + kx] * kernelY[ky + 1][kx + 1];
                             }
                         }
                         const magnitude = Math.sqrt(pixelX * pixelX + pixelY * pixelY);
                         magnitudeData[centerIndex] = magnitude;
                         if (magnitude > maxMagnitude) maxMagnitude = magnitude;
                     }
                 }

                 const normalizedMagnitudeData = new Uint8ClampedArray(width * height); // Vytvoríme pole správnej veľkosti
                 if (maxMagnitude > 0) {
                    const factor = 255.0 / maxMagnitude;
                    // Prejdeme len vnútorné pixely, kde sme počítali magnitúdu
                    for (let y = 1; y < height - 1; y++) {
                        for (let x = 1; x < width - 1; x++) {
                           const index = y * width + x;
                           normalizedMagnitudeData[index] = Math.round(magnitudeData[index] * factor);
                        }
                    }
                    // Okraje zostanú 0 (implicitne pri vytvorení Uint8ClampedArray)
                 }
                 return normalizedMagnitudeData;
            }


            // --- Hlavná funkcia pre OCR ---
            async function recognizeTextFromImage(imageData) {
                 loadingIndicator.style.display = 'block';
                 resetOcrDebug();
                 debugContainer.style.display = 'block';

                 // Získanie aktuálnych nastavení predspracovania
                 const useThreshold = enableThresholdCheckbox.checked;
                 const thresholdVal = parseInt(thresholdSlider.value, 10);
                 const useGradient = enableGradientCheckbox.checked;
                 const gradientThresholdVal = parseInt(gradientThresholdSlider.value, 10);

                 try {
                     // 1. Predspracovanie
                     const processedImageDataUrl = await preprocessImage(imageData, useThreshold, thresholdVal, useGradient, gradientThresholdVal);
                     jsPreviewImage.src = processedImageDataUrl; // Aktualizujeme náhľad

                     // 2. OCR volanie
                     console.log("Starting Tesseract recognition...");
                     const { data: { text } } = await Tesseract.recognize(
                         processedImageDataUrl,
                         'slk+eng', // Jazyky
                         {
                             // logger: m => console.log(m.status, Math.round(m.progress * 100) + '%') // Detailnejší logger
                         }
                     );
                     console.log("Tesseract finished.");
                     textContentDiv.textContent = text || '(žiadny text nerozpoznaný)';

                     // 3. Hľadanie a korekcia čísla žiadosti
                     // Vzor: Upravený pre väčšiu flexibilitu oddeľovačov a možnú zámenu O/0
                     const separatorPattern = '\\s*[-–—‐‑\\s]+\\s*'; // Aspoň jedna pomlčka alebo medzera, obklopená medzerami
                     const pattern = new RegExp(
                         `([A-Z]{2})${separatorPattern}([A-Z0-9]{4,5})${separatorPattern}([0-9O]{6})${separatorPattern}([A-Z0-9]{3})`,
                         'igm' // insensitive, global, multiline
                     );

                     const allMatchesIterator = text.matchAll(pattern);
                     let correctedMatches = [];

                     for (const match of allMatchesIterator) {
                          if (match.length >= 5) {
                              let part1 = match[1].toUpperCase();
                              let part2 = match[2].toUpperCase().replace(/0/g, 'O'); // 0->O v 2. časti (JPPO1)
                              let part3 = match[3].toUpperCase().replace(/O/g, '0'); // O->0 v 3. časti (číslice)
                              let part4 = match[4].toUpperCase();
                              let corrected = `${part1}-${part2}-${part3}-${part4}`;
                              // Odstránenie prípadných nechcených znakov chytených na začiatku/konci
                              corrected = corrected.replace(/[^A-Z0-9-]/g, '');
                              // Finálna validácia formátu pred pridaním
                              if (/^[A-Z]{2}-[A-Z]{1}[A-Z0-9]{3,4}-[0-9]{6}-[A-Z0-9]{3}$/.test(corrected)) {
                                  correctedMatches.push(corrected);
                              } else {
                                   console.warn("Skipped potential match due to invalid final format:", corrected, "Original match:", match[0]);
                              }
                          }
                     }
                     // Odstránenie duplicít
                     correctedMatches = [...new Set(correctedMatches)];

                     // Zobrazenie výsledkov
                     if (correctedMatches.length > 0) {
                         matchesContentDiv.innerHTML = correctedMatches.map(cm =>
                              `<span class="ocr-matches" style="cursor:pointer; text-decoration: underline;" onclick="setRequestNumber('${cm.replace(/'/g, "\\'")}')" title="Vložiť ${cm}">${cm}</span>`
                          ).join('');
                         // Automaticky vyplníme prvé nájdené, len ak pole ešte nie je vyplnené používateľom
                         if(!requestNumberInput.value) {
                            requestNumberInput.value = correctedMatches[0];
                         }
                     } else {
                         matchesContentDiv.innerHTML = '<span style="color: #aaa;">Neboli nájdené žiadne čísla v očakávanom formáte.</span>';
                     }

                 } catch (err) {
                     console.error("Chyba pri OCR alebo predspracovaní:", err);
                     let errorMsg = `Chyba: ${err.message || err}`;
                     if (err.message && err.message.includes('NetworkError')) {
                         errorMsg += "\nSkontrolujte pripojenie k internetu alebo či nie je blokovaný prístup k Tesseract.js dátam.";
                     }
                     errorContentDiv.textContent = errorMsg;
                     errorContainer.style.display = 'block';
                 } finally {
                     loadingIndicator.style.display = 'none';
                 }
             }

            // Pomocná funkcia na vloženie čísla do inputu (globálna)
            window.setRequestNumber = function(reqNum) {
                 if (requestNumberInput) {
                    requestNumberInput.value = reqNum;
                 }
            }

        })(); // Koniec IIFE
    </script>
</body>
</html>