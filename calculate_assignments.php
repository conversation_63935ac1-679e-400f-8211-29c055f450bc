<?php
// calculate_assignments.php
require_once 'config.php';

/**
 * Získa posledné priradenie pracovníka (pred zadaným dátumom).
 * Poznámka: Táto funkcia zostáva nezmenená.
 */
function getLastAssignment($user_id, $date, $pdo) {
    $stmt = $pdo->prepare("
        SELECT assignment_date as assign_date, machine_number as machine
        FROM assignments
        WHERE user_id = ?
        AND assignment_date < ?
        AND assigned_role = 'strojnik'
        AND machine_number IS NOT NULL AND machine_number > 0 -- Iba reálne stroje pre rotáciu
        ORDER BY assignment_date DESC
        LIMIT 1
    ");
    $stmt->execute([$user_id, $date]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * Vypočíta a uloží priradenia pre daný deň podľa Novej logiky (vrátane stroja 0).
 */
function calculateAssignments(DateTimeInterface $date, PDO $pdo): array {
    $dateStr = $date->format('Y-m-d');
    error_log("--- Starting Assignment Calculation for Date: " . $dateStr . " ---");
    $numberOfMachines = 3; // Počet reálnych strojov

    // 1. Získaj VŠETKÝCH potenciálnych strojníkov zoradených podľa priority
    $stmtOps = $pdo->prepare("SELECT * FROM users WHERE strojnik_order IS NOT NULL AND is_active = 1 ORDER BY strojnik_order");
    $stmtOps->execute();
    $allOperators = $stmtOps->fetchAll(PDO::FETCH_ASSOC);
    if (!$allOperators) {
        error_log("No active operators found with strojnik_order. No assignments calculated.");
        return array_fill(1, $numberOfMachines, null); // Vráti prázdne pole priradení
    }
    $operatorCount = count($allOperators);
    error_log("Total potential operators found: " . $operatorCount);
    // error_log("Potential operators list: " . print_r(array_column($allOperators, 'name', 'id'), true));


    // 2. Získaj ID všetkých strojníkov, ktorí majú schválenú absenciu na daný deň
    $absentOperatorIds = [];
    try {
        // Zahrnieme len ID strojníkov z $allOperators pre optimalizáciu
        $allOperatorIds = array_column($allOperators, 'id');
        if(!empty($allOperatorIds)) {
            $placeholders = implode(',', array_fill(0, count($allOperatorIds), '?'));
            $sqlAbsence = "
                SELECT user_id FROM absences
                WHERE user_id IN ($placeholders)
                AND ? BETWEEN start_date AND end_date
                AND status = 'approved'
            ";
            // Pripravíme parametre: ID operátorov a dátum na konci
            $params = array_merge($allOperatorIds, [$dateStr]);

            $stmtAbsence = $pdo->prepare($sqlAbsence);
            $stmtAbsence->execute($params);
            $absences = $stmtAbsence->fetchAll(PDO::FETCH_COLUMN);
            $absentOperatorIds = array_flip($absences); // Použijeme kľúče pre rýchle vyhľadávanie (user_id => 1)
            error_log("Absent operator IDs for {$dateStr}: " . (!empty($absentOperatorIds) ? implode(', ', array_keys($absentOperatorIds)) : 'None'));
        } else {
             error_log("No operator IDs to check for absences.");
        }
    } catch (PDOException $e) {
        error_log("Error fetching absences: " . $e->getMessage());
        // Rozhodnúť sa: pokračovať bez info o absenciách alebo zastaviť? Zastavíme.
        throw new Exception("Failed to fetch absence data for $dateStr", 0, $e);
    }


    // 3. Rozdeľ operátorov na prítomných a neprítomných (zachovaj poradie)
    $presentOperators = [];
    $absentOperatorsMap = []; // Mapa neprítomných pre neskoršie použitie (id => data)
    foreach ($allOperators as $op) {
        if (!isset($absentOperatorIds[$op['id']])) {
            $presentOperators[] = $op;
        } else {
            $absentOperatorsMap[$op['id']] = $op;
        }
    }
    $presentCount = count($presentOperators);
    error_log("Present operators count: " . $presentCount);
    // error_log("Present operators list: " . print_r(array_column($presentOperators, 'name', 'id'), true));


    // 4. Priraď Stroje 1-3 pomocou IBA PRÍTOMNÝCH operátorov
    $assignments = array_fill(1, $numberOfMachines, null); // [1 => null, 2 => null, 3 => null]
    $assignedOperatorIds = []; // Sleduj ID už priradených (k strojom 1-3)
    $availablePresentOperators = $presentOperators; // Pracovná kópia zoznamu prítomných

    // --- Začiatok: Logika priradenia strojov 1-3 ---

    // 4a. Prvý pokus: Priradenie podľa preferencie rotácie (posledný stroj + 1)
    $tempAvailable = $availablePresentOperators; // Pracujeme s kópiou, aby sme mohli bezpečne odstraňovať
    $indicesToRemove = []; // Zbierame indexy na odstránenie z $availablePresentOperators

    foreach ($tempAvailable as $index => $user) {
        $user_id = $user['id'];
        $last = getLastAssignment($user_id, $dateStr, $pdo);

        if ($last && $last['machine'] !== null && $last['machine'] > 0) {
            $nextMachine = ($last['machine'] % $numberOfMachines) + 1;
            // Ak preferovaný stroj je voľný A tento používateľ ešte nebol priradený
            if ($assignments[$nextMachine] === null && !isset($assignedOperatorIds[$user_id])) {
                $assignments[$nextMachine] = $user;
                $assignedOperatorIds[$user_id] = true; // Označ ako priradeného
                $indicesToRemove[] = $index; // Označ index na odstránenie z hlavného zoznamu
                error_log("Rotation assignment: User {$user_id} ({$user['name']}) assigned to Machine {$nextMachine}");
            }
        }
    }

    // Odstránime priradených pracovníkov z $availablePresentOperators
    // Ideme odzadu, aby sme nenarušili indexy
    rsort($indicesToRemove);
    foreach($indicesToRemove as $index) {
        unset($availablePresentOperators[$index]);
    }
     // Preindexujeme pole po odstránení, aby malo súvislé indexy 0, 1, 2...
    $availablePresentOperators = array_values($availablePresentOperators);

    // 4b. Druhý pokus: Doplnenie zvyšných strojov zvyšnými dostupnými prítomnými operátormi (podľa poradia)
    for ($machine = 1; $machine <= $numberOfMachines; $machine++) {
        if ($assignments[$machine] === null && !empty($availablePresentOperators)) {
            // Vezmeme prvého dostupného prítomného pracovníka z preindexovaného zoznamu
            $user = array_shift($availablePresentOperators);
            if ($user && !isset($assignedOperatorIds[$user['id']])) { // Ešte raz overíme, či nebol priradený
                $assignments[$machine] = $user;
                $assignedOperatorIds[$user['id']] = true;
                error_log("Fill assignment: User {$user['id']} ({$user['name']}) assigned to Machine {$machine}");
            } elseif ($user) {
                 // Ak bol už priradený (nemalo by nastať pri správnej logike vyššie), vrátime ho späť na začiatok
                 array_unshift($availablePresentOperators, $user);
            }
        }
    }
    // --- Koniec: Logika priradenia strojov 1-3 ---


    // 5. Identifikuj Kandidátov pre Stroj 0
    $machineZeroAssignments = []; // Pole pracovníkov pre stroj 0
    // Vezmi prvých N ($numberOfMachines) operátorov z PÔVODNÉHO zoradeného zoznamu všetkých
    $topPotentialCandidates = array_slice($allOperators, 0, $numberOfMachines);

    error_log("Checking top " . $numberOfMachines . " potential candidates for Machine 0 eligibility: " . print_r(array_column($topPotentialCandidates, 'name', 'id'), true));

    foreach ($topPotentialCandidates as $potentialOp) {
        $userId = $potentialOp['id'];
        // Podmienka: Musí byť NEPRÍTOMNÝ (v $absentOperatorsMap)
        // A zároveň NESMIE byť už priradený k stroju 1-3 (čo by nemal byť, ak je neprítomný)
        if (isset($absentOperatorsMap[$userId]) && !isset($assignedOperatorIds[$userId])) {
            $machineZeroAssignments[] = $potentialOp; // Pridaj pracovníka do zoznamu pre stroj 0
            error_log("Eligible for Machine 0: User {$userId} ({$potentialOp['name']}) - Was in top {$numberOfMachines} and is absent.");
        } else {
             // error_log("Not eligible for Machine 0: User {$userId} - Present or not in top {$numberOfMachines}.");
        }
    }


    // 6. Ulož Všetky Priradenia (Stroje 1-3 a Stroj 0) v Transakcii
    $pdo->beginTransaction();
    try {
        // 6a. Zmaž existujúce automatické priradenia pre VŠETKÝCH strojníkov na tento deň
        if (!empty($allOperatorIds)) {
            // Mazanie len pre strojníkov, ktorých sme spracovávali
            $placeholdersDelete = implode(',', array_fill(0, count($allOperatorIds), '?'));
            // Mažeme len rolu 'strojnik', aby sme neovplyvnili iné roly
            $stmtDelete = $pdo->prepare("
                DELETE FROM assignments
                WHERE assignment_date = ?
                AND user_id IN ($placeholdersDelete)
                AND assigned_role = 'strojnik'
            ");
            $paramsDelete = array_merge([$dateStr], $allOperatorIds);
            $deleteCount = $stmtDelete->execute($paramsDelete) ? $stmtDelete->rowCount() : 0;
            error_log("Cleared {$deleteCount} previous 'strojnik' assignments for {$operatorCount} operators on {$dateStr}");
        } else {
             error_log("No operators to clear assignments for.");
        }


        // 6b. Ulož priradenia pre stroje 1-3 (len ak bol niekto priradený)
        $stmtInsertReal = $pdo->prepare("
            INSERT INTO assignments (user_id, assignment_date, assigned_role, machine_number)
            VALUES (?, ?, 'strojnik', ?)
        ");
        foreach ($assignments as $machine => $user) {
            if ($user) {
                 $stmtInsertReal->execute([$user['id'], $dateStr, $machine]);
                 error_log("Saved assignment: User {$user['id']} ({$user['name']}) -> Machine {$machine} on {$dateStr}");
            }
        }

        // 6c. Ulož priradenia pre stroj 0 (len ak boli nájdení kandidáti)
        if (!empty($machineZeroAssignments)) {
            $stmtInsertZero = $pdo->prepare("
                INSERT INTO assignments (user_id, assignment_date, assigned_role, machine_number)
                VALUES (?, ?, 'strojnik', 0)
            ");
            foreach ($machineZeroAssignments as $user) {
                 $stmtInsertZero->execute([$user['id'], $dateStr]);
                 error_log("Saved assignment: User {$user['id']} ({$user['name']}) -> Machine 0 on {$dateStr}");
            }
        } else {
             error_log("No assignments to save for Machine 0 on {$dateStr}");
        }

        $pdo->commit();
        error_log("--- Assignment Calculation Finished Successfully for Date: " . $dateStr . " ---");

    } catch (PDOException $e) {
        $pdo->rollBack();
        error_log("!!! Database Error during assignment saving for date {$dateStr}: " . $e->getMessage() . " !!!");
        // Vrátime pôvodnú výnimku, aby volajúca funkcia vedela o chybe
        throw new Exception("Failed to save assignments for $dateStr due to DB error.", 0, $e);
    } catch (Exception $ex) {
         // Zachytíme aj iné výnimky (napr. z fetchovania absencií)
         if ($pdo->inTransaction()) {
             $pdo->rollBack();
         }
         error_log("!!! General Error during assignment calculation for date {$dateStr}: " . $ex->getMessage() . " !!!");
         throw $ex; // Vrátime pôvodnú výnimku
    }

    // Vraciame pole s priradeniami pre stroje 1-3 (tak ako predtým)
    // Priradenia pre stroj 0 sú uložené v DB, ale nie sú súčasťou tohto návratového poľa.
    return $assignments;
}

// --- Stará funkcia getAvailableWorkers už nie je priamo potrebná pre calculateAssignments ---
// Ak ju nepoužívate inde, môžete ju odstrániť alebo zakomentovať.
/*
function getAvailableWorkers($date, $pdo) {
    // Získame všetkých aktívnych používateľov
    $stmt = $pdo->prepare("SELECT * FROM users WHERE is_active = 1");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $available = [];
    $dateStr = ($date instanceof DateTimeInterface) ? $date->format('Y-m-d') : $date; // Ensure string format

    // Získame všetky absencie pre daný deň naraz
    $stmtAbs = $pdo->prepare("SELECT user_id FROM absences WHERE ? BETWEEN start_date AND end_date AND status = 'approved'");
    $stmtAbs->execute([$dateStr]);
    $absentIds = $stmtAbs->fetchAll(PDO::FETCH_COLUMN);
    $absentUserMap = array_flip($absentIds); // Mapa pre rýchle overenie

    foreach($users as $user) {
        // Ak user ID nie je v mape neprítomných, je dostupný
        if (!isset($absentUserMap[$user['id']])) {
            $available[] = $user;
        }
    }
    return $available;
}
*/

?>