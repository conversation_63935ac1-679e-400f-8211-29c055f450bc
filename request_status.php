
<?php
// Potlačenie varovaní o zastaraných funkciách
error_reporting(E_ALL & ~E_DEPRECATED & ~E_NOTICE);
require_once 'config.php';
require_once 'functions.php';

// Overenie prihlásenia
requireLogin();

// Získanie PDO spojenia
$pdo = getDbConnection();

// Načítanie údajov prihláseného používateľa
$loggedInUserId = $_SESSION['user_id'] ?? 0;
$loggedUser = getUserById($loggedInUserId, $pdo);

// Inicializácia premenných pre výsledok
$externalStatusResult = null; // Premenná pre výsledok z minv.sk
$errorMessage = null;
$successMessage = null;
$uploadedImagePath = null;

// Spracovanie formulára
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Spracovanie nahrávania obrázku
    if (isset($_FILES['request_image']) && $_FILES['request_image']['error'] === UPLOAD_ERR_OK) {
        $uploadDir = 'uploads/';
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }
        $fileName = $_FILES['request_image']['name'];
        $fileType = $_FILES['request_image']['type'];
        $fileTmpName = $_FILES['request_image']['tmp_name'];
        // ... (zvyšok validácie a presunu súboru - bez zmeny) ...
         $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
         if (!in_array($fileType, $allowedTypes)) {
             $errorMessage = "Neplatný formát súboru. Povolené formáty sú: JPG, PNG, GIF.";
         } elseif ($_FILES['request_image']['size'] > 5 * 1024 * 1024) {
             $errorMessage = "Súbor je príliš veľký. Maximálna veľkosť je 5MB.";
         } else {
             $newFileName = uniqid() . '_' . $fileName;
             $destination = $uploadDir . $newFileName;
             if (move_uploaded_file($fileTmpName, $destination)) {
                 $uploadedImagePath = $destination;
             } else {
                 $errorMessage = "Nastala chyba pri nahrávaní súboru.";
             }
         }
    }

    // Spracovanie kontroly stavu žiadosti
    if (isset($_POST['check_status'])) {
        $requestNumber = isset($_POST['request_number']) ? htmlspecialchars(trim($_POST['request_number']), ENT_QUOTES, 'UTF-8') : '';

        if (empty($requestNumber)) {
            $errorMessage = "Prosím, zadajte číslo žiadosti.";
        } else {
            try {
                $externalStatusResult = getExternalRequestStatus($requestNumber);
            } catch (Exception $e) {
                $errorMessage = "Nastala chyba pri zisťovaní stavu žiadosti na minv.sk: " . $e->getMessage();
            }
        }
    }
}

// Funkcia na získanie stavu žiadosti z externého systému (bez zmeny)
function getExternalRequestStatus($requestNumber) {
    $url = 'https://www.minv.sk/?zistenie-stavu-spracovania-ziadosti';
    $ch = curl_init();
    
    // Nastavenia cURL
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, "cislo=" . urlencode($requestNumber));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    
    // Dočasne vypneme overovanie SSL pre testovanie
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    
    $response = curl_exec($ch);
    
    if (curl_errno($ch)) {
        $error_msg = curl_error($ch);
        curl_close($ch);
        throw new Exception('Chyba pri komunikácii so serverom minv.sk (cURL Error): ' . $error_msg);
    }
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($http_code !== 200) {
         throw new Exception("Server minv.sk vrátil neočakávaný stavový kód: {$http_code}");
    }
    if (empty($response)) {
         throw new Exception("Server minv.sk vrátil prázdnu odpoveď.");
    }

    // Extrakcia výsledku z odpovede (bez zmeny)
    $result = [];
    if (preg_match('/<h4[^>]*>(.*?)<\/h4>/is', $response, $matches)) {
        $result['status'] = trim(strip_tags($matches[1]));
    } else {
         if (strpos($response, 'nebola nájdená') !== false) {
              $result['status'] = "Žiadosť s daným číslom nebola nájdená.";
         } elseif (strpos($response, 'nesprávny formát') !== false) {
              $result['status'] = "Zadané číslo žiadosti má nesprávny formát.";
         } else {
            $result['status'] = "Nepodarilo sa zistiť stav žiadosti (neznáma odpoveď zo servera).";
         }
    }
    if (preg_match('/<div class="text"[^>]*>(.*?)<\/div>/is', $response, $matches)) {
        $result['details'] = trim(strip_tags($matches[1]));
    }
    return $result;
}

// Určenie návratovej URL (bez zmeny)
$returnUrl = $_GET['return'] ?? 'dashboard.php';
if (!preg_match('/^(dashboard\.php|monthly_overview\.php|absences\.php|weekly_absences\.php)/', $returnUrl) && !filter_var($returnUrl, FILTER_VALIDATE_URL)) {
    $returnUrl = 'dashboard.php';
}
?>

<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Zistenie stavu žiadosti</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdn.jsdelivr.net/npm/tesseract.js@4/dist/tesseract.min.js"></script>
    <style>
        /* CSS štýly zostávajú nezmenené, pridáme len pre nové prvky */
        body { font-family: sans-serif; background-color: #1e1e1e; color: #f0f0f0; margin: 0; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .header { padding: 15px; background-color: #333; color: #f0f0f0; text-align: center; margin-bottom: 20px; border-radius: 8px; }
        .request-form { max-width: 500px; margin: 0 auto; padding: 20px; background-color: #2a2a2a; border-radius: 8px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3); margin-bottom: 20px; }
        .external-status-result { max-width: 500px; margin: 20px auto; }
        .request-result { max-width: 500px; margin: 0 auto; padding: 20px; background-color: #2a2a2a; border-radius: 8px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3); margin-bottom: 20px; }
        .error-message { color: #ff6b6b; background-color: rgba(255, 107, 107, 0.1); border: 1px solid rgba(255, 107, 107, 0.3); padding: 10px 15px; border-radius: 4px; margin-bottom: 15px; text-align: center; }
        .success-message { color: #4CAF50; background-color: rgba(76, 175, 80, 0.1); border: 1px solid rgba(76, 175, 80, 0.3); padding: 10px 15px; border-radius: 4px; margin-bottom: 15px; text-align: center; }
        .status-success-display { color: #4CAF50; background-color: rgba(76, 175, 80, 0.15); border: 1px solid rgba(76, 175, 80, 0.4); padding: 15px 20px; border-radius: 8px; margin-bottom: 20px; text-align: center; font-size: 1.3em; font-weight: bold; }
        .status-other-display { color: #f0f0f0; background-color: #3a3a3a; border: 1px solid #555; padding: 15px 20px; border-radius: 8px; margin-bottom: 20px; text-align: center; font-size: 1.2em; }
        .status-details { background-color: #2a2a2a; padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #444; }
        .status-details strong { color: #8ab4f8; display: block; margin-bottom: 8px; }
        .status-details p { color: #f0f0f0; margin: 0; line-height: 1.6; }
        .buttons { display: flex; justify-content: space-between; margin-top: 20px; }
        .back-button { background-color: #555; color: #fff; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer; transition: background-color 0.3s; text-decoration: none; display: inline-block; }
        .check-button { background-color: #3a6ea5; color: #fff; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer; transition: background-color 0.3s; }
        .file-upload { margin-bottom: 15px; }
        .file-upload label { display: block; margin-bottom: 5px; color: #f0f0f0; }
        .file-upload-wrapper { position: relative; margin-bottom: 10px; }
        .file-upload input[type="file"] { width: 100%; padding: 10px; background-color: #333; border: 1px dashed #555; border-radius: 4px; color: #e0e0e0; cursor: pointer; box-sizing: border-box; }
        .file-info { color: #bbb; font-size: 0.9em; margin-top: 5px; }
        .image-preview {
            position: relative;
            margin-top: 15px;
            text-align: center;
        }
        .image-preview img {
            max-width: 100%;
            max-height: 200px;
            border: 1px solid #444;
            border-radius: 4px;
            background-color: #333;
        }
        .image-preview p {
            margin-bottom: 5px;
            color: #f0f0f0;
        }
        .loading-indicator { display: none; text-align: center; margin-top: 10px; color: #8ab4f8; }
        .loading-spinner { display: inline-block; width: 20px; height: 20px; border: 3px solid rgba(138, 180, 248, 0.3); border-radius: 50%; border-top-color: #8ab4f8; animation: spin 1s ease-in-out infinite; margin-right: 10px; vertical-align: middle; }
        @keyframes spin { to { transform: rotate(360deg); } }
        .ocr-debug { margin-top: 15px; padding: 10px; background-color: #333; border: 1px solid #555; border-radius: 4px; color: #f0f0f0; font-family: monospace; white-space: pre-wrap; word-break: break-word; max-height: 200px; overflow-y: auto; }
        .ocr-debug h4 { margin-top: 0; color: #8ab4f8; border-bottom: 1px solid #444; padding-bottom: 5px; }
        .ocr-error { color: #ff6b6b; }
        .ocr-matches { color: #4CAF50; }
        label { color: #f0f0f0; margin-bottom: 5px; display: block; }
        input[type="text"] { width: 100%; padding: 10px; margin-bottom: 15px; background-color: #333; border: 1px solid #555; border-radius: 4px; color: #f0f0f0; box-sizing: border-box; }

        /* --- ŠTÝLY PRE NOVÉ OVLÁDACIE PRVKY --- */
        .preprocessing-controls { border: 1px solid #444; padding: 15px; margin-top: 20px; border-radius: 8px; background-color: #303030; }
        .control-group { margin-bottom: 15px; padding-bottom: 15px; border-bottom: 1px solid #444; }
        .control-group:last-child { margin-bottom: 0; padding-bottom: 0; border-bottom: none; }
        .control-header { display: flex; align-items: center; margin-bottom: 10px; }
        .control-header label { margin-bottom: 0; margin-left: 10px; cursor: pointer; }
        .control-header input[type="checkbox"] { width: 18px; height: 18px; cursor: pointer; }
        .slider-container label { font-size: 0.9em; color: #bbb; margin-bottom: 3px; }
        .slider-container input[type="range"] { width: 100%; cursor: pointer; margin-top: 5px;}
        .slider-container span { font-weight: bold; color: #8ab4f8; margin-left: 5px;}
        .toggle-button {
            display: block;
            margin: 10px auto;
            padding: 8px 15px;
            background-color: #444;
            color: #f0f0f0;
            border: 1px solid #666;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
            transition: background-color 0.2s;
            font-weight: bold;
        }
        .toggle-button:hover {
            background-color: #555;
        }
        /* --------------------------------------- */

    </style>
</head>
<body>
    <div class="container">
        <div class="header">Zistenie stavu žiadosti</div>

         <?php if ($externalStatusResult): ?>
        <div class="external-status-result">
            <?php
            $statusText = $externalStatusResult['status'] ?? 'Neznámy stav';
            $statusClass = (mb_stripos($statusText, 'prijatá') !== false) ? 'status-success-display' : 'status-other-display';
            ?>
            <div class="<?= $statusClass ?>">
                 <?= htmlspecialchars($statusText) ?>
            </div>
            <?php if (!empty($externalStatusResult['details'])): ?>
                <div class="status-details">
                    <strong>Detaily:</strong>
                    <p><?= nl2br(htmlspecialchars($externalStatusResult['details'])) ?></p>
                </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <?php if ($errorMessage): ?> <div class="error-message"><?= htmlspecialchars($errorMessage) ?></div> <?php endif; ?>
        <?php if ($successMessage): ?> <div class="success-message"><?= htmlspecialchars($successMessage) ?></div> <?php endif; ?>

        <div class="request-form">
             <form method="post" action="request_status.php<?= !empty($returnUrl) ? '?return=' . urlencode($returnUrl) : '' ?>" enctype="multipart/form-data">
                <label for="request_number">Zadajte číslo žiadosti:</label>
                <input type="text" id="request_number" name="request_number" required
                       value="<?= htmlspecialchars($_POST['request_number'] ?? '') ?>"
                       placeholder="Napr. DS-JPPO1-123456-ABC">

                <div class="file-upload">
                    <label for="request_image">Nahrajte obrázok (voliteľné, pre OCR):</label>
                    <div class="file-upload-wrapper">
                        <input type="file" id="request_image" name="request_image" accept="image/jpeg, image/png, image/gif">
                    </div>
                    <div class="file-info">Povolené formáty: JPG, PNG, GIF. Max. veľkosť: 5MB</div>

                    <div class="preprocessing-controls">
                        <div class="control-group">
                            <div class="control-header">
                                <input type="checkbox" id="enableThreshold" name="enableThreshold">
                                <label for="enableThreshold">Zapnúť Thresholding (Binarizácia)</label>
                            </div>
                            <div class="slider-container">
                                <label for="thresholdSlider">Prahová hodnota: <span id="thresholdValueDisplay">128</span></label>
                                <input type="range" id="thresholdSlider" name="thresholdSlider" min="0" max="255" value="128">
                            </div>
                        </div>
                        <div class="control-group">
                            <div class="control-header">
                                <input type="checkbox" id="enableGradient" name="enableGradient">
                                <label for="enableGradient">Zapnúť Gradient Map (Experimentálne)</label>
                            </div>
                            <div class="slider-container">
                                <label for="gradientThresholdSlider">Prah gradientu: <span id="gradientThresholdValueDisplay">50</span></label>
                                <input type="range" id="gradientThresholdSlider" name="gradientThresholdSlider" min="0" max="255" value="50">
                            </div>
                        </div>
                    </div>
                    <div class="loading-indicator" id="ocr-loading">
                        <div class="loading-spinner"></div>
                        <span>Rozpoznávanie textu z obrázka...</span>
                    </div>

                    <div id="ocr-debug-container" style="display: none;">
                        <div id="ocr-recognized-text" class="ocr-debug"><h4>Rozpoznaný text:</h4><div id="ocr-text-content"></div></div>
                        <div id="ocr-matches-container" class="ocr-debug"><h4>Nájdené zhody:</h4><div id="ocr-matches-content"></div></div>
                        <div id="ocr-error-container" class="ocr-debug" style="display: none;"><h4>Chyba OCR:</h4><div id="ocr-error-content" class="ocr-error"></div></div>
                    </div>
                     <?php if ($uploadedImagePath): ?>
                    <div class="image-preview" id="uploaded-image-preview"><p>Nahraný obrázok:</p><img src="<?= htmlspecialchars($uploadedImagePath) ?>" alt="Nahraný obrázok"></div>
                    <?php endif; ?>
                     <div class="image-preview" id="js-image-preview" style="display: none;"><p>Náhľad obrázka:</p><img id="js-preview-img" src="#" alt="Náhľad obrázka"></div>
                </div>

                <div class="buttons">
                    <a href="<?= htmlspecialchars($returnUrl) ?>" class="back-button">Späť</a>
                    <button type="submit" name="check_status" class="check-button">Zisti stav žiadosti</button>
                </div>
            </form>
        </div>

        <div style="text-align: center; margin-top: 20px; font-size: 0.9em; color: #777;">
            Prihlásený ako: <?php echo htmlspecialchars($loggedUser['name'] ?? $_SESSION['user_name'] ?? 'Neznámy'); ?>
        </div>
    </div>

    <script>
        // --- JavaScript Kód ---

        // Získanie referencií na DOM elementy
        const requestImageInput = document.getElementById('request_image');
        const jsPreviewContainer = document.getElementById('js-image-preview');
        const jsPreviewImage = document.getElementById('js-preview-img');
        const uploadedPreviewContainer = document.getElementById('uploaded-image-preview');
        // Nové elementy
        const enableThresholdCheckbox = document.getElementById('enableThreshold');
        const thresholdSlider = document.getElementById('thresholdSlider');
        const thresholdValueDisplay = document.getElementById('thresholdValueDisplay');
        const enableGradientCheckbox = document.getElementById('enableGradient');
        const gradientThresholdSlider = document.getElementById('gradientThresholdSlider');
        const gradientThresholdValueDisplay = document.getElementById('gradientThresholdValueDisplay');
        // Ostatné
        const loadingIndicator = document.getElementById('ocr-loading');
        const debugContainer = document.getElementById('ocr-debug-container');
        const textContent = document.getElementById('ocr-text-content');
        const matchesContent = document.getElementById('ocr-matches-content');
        const errorContainer = document.getElementById('ocr-error-container');
        const errorContent = document.getElementById('ocr-error-content');
        const requestNumberInput = document.getElementById('request_number');


        // Listener pre výber súboru
        requestImageInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // Validácia
                const fileType = file.type;
                const validImageTypes = ['image/jpeg', 'image/png', 'image/gif'];
                if (!validImageTypes.includes(fileType)) {
                    alert('Neplatný formát súboru. Povolené formáty sú: JPG, PNG, GIF.');
                    this.value = ''; jsPreviewContainer.style.display = 'none'; return;
                }
                if (file.size > 5 * 1024 * 1024) {
                    alert('Súbor je príliš veľký. Maximálna veľkosť je 5MB.');
                    this.value = ''; jsPreviewContainer.style.display = 'none'; return;
                }

                // Zobrazenie náhľadu a spustenie OCR
                displayAndProcessImage(file);
            } else {
                jsPreviewContainer.style.display = 'none';
                if (uploadedPreviewContainer) uploadedPreviewContainer.style.display = 'block';
            }
        });

        // Listener pre Threshold slider
        if (thresholdSlider && thresholdValueDisplay) {
            thresholdValueDisplay.textContent = thresholdSlider.value;
            thresholdSlider.addEventListener('input', () => {
                thresholdValueDisplay.textContent = thresholdSlider.value;
            });
            // Bonus: Ak sa zmení checkbox, povolíme/zakážeme slider
            enableThresholdCheckbox.addEventListener('change', () => {
                thresholdSlider.disabled = !enableThresholdCheckbox.checked;
            });
             thresholdSlider.disabled = !enableThresholdCheckbox.checked; // Počiatočné nastavenie
        }

        // Listener pre Gradient Threshold slider
        if (gradientThresholdSlider && gradientThresholdValueDisplay) {
             gradientThresholdValueDisplay.textContent = gradientThresholdSlider.value;
             gradientThresholdSlider.addEventListener('input', () => {
                 gradientThresholdValueDisplay.textContent = gradientThresholdSlider.value;
             });
             // Bonus: Ak sa zmení checkbox, povolíme/zakážeme slider
            enableGradientCheckbox.addEventListener('change', () => {
                gradientThresholdSlider.disabled = !enableGradientCheckbox.checked;
            });
             gradientThresholdSlider.disabled = !enableGradientCheckbox.checked; // Počiatočné nastavenie
        }

        // Reset OCR Debug výstupov
        function resetOcrDebug() {
            debugContainer.style.display = 'none';
            textContent.textContent = '';
            matchesContent.textContent = '';
            errorContainer.style.display = 'none';
            errorContent.textContent = '';
        }

        // --- Funkcia na predspracovanie obrázka (Thresholding + Gradient Map) ---
        function preprocessImage(imageDataUrl, enableThreshold, thresholdValue, enableGradient, gradientThresholdValue) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = () => {
                    const canvas = document.createElement('canvas');
                    const width = img.width;
                    const height = img.height;
                    canvas.width = width;
                    canvas.height = height;
                    const ctx = canvas.getContext('2d', { willReadFrequently: true }); // Optimalizácia pre getImageData
                    if (!ctx) return reject(new Error('Canvas context error.'));

                    ctx.drawImage(img, 0, 0);
                    let imageData = ctx.getImageData(0, 0, width, height);
                    let data = imageData.data;

                    // 1. Konverzia na stupne šedi (vždy potrebná pre ďalšie kroky)
                    let grayData = new Uint8ClampedArray(width * height);
                    for (let i = 0, j = 0; i < data.length; i += 4, j++) {
                        const gray = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
                        grayData[j] = gray;
                    }

                    // 2. Aplikácia Gradient Map (Sobel Filter + Threshold) - AK JE ZAPNUTÁ
                    if (enableGradient) {
                        console.log(`Applying Gradient Map with threshold: ${gradientThresholdValue}`);
                        const sobelData = applySobelFilter(grayData, width, height);
                        // Aplikujeme threshold na výsledok Sobel filtra
                        for (let i = 0, j = 0; i < data.length; i += 4, j++) {
                             const newValue = sobelData[j] > gradientThresholdValue ? 255 : 0;
                             data[i] = data[i + 1] = data[i + 2] = newValue;
                             data[i + 3] = 255; // Alpha
                        }
                    }
                    // 3. Aplikácia štandardného Thresholdingu - AK NIE JE Gradient ZAPNUTÝ a Thresholding JE ZAPNUTÝ
                    else if (enableThreshold) {
                        console.log(`Applying standard Thresholding: ${thresholdValue}`);
                        for (let i = 0, j = 0; i < data.length; i += 4, j++) {
                            const newValue = grayData[j] > thresholdValue ? 255 : 0;
                            data[i] = data[i + 1] = data[i + 2] = newValue;
                            data[i + 3] = 255; // Alpha
                        }
                    }
                    // 4. Ak nie je zapnuté nič, vrátime grayscale
                    else {
                        console.log("Applying Grayscale only (no effects enabled)");
                        for (let i = 0, j = 0; i < data.length; i += 4, j++) {
                            data[i] = data[i + 1] = data[i + 2] = grayData[j];
                            data[i + 3] = 255;
                        }
                    }

                    // Vloženie upravených dát späť na canvas
                    ctx.putImageData(imageData, 0, 0);
                    resolve(canvas.toDataURL('image/png')); // Export ako PNG
                };
                img.onerror = (err) => reject(new Error('Image loading failed for preprocessing.'));
                img.src = imageDataUrl;
            });
        }

        // --- Pomocná funkcia pre Sobel Filter (Výpočet magnitúdy gradientu) ---
        function applySobelFilter(grayData, width, height) {
            const kernelX = [
                [-1, 0, 1],
                [-2, 0, 2],
                [-1, 0, 1]
            ];
            const kernelY = [
                [-1, -2, -1],
                [ 0,  0,  0],
                [ 1,  2,  1]
            ];
            const magnitudeData = new Uint8ClampedArray(width * height);
            let maxMagnitude = 0; // Na normalizáciu

            for (let y = 1; y < height - 1; y++) {
                for (let x = 1; x < width - 1; x++) {
                    let pixelX = 0;
                    let pixelY = 0;
                    const centerIndex = y * width + x;

                    for (let ky = -1; ky <= 1; ky++) {
                        for (let kx = -1; kx <= 1; kx++) {
                             const currentPixelIndex = centerIndex + ky * width + kx;
                             const grayValue = grayData[currentPixelIndex];
                             pixelX += grayValue * kernelX[ky + 1][kx + 1];
                             pixelY += grayValue * kernelY[ky + 1][kx + 1];
                        }
                    }

                    const magnitude = Math.sqrt(pixelX * pixelX + pixelY * pixelY);
                    magnitudeData[centerIndex] = magnitude; // Uložíme zatiaľ nenormalizované
                    if(magnitude > maxMagnitude) {
                        maxMagnitude = magnitude;
                    }
                }
            }

            // Normalizácia magnitúdy na rozsah 0-255
            if (maxMagnitude > 0) {
                 const factor = 255.0 / maxMagnitude;
                 for(let i = 0; i < magnitudeData.length; i++) {
                      magnitudeData[i] = Math.round(magnitudeData[i] * factor);
                 }
            }

            return magnitudeData;
        }


        // --- Funkcia na rozpoznanie textu (číta hodnoty z checkboxov a sliderov) ---
        async function recognizeTextFromImage(imageData) {
            loadingIndicator.style.display = 'block';
            resetOcrDebug(); // Presunutý reset sem
            debugContainer.style.display = 'block';

            // Načítanie stavu ovládacích prvkov
            const useThreshold = enableThresholdCheckbox.checked;
            const thresholdVal = parseInt(thresholdSlider.value, 10);
            const useGradient = enableGradientCheckbox.checked;
            const gradientThresholdVal = parseInt(gradientThresholdSlider.value, 10);

            try {
                // Krok 1: Predspracovanie s aktuálnymi nastaveniami
                const processedImageDataUrl = await preprocessImage(imageData, useThreshold, thresholdVal, useGradient, gradientThresholdVal);

                // Krok 2: OCR
                const { data: { text } } = await Tesseract.recognize(
                    processedImageDataUrl,
                    'slk+eng',
                    { /* logger: m => console.log(m) */ }
                );

                textContent.textContent = text || '(žiadny text nerozpoznaný)';

                // Krok 3: Hľadanie a korekcia čísla žiadosti
                const separatorPattern = '\\s*(?:[-–—‐‑]|\\s+)\\s*';
                const pattern = new RegExp(
                    `([A-Z]{2})${separatorPattern}([A-Z0-9]{4,5})${separatorPattern}([0-9O]{6})${separatorPattern}([A-Z0-9]{3})`,
                    'ig'
                );
                let match;
                let correctedMatches = [];
                while ((match = pattern.exec(text)) !== null) {
                    let part1 = match[1].toUpperCase();
                    let part2 = match[2].toUpperCase().replace(/0/g, 'O');
                    let part3 = match[3].toUpperCase().replace(/O/g, '0');
                    let part4 = match[4].toUpperCase();
                    let corrected = `${part1}-${part2}-${part3}-${part4}`;
                    correctedMatches.push(corrected);
                }
                if (correctedMatches.length > 0) {
                    matchesContent.innerHTML =
                        '<span class="ocr-matches">Nájdené potenciálne čísla (kliknite pre vloženie):</span><br>' +
                        correctedMatches.map(cm => `<span class="ocr-matches" style="cursor:pointer; text-decoration: underline;" onclick="setRequestNumber('${cm}')">${cm}</span>`).join('<br>');
                    requestNumberInput.value = correctedMatches[0]; // Auto-vyplnenie
                } else {
                    matchesContent.innerHTML = 'Neboli nájdené žiadne zhody pre formát čísla žiadosti.';
                }

            } catch (err) {
                console.error("Chyba pri OCR alebo predspracovaní:", err);
                errorContent.textContent = `Chyba: ${err.message || err}`;
                errorContainer.style.display = 'block';
            } finally {
                loadingIndicator.style.display = 'none';
            }
        }

        // Pomocná funkcia na vloženie čísla do inputu
        function setRequestNumber(reqNum) {
             requestNumberInput.value = reqNum;
        }

        // --- Vylepšená funkcia na detekciu a orezanie oblasti s číslom žiadosti ---
        function detectAndCropRequestNumber(imageDataUrl) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = () => {
                    const originalWidth = img.width;
                    const originalHeight = img.height;
                    
                    // 1. Vytvoríme canvas pre analýzu
                    const canvas = document.createElement('canvas');
                    canvas.width = originalWidth;
                    canvas.height = originalHeight;
                    const ctx = canvas.getContext('2d', { willReadFrequently: true });
                    if (!ctx) return reject(new Error('Canvas context error.'));
                    
                    ctx.drawImage(img, 0, 0);
                    const imageData = ctx.getImageData(0, 0, originalWidth, originalHeight);
                    const data = imageData.data;
                    
                    // 2. Konverzia na stupne šedi pre analýzu
                    const grayData = new Uint8ClampedArray(originalWidth * originalHeight);
                    for (let i = 0, j = 0; i < data.length; i += 4, j++) {
                        grayData[j] = 0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2];
                    }
                    
                    // 3. Detekcia oblastí s textom pomocou analýzy kontrastu
                    // Inicializácia hraníc
                    let minX = originalWidth;
                    let minY = originalHeight;
                    let maxX = 0;
                    let maxY = 0;
                    let hasText = false;
                    
                    // Znížime prah pre detekciu textu, aby bol algoritmus citlivejší
                    const CONTRAST_THRESHOLD = 15; // Znížené z 30 na 15
                    const BLOCK_SIZE = 3;          // Znížené z 5 na 3 pre jemnejšiu detekciu
                    
                    // Prehľadávame obraz po blokoch
                    for (let y = 0; y < originalHeight - BLOCK_SIZE; y += BLOCK_SIZE) {
                        for (let x = 0; x < originalWidth - BLOCK_SIZE; x += BLOCK_SIZE) {
                            // Analýza kontrastu v bloku
                            let min = 255, max = 0;
                            for (let by = 0; by < BLOCK_SIZE; by++) {
                                for (let bx = 0; bx < BLOCK_SIZE; bx++) {
                                    const idx = (y + by) * originalWidth + (x + bx);
                                    const val = grayData[idx];
                                    min = Math.min(min, val);
                                    max = Math.max(max, val);
                                }
                            }
                            
                            // Ak je kontrast dostatočný, považujeme to za text
                            if (max - min > CONTRAST_THRESHOLD) {
                                minX = Math.min(minX, x);
                                minY = Math.min(minY, y);
                                maxX = Math.max(maxX, x + BLOCK_SIZE);
                                maxY = Math.max(maxY, y + BLOCK_SIZE);
                                hasText = true;
                            }
                        }
                    }
                    
                    // 4. Ak sme našli text, orezať obrázok
                    if (hasText) {
                        // Pridáme okraj okolo detekovanej oblasti
                        const PADDING = 30; // Zväčšené z 20 na 30
                        minX = Math.max(0, minX - PADDING);
                        minY = Math.max(0, minY - PADDING);
                        maxX = Math.min(originalWidth, maxX + PADDING);
                        maxY = Math.min(originalHeight, maxY + PADDING);
                        
                        // Vypočítame rozmery výsledného obrázka
                        const croppedWidth = maxX - minX;
                        const croppedHeight = maxY - minY;
                        
                        // Kontrola, či orezaná oblasť nie je príliš malá
                        if (croppedWidth < 50 || croppedHeight < 20) {
                            console.log("Detekovaná oblasť je príliš malá, používam celý obrázok");
                            resolve({
                                croppedImageUrl: imageDataUrl,
                                originalImageUrl: imageDataUrl,
                                bounds: null
                            });
                            return;
                        }
                        
                        // Vytvoríme nový canvas pre orezaný obrázok
                        const croppedCanvas = document.createElement('canvas');
                        croppedCanvas.width = croppedWidth;
                        croppedCanvas.height = croppedHeight;
                        const croppedCtx = croppedCanvas.getContext('2d');
                        
                        // Vykreslíme orezanú časť
                        croppedCtx.drawImage(
                            img,
                            minX, minY, croppedWidth, croppedHeight,
                            0, 0, croppedWidth, croppedHeight
                        );
                        
                        // Vrátime orezaný obrázok
                        resolve({
                            croppedImageUrl: croppedCanvas.toDataURL('image/png'),
                            originalImageUrl: imageDataUrl,
                            bounds: { minX, minY, maxX, maxY, width: croppedWidth, height: croppedHeight }
                        });
                    } else {
                        // Ak sme nenašli text, skúsime alternatívny prístup - orezať na stred obrázka
                        console.log("Nepodarilo sa detekovať text, orezávam na stred obrázka");
                        
                        // Vypočítame stred obrázka a vytvoríme oblasť okolo neho
                        const centerX = Math.floor(originalWidth / 2);
                        const centerY = Math.floor(originalHeight / 2);
                        const cropWidth = Math.min(originalWidth, 400);
                        const cropHeight = Math.min(originalHeight, 200);
                        
                        const minX = Math.max(0, centerX - cropWidth / 2);
                        const minY = Math.max(0, centerY - cropHeight / 2);
                        const maxX = Math.min(originalWidth, minX + cropWidth);
                        const maxY = Math.min(originalHeight, minY + cropHeight);
                        
                        // Vytvoríme nový canvas pre orezaný obrázok
                        const croppedCanvas = document.createElement('canvas');
                        croppedCanvas.width = maxX - minX;
                        croppedCanvas.height = maxY - minY;
                        const croppedCtx = croppedCanvas.getContext('2d');
                        
                        // Vykreslíme orezanú časť
                        croppedCtx.drawImage(
                            img,
                            minX, minY, maxX - minX, maxY - minY,
                            0, 0, maxX - minX, maxY - minY
                        );
                        
                        // Vrátime orezaný obrázok
                        resolve({
                            croppedImageUrl: croppedCanvas.toDataURL('image/png'),
                            originalImageUrl: imageDataUrl,
                            bounds: { minX, minY, maxX, maxY, width: maxX - minX, height: maxY - minY }
                        });
                    }
                };
                img.onerror = (err) => reject(new Error('Image loading failed for cropping.'));
                img.src = imageDataUrl;
            });
        }

        // --- Funkcia na detekciu a orezanie oblasti s číslom žiadosti pomocou OCR ---
        async function detectAndCropRequestNumberWithOCR(imageDataUrl) {
            try {
                // Kontrola, či je imageDataUrl platný
                if (!imageDataUrl || typeof imageDataUrl !== 'string' || !imageDataUrl.startsWith('data:')) {
                    console.error("Neplatný formát imageDataUrl:", typeof imageDataUrl);
                    throw new Error('Neplatný formát obrázka');
                }

                // 1. Najprv vykonáme OCR na celom obrázku
                console.log("Spúšťam OCR pre detekciu pozície čísla žiadosti...");
                const { data } = await Tesseract.recognize(
                    imageDataUrl,
                    'slk+eng',
                    {
                        logger: m => console.log(m.status, Math.round(m.progress * 100) + '%')
                    }
                );
                
                // 2. Hľadáme vzor čísla žiadosti v texte
                const separatorPattern = '\\s*(?:[-–—‐‑]|\\s+)\\s*';
                const pattern = new RegExp(
                    `([A-Z]{2})${separatorPattern}([A-Z0-9]{4,5})${separatorPattern}([0-9O]{6})${separatorPattern}([A-Z0-9]{3})`,
                    'ig'
                );
                
                // 3. Ak sme našli zhodu, získame jej pozíciu
                if (data.text && data.words && data.words.length > 0) {
                    const matches = [...data.text.matchAll(pattern)];
                    
                    if (matches && matches.length > 0) {
                        // Našli sme aspoň jednu zhodu
                        const firstMatch = matches[0][0]; // Text prvej zhody
                        console.log("Našiel som číslo žiadosti:", firstMatch);
                        
                        // Hľadáme slová, ktoré tvoria túto zhodu
                        let minX = Infinity, minY = Infinity, maxX = 0, maxY = 0;
                        let foundWords = [];
                        
                        // Rozdelíme zhodu na časti (môžu byť oddelené rôznymi oddeľovačmi)
                        const parts = firstMatch.split(/[-–—‐‑\s]+/);
                        
                        // Hľadáme každú časť v zozname slov
                        for (const part of parts) {
                            if (part.trim().length === 0) continue;
                            
                            // Hľadáme slová, ktoré obsahujú túto časť
                            for (const word of data.words) {
                                if (word.text.includes(part) || part.includes(word.text)) {
                                    foundWords.push(word);
                                    minX = Math.min(minX, word.bbox.x0);
                                    minY = Math.min(minY, word.bbox.y0);
                                    maxX = Math.max(maxX, word.bbox.x1);
                                    maxY = Math.max(maxY, word.bbox.y1);
                                }
                            }
                        }
                        
                        // Ak sme našli aspoň jedno slovo
                        if (foundWords.length > 0) {
                            console.log(`Našiel som ${foundWords.length} slov tvoriacich číslo žiadosti`);
                            
                            // Pridáme okraj okolo detekovanej oblasti
                            const PADDING = 40;
                            minX = Math.max(0, minX - PADDING);
                            minY = Math.max(0, minY - PADDING);
                            maxX = Math.min(data.width, maxX + PADDING);
                            maxY = Math.min(data.height, maxY + PADDING);
                            
                            // Orezanie obrázka
                            return await cropImage(
                                imageDataUrl, 
                                { minX, minY, maxX, maxY, width: maxX - minX, height: maxY - minY }
                            );
                        }
                    }
                }
                
                // Ak sme nenašli zhodu, použijeme záložnú metódu
                console.log("Nenašiel som číslo žiadosti pomocou OCR, použijem záložnú metódu");
                return {
                    croppedImageUrl: imageDataUrl,
                    originalImageUrl: imageDataUrl,
                    bounds: null
                };
                
            } catch (err) {
                console.error("Chyba pri OCR detekcii:", err);
                // V prípade chyby vrátime pôvodný obrázok
                return {
                    croppedImageUrl: imageDataUrl,
                    originalImageUrl: imageDataUrl,
                    bounds: null
                };
            }
        }

        // Pomocná funkcia na orezanie obrázka podľa zadaných súradníc
        function cropImage(imageDataUrl, bounds) {
            return new Promise((resolve, reject) => {
                try {
                    const img = new Image();
                    
                    img.onload = () => {
                        try {
                            const { minX, minY, width, height } = bounds;
                            
                            // Vytvoríme canvas pre orezaný obrázok
                            const canvas = document.createElement('canvas');
                            canvas.width = width;
                            canvas.height = height;
                            const ctx = canvas.getContext('2d');
                            
                            if (!ctx) {
                                throw new Error('Nepodarilo sa získať 2D kontext canvasu');
                            }
                            
                            // Vykreslíme orezanú časť
                            ctx.drawImage(img, minX, minY, width, height, 0, 0, width, height);
                            
                            // Vrátime výsledok
                            resolve({
                                croppedImageUrl: canvas.toDataURL('image/png'),
                                originalImageUrl: imageDataUrl,
                                bounds: bounds
                            });
                        } catch (err) {
                            console.error("Chyba pri orezávaní:", err);
                            // V prípade chyby vrátime pôvodný obrázok
                            resolve({
                                croppedImageUrl: imageDataUrl,
                                originalImageUrl: imageDataUrl,
                                bounds: null
                            });
                        }
                    };
                    
                    img.onerror = (err) => {
                        console.error("Chyba pri načítaní obrázka:", err);
                        // V prípade chyby vrátime pôvodný obrázok
                        resolve({
                            croppedImageUrl: imageDataUrl,
                            originalImageUrl: imageDataUrl,
                            bounds: null
                        });
                    };
                    
                    // Nastavíme crossOrigin pre prípad, že obrázok je z inej domény
                    img.crossOrigin = "Anonymous";
                    img.src = imageDataUrl;
                    
                    // Pridáme timeout pre prípad, že načítanie trvá príliš dlho
                    setTimeout(() => {
                        if (!img.complete) {
                            console.error("Timeout pri načítaní obrázka");
                            resolve({
                                croppedImageUrl: imageDataUrl,
                                originalImageUrl: imageDataUrl,
                                bounds: null
                            });
                        }
                    }, 5000); // 5 sekúnd timeout
                } catch (err) {
                    console.error("Neočakávaná chyba pri cropImage:", err);
                    // V prípade chyby vrátime pôvodný obrázok
                    resolve({
                        croppedImageUrl: imageDataUrl,
                        originalImageUrl: imageDataUrl,
                        bounds: null
                    });
                }
            });
        }

        // Upravíme funkciu na zobrazenie a spracovanie obrázka - úplne nový prístup
        function displayAndProcessImage(file) {
            const reader = new FileReader();
            
            reader.onload = async function(event) {
                try {
                    if (uploadedPreviewContainer) uploadedPreviewContainer.style.display = 'none';
                    
                    // Zobrazíme indikátor načítavania
                    loadingIndicator.style.display = 'block';
                    
                    // Vytvoríme jeden obrázok, ktorý budeme používať pre všetky operácie
                    const img = new Image();
                    img.onload = async function() {
                        try {
                            // 1. Zobrazíme pôvodný obrázok
                            jsPreviewImage.src = event.target.result;
                            jsPreviewContainer.style.display = 'block';
                            
                            // 2. Vykonáme OCR na celom obrázku
                            resetOcrDebug();
                            const { data } = await Tesseract.recognize(
                                event.target.result,
                                'slk+eng',
                                { /* logger: m => console.log(m.status, Math.round(m.progress * 100) + '%') */ }
                            );
                            
                            // 3. Hľadáme vzor čísla žiadosti v texte
                            const separatorPattern = '\\s*(?:[-–—‐‑]|\\s+)\\s*';
                            const pattern = new RegExp(
                                `([A-Z]{2})${separatorPattern}([A-Z0-9]{4,5})${separatorPattern}([0-9O]{6})${separatorPattern}([A-Z0-9]{3})`,
                                'ig'
                            );
                            
                            let bounds = null;
                            let croppedImageUrl = null;
                            
                            // 4. Ak sme našli zhodu, získame jej pozíciu a orezeme obrázok
                            if (data.text && data.words && data.words.length > 0) {
                                const matches = [...data.text.matchAll(pattern)];
                                
                                if (matches && matches.length > 0) {
                                    // Našli sme aspoň jednu zhodu
                                    const firstMatch = matches[0][0]; // Text prvej zhody
                                    console.log("Našiel som číslo žiadosti:", firstMatch);
                                    
                                    // Hľadáme slová, ktoré tvoria túto zhodu
                                    let minX = Infinity, minY = Infinity, maxX = 0, maxY = 0;
                                    let foundWords = [];
                                    
                                    // Rozdelíme zhodu na časti (môžu byť oddelené rôznymi oddeľovačmi)
                                    const parts = firstMatch.split(/[-–—‐‑\s]+/);
                                    
                                    // Hľadáme každú časť v zozname slov
                                    for (const part of parts) {
                                        if (part.trim().length === 0) continue;
                                        
                                        // Hľadáme slová, ktoré obsahujú túto časť
                                        for (const word of data.words) {
                                            if (word.text.includes(part) || part.includes(word.text)) {
                                                foundWords.push(word);
                                                minX = Math.min(minX, word.bbox.x0);
                                                minY = Math.min(minY, word.bbox.y0);
                                                maxX = Math.max(maxX, word.bbox.x1);
                                                maxY = Math.max(maxY, word.bbox.y1);
                                            }
                                        }
                                    }
                                    
                                    // Ak sme našli aspoň jedno slovo
                                    if (foundWords.length > 0) {
                                        console.log(`Našiel som ${foundWords.length} slov tvoriacich číslo žiadosti`);
                                        
                                        // Pridáme okraj okolo detekovanej oblasti
                                        const PADDING = 40;
                                        minX = Math.max(0, minX - PADDING);
                                        minY = Math.max(0, minY - PADDING);
                                        maxX = Math.min(img.width, maxX + PADDING);
                                        maxY = Math.min(img.height, maxY + PADDING);
                                        
                                        // Vytvoríme canvas pre orezaný obrázok
                                        const canvas = document.createElement('canvas');
                                        const width = maxX - minX;
                                        const height = maxY - minY;
                                        canvas.width = width;
                                        canvas.height = height;
                                        const ctx = canvas.getContext('2d');
                                        
                                        // Vykreslíme orezanú časť
                                        ctx.drawImage(img, minX, minY, width, height, 0, 0, width, height);
                                        
                                        // Uložíme výsledok
                                        croppedImageUrl = canvas.toDataURL('image/png');
                                        bounds = { minX, minY, maxX, maxY, width, height };
                                        
                                        // Aktualizujeme zobrazený obrázok na orezaný
                                        jsPreviewImage.src = croppedImageUrl;
                                    }
                                }
                            }
                            
                            // 5. Pridáme tlačidlo na prepínanie, ak máme orezaný obrázok
                            if (bounds && croppedImageUrl) {
                                let toggleButton = document.getElementById('toggle-crop-button');
                                if (!toggleButton) {
                                    toggleButton = document.createElement('button');
                                    toggleButton.id = 'toggle-crop-button';
                                    toggleButton.type = 'button';
                                    toggleButton.className = 'toggle-button';
                                    toggleButton.textContent = 'Zobraziť celý obrázok';
                                    toggleButton.dataset.mode = 'cropped';
                                    toggleButton.dataset.originalUrl = event.target.result;
                                    toggleButton.dataset.croppedUrl = croppedImageUrl;
                                    
                                    // Vložíme tlačidlo pred náhľad
                                    jsPreviewContainer.insertBefore(toggleButton, jsPreviewContainer.firstChild);
                                    
                                    // Pridáme listener na prepínanie
                                    toggleButton.addEventListener('click', function() {
                                        if (this.dataset.mode === 'cropped') {
                                            jsPreviewImage.src = this.dataset.originalUrl;
                                            this.textContent = 'Zobraziť orezaný obrázok';
                                            this.dataset.mode = 'original';
                                        } else {
                                            jsPreviewImage.src = this.dataset.croppedUrl;
                                            this.textContent = 'Zobraziť celý obrázok';
                                            this.dataset.mode = 'cropped';
                                        }
                                    });
                                } else {
                                    // Aktualizujeme existujúci prepínač
                                    toggleButton.dataset.originalUrl = event.target.result;
                                    toggleButton.dataset.croppedUrl = croppedImageUrl;
                                    toggleButton.dataset.mode = 'cropped';
                                    toggleButton.textContent = 'Zobraziť celý obrázok';
                                }
                                toggleButton.style.display = 'block';
                            } else {
                                // Ak sa nepodarilo orezať, skryjeme prepínač
                                const toggleButton = document.getElementById('toggle-crop-button');
                                if (toggleButton) toggleButton.style.display = 'none';
                            }
                            
                            // 6. Spustíme OCR na aktuálne zobrazenom obrázku (orezanom alebo pôvodnom)
                            await recognizeTextFromImage(jsPreviewImage.src);
                            
                        } catch (err) {
                            console.error("Chyba pri spracovaní obrázka:", err);
                            // V prípade chyby už máme zobrazený pôvodný obrázok
                            
                            // Skryjeme prepínač
                            const toggleButton = document.getElementById('toggle-crop-button');
                            if (toggleButton) toggleButton.style.display = 'none';
                            
                            // Resetujeme OCR debug a spustíme OCR na pôvodnom obrázku
                            resetOcrDebug();
                            await recognizeTextFromImage(event.target.result);
                        } finally {
                            loadingIndicator.style.display = 'none';
                        }
                    };
                    
                    img.onerror = async function(err) {
                        console.error("Chyba pri načítaní obrázka:", err);
                        // V prípade chyby zobrazíme pôvodný obrázok
                        jsPreviewImage.src = event.target.result;
                        jsPreviewContainer.style.display = 'block';
                        
                        // Skryjeme prepínač
                        const toggleButton = document.getElementById('toggle-crop-button');
                        if (toggleButton) toggleButton.style.display = 'none';
                        
                        // Resetujeme OCR debug a spustíme OCR na pôvodnom obrázku
                        resetOcrDebug();
                        await recognizeTextFromImage(event.target.result);
                        loadingIndicator.style.display = 'none';
                    };
                    
                    // Nastavíme crossOrigin pre prípad, že obrázok je z inej domény
                    img.crossOrigin = "Anonymous";
                    img.src = event.target.result;
                    
                } catch (err) {
                    console.error("Neočakávaná chyba:", err);
                    alert("Nastala chyba pri spracovaní obrázka: " + (err.message || "Neznáma chyba"));
                    loadingIndicator.style.display = 'none';
                }
            };
            
            reader.onerror = function(err) {
                console.error("FileReader error:", err);
                alert("Chyba pri načítavaní súboru: " + (err.message || "Neznáma chyba"));
                loadingIndicator.style.display = 'none';
            };
            
            try {
                reader.readAsDataURL(file);
            } catch (err) {
                console.error("Chyba pri čítaní súboru:", err);
                alert("Chyba pri čítaní súboru: " + (err.message || "Neznáma chyba"));
                loadingIndicator.style.display = 'none';
            }
        }

    </script>
</body>
</html>
