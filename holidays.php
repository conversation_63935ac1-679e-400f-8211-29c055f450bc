<?php
// holidays.php
// <PERSON><PERSON><PERSON><PERSON>, ktorý načíta a vloží verejné sviatky pre aktuálny a nasledujúci rok pre Slovensko, a následne ich vypíše.

require_once 'config.php'; // Načíta pripojenie $pdo a nastavenia

/**
 * Načíta zoznam verejných sviatkov pre daný rok a krajinu pomocou Nager.Date API.
 *
 * @param int $year Rok, pre ktorý chceme sviatky.
 * @param string $countryCode ISO kód krajiny, napr. 'SK' pre Slovensko.
 * @return array Pole sviatkov alebo prázdne pole pri chybe.
 */
function getHolidaysFromApi($year, $countryCode = 'SK') {
    $url = "https://date.nager.at/api/v3/PublicHolidays/{$year}/{$countryCode}";
    // Nastavenie context streamu pre prípadné problémy s SSL/TLS
    $context = stream_context_create([
        'ssl' => [
            'verify_peer' => true,
            'verify_peer_name' => true,
            'allow_self_signed' => false,
             // Môžete skúsiť pridať cestu k certifikátu, ak je problém
             // 'cafile' => '/path/to/your/cacert.pem',
        ],
        'http' => [
             'timeout' => 10, // Timeout v sekundách
        ]
    ]);

    $json = @file_get_contents($url, false, $context);
    if ($json === false) {
        $error = error_get_last();
        echo "Chyba pri volaní API Nager.Date: " . ($error['message'] ?? 'Neznáma chyba') . "<br>\n";
        return [];
    }
    $holidays = json_decode($json, true);
    // Kontrola, či decode prebehol úspešne a vrátil pole
    if (json_last_error() !== JSON_ERROR_NONE || !is_array($holidays)) {
         echo "Chyba pri dekódovaní JSON odpovede z API: " . json_last_error_msg() . "<br>\n";
         return [];
    }
    return $holidays;
}

/**
 * Vloží sviatky do tabuľky holidays pre daný rok, ak ešte nie sú vložené.
 * Používa globálne $pdo.
 *
 * @param int $year Rok, pre ktorý sa majú sviatky vložiť.
 */
function insertHolidaysForYear($year) {
    global $pdo; // Použijeme globálne PDO spojenie

    // Skontrolujeme, či pre daný rok už boli sviatky vložené
    try {
        $stmtCheck = $pdo->prepare("SELECT COUNT(*) FROM holidays WHERE year = ?");
        $stmtCheck->execute([$year]);
        $count = $stmtCheck->fetchColumn();
    } catch (PDOException $e) {
        echo "Chyba pri kontrole existujúcich sviatkov pre rok $year: " . $e->getMessage() . "<br>\n";
        return;
    }

    if ($count > 0) {
        echo "Sviatky pre rok $year už sú vložené do databázy.<br>\n";
        return;
    }

    // Načítanie sviatkov z API
    echo "Načítavam sviatky pre rok $year z Nager.Date API...<br>\n";
    $holidays = getHolidaysFromApi($year, 'SK');
    if (empty($holidays)) {
        echo "Nepodarilo sa načítať sviatky pre rok $year z API alebo API vrátilo prázdny zoznam.<br>\n";
        return;
    }

    // Pripravíme INSERT príkaz - používame IGNORE na preskočenie duplicít podľa UNIQUE kľúča `date`
    $insertStmt = $pdo->prepare("
        INSERT IGNORE INTO holidays (year, `date`, localName, name, countryCode)
        VALUES (?, ?, ?, ?, ?)
    "); // `date` je v `` kvôli možnému konfliktu s kľúčovým slovom

    $insertCount = 0;
    $pdo->beginTransaction(); // Spustenie transakcie pre rýchlejší hromadný insert

    try {
        foreach ($holidays as $holiday) {
            // Overíme, že máme požadované údaje
            if (isset($holiday['date'], $holiday['localName'], $holiday['name'], $holiday['countryCode'])) {
                // Skontrolujeme platnosť dátumu
                $d = DateTime::createFromFormat('Y-m-d', $holiday['date']);
                if ($d && $d->format('Y-m-d') === $holiday['date']) {
                     $insertStmt->execute([
                        $year,
                        $holiday['date'],
                        $holiday['localName'],
                        $holiday['name'],
                        $holiday['countryCode']
                    ]);
                    //rowCount() môže byť nespoľahlivé s IGNORE, radšej počítame manuálne
                    if ($insertStmt->rowCount() > 0) {
                         $insertCount++;
                    }
                } else {
                    echo "Neplatný formát dátumu '{$holiday['date']}' pre sviatok '{$holiday['name']}'. Preskakujem.<br>\n";
                }
            } else {
                 echo "Neúplné dáta pre sviatok (chýba dátum, názov alebo kód). Preskakujem.<br>\n";
            }
        }
        $pdo->commit(); // Potvrdenie transakcie
        echo "Úspešne vložených (alebo ignorovaných) sviatkov pre rok $year: $insertCount nových záznamov.<br>\n";
    } catch (Exception $e) {
        $pdo->rollBack(); // Vrátenie zmien v prípade chyby
        echo "Chyba pri vkladaní sviatkov pre rok $year: " . $e->getMessage() . "<br>\n";
    }
}

// --- Hlavná logika skriptu ---

echo "<h1>Aktualizácia Sviatkov</h1>\n";

// Získame aktuálny rok a nasledujúci rok
$currentYear = date('Y');
$nextYear = $currentYear + 1;

// Vložíme sviatky pre aktuálny a nasledujúci rok
insertHolidaysForYear($currentYear);
echo "<hr>\n";
insertHolidaysForYear($nextYear);
echo "<hr>\n";


// Načítame a vypíšeme sviatky pre oba roky z databázy
try {
    $stmt = $pdo->prepare("SELECT * FROM holidays WHERE year IN (?, ?) ORDER BY `date` ASC");
    $stmt->execute([$currentYear, $nextYear]);
    $allHolidays = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    echo "Chyba pri načítaní sviatkov z DB: " . $e->getMessage() . "<br>\n";
    $allHolidays = [];
}

?>
<!DOCTYPE html>
<html lang="sk">
<head>
   <meta charset="UTF-8">
   <meta name="viewport" content="width=device-width, initial-scale=1">
   <title>Verejné sviatky</title>
   <link rel="stylesheet" href="style.css">
   <style>
       /* Štýly pre tabuľku sviatkov - použijú sa globálne z style.css */
   </style>
</head>
<body>
   <div class="container">
     <h2>Zoznam Sviatkov v Databáze (<?php echo $currentYear . " a " . $nextYear; ?>)</h2>
     <?php if (empty($allHolidays)): ?>
         <p>V databáze neboli nájdené žiadne sviatky pre zvolené roky.</p>
     <?php else: ?>
     <div class="table-responsive">
         <table>
           <thead>
             <tr>
               <th>ID</th>
               <th>Rok</th>
               <th>Dátum</th>
               <th>Miestny názov</th>
               <th>Anglický názov</th>
               <th>Kód krajiny</th>
             </tr>
           </thead>
           <tbody>
             <?php foreach ($allHolidays as $holiday): ?>
             <tr>
               <td><?php echo htmlspecialchars($holiday['id']); ?></td>
               <td><?php echo htmlspecialchars($holiday['year']); ?></td>
               <td><?php echo htmlspecialchars(date("d.m.Y", strtotime($holiday['date']))); ?></td>
               <td><?php echo htmlspecialchars($holiday['localName']); ?></td>
               <td><?php echo htmlspecialchars($holiday['name']); ?></td>
               <td><?php echo htmlspecialchars($holiday['countryCode']); ?></td>
             </tr>
             <?php endforeach; ?>
           </tbody>
         </table>
     </div>
     <?php endif; ?>
     <div class="buttons" style="border-top: none; margin-top: 10px;">
         <button onclick="location.href='dashboard.php'" class="back-button">Späť na Dashboard</button>
     </div>
   </div>
</body>
</html>