<?php
require_once 'config.php';
require_once 'functions.php';

// Kontrola prihlásenia
session_start();
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo "Nie ste prihlásený";
    exit;
}

// Kontrola oprávnení - len vedúci alebo schifflieder môžu prepočítať priradenia
$pdo = getDbConnection();
$loggedUser = getUserById($_SESSION['user_id'], $pdo);
if (!in_array($loggedUser['role1'] ?? '', ['veduci', 'schifflieder'])) {
    http_response_code(403);
    echo "Nemáte oprávnenie na túto akciu";
    exit;
}

// Získanie dátumu a ID oddelenia z POST parametrov
$date_str = $_POST['date'] ?? date('Y-m-d');
$oddelenie_id = $_POST['oddelenie_id'] ?? null; // Očakávame oddelenie_id z POST

// Validácia oddelenie_id
if ($oddelenie_id === null || !is_numeric($oddelenie_id)) {
    http_response_code(400);
    echo "Chýba alebo je neplatné ID oddelenia.";
    exit;
}
$oddelenie_id = (int)$oddelenie_id; // Pretypovanie na int

try {
    // Vytvorenie objektu DateTime z reťazca
    $date = new DateTime($date_str);
    
    // Volanie funkcie na prepočítanie priradení
    $result = calculateDailyAssignments($date, $oddelenie_id, $pdo); // Odovzdáme oddelenie_id
    
    // Odpoveď
    echo "Priradenia boli úspešne prepočítané";
} catch (Exception $e) {
    http_response_code(500);
    echo "Chyba pri prepočítavaní priradení: " . $e->getMessage();
}
?>