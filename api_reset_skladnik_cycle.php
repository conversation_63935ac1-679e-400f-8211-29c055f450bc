<?php
// Súbor: api_reset_skladnik_cycle.php
require_once 'config.php';
require_once 'functions.php';

header('Content-Type: application/json');
$pdo = getDbConnection();

// Overenie prihlásenia - toto zostáva
requireLogin();
// $loggedUser = getUserById($_SESSION['user_id'], $pdo); // Môže zostať, ak by ste $loggedUser potrebovali na iné účely

// --- ODSTRÁNENÉ ŠPECIFICKÉ OVERENIE OPRÁVNENÍ PODĽA ROLY ---
// Akcia je teraz povolená pre každého prihláseného používateľa.
// --- KONIEC ODSTRÁNENIA OVERENIA OPRÁVNENÍ ---

// Skontrolujeme, či sme dostali ID oddelenia (toto je stále potrebné pre logiku skladníka)
if (!isset($_POST['oddelenie_id']) || !is_numeric($_POST['oddelenie_id'])) {
    echo json_encode(['success' => false, 'message' => 'Chýba ID oddelenia.']);
    exit;
}
$oddelenie_id = (int)$_POST['oddelenie_id'];

try {
    $pdo->beginTransaction();

    // 1. Získame zoznam skladníkov (zoradený, aby sme vedeli kto je S1 a S2)
    $stmtAllSkl = $pdo->prepare("SELECT id FROM users WHERE role1 = 'skladnik' AND is_active = 1 AND oddelenie_id = ? ORDER BY name ASC");
    $stmtAllSkl->execute([$oddelenie_id]);
    $skladnici_ids = $stmtAllSkl->fetchAll(PDO::FETCH_COLUMN);

    if (count($skladnici_ids) < 2) {
        throw new Exception('Pre túto akciu musia byť v oddelení aspoň dvaja aktívni skladníci.');
    }

    // 2. Získame aktuálne referenčné dáta
    $stmtSkladnikRef = $pdo->prepare("SELECT skladnik_ref_date, skladnik_ref_starts_s1 FROM Oddelenia WHERE oddelenie_id = ?");
    $stmtSkladnikRef->execute([$oddelenie_id]);
    $skladnikRefData = $stmtSkladnikRef->fetch(PDO::FETCH_ASSOC);

    $refDate = new DateTimeImmutable($skladnikRefData['skladnik_ref_date'] ?? '2025-04-14'); // Fallback
    $startsS1 = (bool)($skladnikRefData['skladnik_ref_starts_s1'] ?? true); // Fallback

    // 3. Vypočítame, kto je priradený DNES podľa STAREJ logiky
    $today = new DateTimeImmutable('today');
    $daysDiff = $refDate->diff($today)->days;
    $blockIndex = floor($daysDiff / 14);
    $startIndex = $startsS1 ? 0 : 1;
    $currentlyAssignedIndex = ($startIndex + $blockIndex) % count($skladnici_ids);

    // 4. Určíme, kto začne NOVÝ cyklus (ten druhý)
    $newCycleStartIndex = ($currentlyAssignedIndex + 1) % count($skladnici_ids);

    // 5. Pripravíme nové dáta pre DB
    $new_ref_date = $today->format('Y-m-d');
    $new_starts_s1 = ($newCycleStartIndex === 0); 

    // 6. Aktualizujeme DB
    $updateStmt = $pdo->prepare("UPDATE Oddelenia SET skladnik_ref_date = ?, skladnik_ref_starts_s1 = ? WHERE oddelenie_id = ?");
    $updateStmt->execute([$new_ref_date, $new_starts_s1, $oddelenie_id]);

    // 7. Vymažeme budúce metadáta, aby sa vynútil prepočet
    $deleteMetaStmt = $pdo->prepare("DELETE FROM assignment_metadata WHERE oddelenie_id = ? AND assignment_date >= ?");
    $deleteMetaStmt->execute([$oddelenie_id, $today->format('Y-m-d')]);

    $pdo->commit();

    echo json_encode(['success' => true, 'message' => 'Cyklus skladníkov bol úspešne resetovaný.']);

} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    http_response_code(500);
    error_log("API Chyba pri resetovaní cyklu skladníkov: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Chyba: ' . $e->getMessage()]);
}
?>
