-- MySQL dump 10.13  Distrib 9.2.0, for macos15.2 (arm64)
--
-- Host: sql16.hostcreators.sk    Database: d52810_dochadzka
-- ------------------------------------------------------
-- Server version	8.0.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `Oddelenia`
--

DROP TABLE IF EXISTS `Oddelenia`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `Oddelenia` (
  `oddelenie_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `nazov` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`oddelenie_id`),
  UNIQUE KEY `oddelenie_id` (`oddelenie_id`),
  UNIQUE KEY `nazov` (`nazov`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `Oddelenia`
--

LOCK TABLES `Oddelenia` WRITE;
/*!40000 ALTER TABLE `Oddelenia` DISABLE KEYS */;
INSERT INTO `Oddelenia` VALUES (1,'ID1'),(2,'ID3'),(4,'Sekretariát'),(3,'Vnútorné');
/*!40000 ALTER TABLE `Oddelenia` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `absences`
--

DROP TABLE IF EXISTS `absences`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `absences` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `absence_type` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `absences_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=505 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `absences`
--

LOCK TABLES `absences` WRITE;
/*!40000 ALTER TABLE `absences` DISABLE KEYS */;
INSERT INTO `absences` VALUES (105,3,'2025-04-30','2025-04-30','Lekár','2025-04-25 07:31:50','2025-04-25 09:31:50'),(107,4,'2025-04-29','2025-04-29','Dovolenka','2025-04-25 07:33:05','2025-04-25 09:33:05'),(111,4,'2025-05-06','2025-05-06','Dovolenka','2025-04-25 07:35:17','2025-04-25 09:35:17'),(112,9,'2025-05-09','2025-05-09','Dovolenka','2025-04-25 07:35:41','2025-04-25 09:35:41'),(114,2,'2025-05-09','2025-05-09','Lekár','2025-04-25 07:36:16','2025-04-25 09:36:16'),(126,5,'2025-04-29','2025-04-29','Dovolenka','2025-04-28 05:54:40','2025-04-28 07:54:40'),(129,6,'2025-04-29','2025-04-29','Dovolenka','2025-04-28 10:47:13','2025-04-28 12:47:13'),(130,6,'2025-04-30','2025-04-30','Lekár','2025-04-28 10:47:38','2025-04-28 12:47:38'),(139,12,'2025-06-11','2025-06-11','Dovolenka','2025-04-28 10:58:52','2025-04-28 12:58:52'),(140,12,'2025-06-12','2025-06-12','Dovolenka','2025-04-28 10:58:57','2025-04-28 12:58:57'),(141,12,'2025-06-13','2025-06-13','Dovolenka','2025-04-28 10:59:00','2025-04-28 12:59:00'),(142,12,'2025-07-14','2025-07-14','Dovolenka','2025-04-28 10:59:17','2025-04-28 12:59:17'),(143,12,'2025-07-15','2025-07-15','Dovolenka','2025-04-28 10:59:21','2025-04-28 12:59:21'),(144,12,'2025-07-16','2025-07-16','Dovolenka','2025-04-28 10:59:23','2025-04-28 12:59:23'),(145,12,'2025-07-17','2025-07-17','Dovolenka','2025-04-28 10:59:26','2025-04-28 12:59:26'),(146,12,'2025-07-21','2025-07-21','Dovolenka','2025-04-28 10:59:34','2025-04-28 12:59:34'),(147,12,'2025-07-22','2025-07-22','Dovolenka','2025-04-28 10:59:37','2025-04-28 12:59:37'),(148,12,'2025-07-23','2025-07-23','Dovolenka','2025-04-28 10:59:40','2025-04-28 12:59:40'),(149,12,'2025-07-24','2025-07-24','Dovolenka','2025-04-28 10:59:43','2025-04-28 12:59:43'),(150,12,'2025-07-25','2025-07-25','Dovolenka','2025-04-28 10:59:46','2025-04-28 12:59:46'),(151,12,'2025-07-14','2025-08-01','Dovolenka','2025-04-28 11:00:27','2025-04-28 13:00:27'),(155,4,'2025-04-30','2025-04-30','Lekár','2025-04-29 11:56:29','2025-04-29 13:56:29'),(172,3,'2025-05-02','2025-05-02','Dovolenka','2025-04-29 12:31:43','2025-04-29 14:31:43'),(173,5,'2025-05-02','2025-05-02','Dovolenka','2025-04-29 12:31:47','2025-04-29 14:31:47'),(174,9,'2025-05-02','2025-05-02','Dovolenka','2025-04-29 12:31:51','2025-04-29 14:31:51'),(175,8,'2025-05-02','2025-05-02','Dovolenka','2025-04-29 12:32:19','2025-04-29 14:32:19'),(176,7,'2025-05-02','2025-05-02','Dovolenka','2025-04-29 12:32:50','2025-04-29 14:32:50'),(178,1,'2025-04-30','2025-04-30','Dovolenka','2025-04-29 12:42:37','2025-04-29 14:42:37'),(182,12,'2025-04-29','2025-04-29','Iné','2025-04-29 15:20:13','2025-04-29 17:20:13'),(184,7,'2025-04-30','2025-04-30','Lekár','2025-04-29 16:13:15','2025-04-29 18:13:15'),(266,8,'2025-05-15','2025-05-15','Dovolenka','2025-05-03 08:45:17','2025-05-03 10:45:17'),(267,8,'2025-05-16','2025-05-16','Dovolenka','2025-05-03 08:45:20','2025-05-03 10:45:20'),(269,9,'2025-05-05','2025-05-05','Dovolenka','2025-05-03 11:04:51','2025-05-03 13:04:51'),(288,10,'2025-05-09','2025-05-09','Dovolenka','2025-05-04 04:35:06','2025-05-04 06:35:06'),(291,2,'2025-05-05','2025-05-05','Dovolenka','2025-05-05 04:56:55','2025-05-05 06:56:55'),(292,2,'2025-05-07','2025-05-07','Dovolenka','2025-05-05 05:01:53','2025-05-05 07:01:53'),(302,5,'2025-05-09','2025-05-09','Dovolenka','2025-05-09 05:56:18','2025-05-09 07:56:18'),(303,11,'2025-05-09','2025-05-09','Dovolenka','2025-05-09 05:57:23','2025-05-09 07:57:23'),(353,12,'2025-05-09','2025-05-09','Dovolenka','2025-05-09 17:39:18','2025-05-09 19:39:18'),(357,4,'2025-05-12','2025-05-12','Dovolenka','2025-05-12 05:17:48','2025-05-12 07:17:48'),(360,9,'2025-05-13','2025-05-13','PN','2025-05-12 10:38:25','2025-05-12 12:38:25'),(361,9,'2025-05-14','2025-05-14','PN','2025-05-12 10:38:46','2025-05-12 12:38:46'),(363,9,'2025-05-15','2025-05-15','PN','2025-05-12 10:39:14','2025-05-12 12:39:14'),(364,9,'2025-05-16','2025-05-16','PN','2025-05-12 10:39:29','2025-05-12 12:39:29'),(365,9,'2025-05-12','2025-05-12','Dovolenka','2025-05-12 11:58:02','2025-05-12 13:58:02'),(370,4,'2025-05-13','2025-05-13','Dovolenka','2025-05-12 11:59:53','2025-05-12 13:59:53'),(371,10,'2025-05-13','2025-05-13','Dovolenka','2025-05-12 12:06:19','2025-05-12 14:06:19'),(374,4,'2025-05-14','2025-05-14','PN','2025-05-13 11:52:24','2025-05-13 13:52:24'),(376,4,'2025-05-15','2025-05-15','PN','2025-05-14 07:38:36','2025-05-14 09:38:36'),(377,4,'2025-05-16','2025-05-16','PN','2025-05-14 07:38:43','2025-05-14 09:38:43'),(378,4,'2025-05-19','2025-05-19','PN','2025-05-14 07:39:01','2025-05-14 09:39:01'),(380,12,'2025-05-14','2025-05-14','Dovolenka','2025-05-14 07:39:58','2025-05-14 09:39:58'),(410,2,'2025-05-16','2025-05-16','Lekár','2025-05-14 19:57:17','2025-05-14 21:57:17'),(416,4,'2025-05-20','2025-05-20','PN','2025-05-19 08:35:59','2025-05-19 10:35:59'),(417,4,'2025-05-21','2025-05-21','PN','2025-05-19 08:36:01','2025-05-19 10:36:01'),(418,4,'2025-05-22','2025-05-22','PN','2025-05-19 08:36:04','2025-05-19 10:36:04'),(419,4,'2025-05-23','2025-05-23','PN','2025-05-19 08:36:08','2025-05-19 10:36:08'),(420,4,'2025-05-26','2025-05-26','PN','2025-05-19 08:36:18','2025-05-19 10:36:18'),(422,11,'2025-05-20','2025-05-20','Dovolenka','2025-05-19 18:44:22','2025-05-19 20:44:22'),(423,2,'2025-06-25','2025-06-25','Dovolenka','2025-05-19 18:49:07','2025-05-19 20:49:07'),(424,2,'2025-06-26','2025-06-26','Dovolenka','2025-05-19 18:49:09','2025-05-19 20:49:09'),(425,2,'2025-06-27','2025-06-27','Dovolenka','2025-05-19 18:49:10','2025-05-19 20:49:10'),(426,2,'2025-07-14','2025-07-14','Dovolenka','2025-05-19 18:49:35','2025-05-19 20:49:35'),(427,2,'2025-07-15','2025-07-15','Dovolenka','2025-05-19 18:49:37','2025-05-19 20:49:37'),(428,2,'2025-07-16','2025-07-16','Dovolenka','2025-05-19 18:49:38','2025-05-19 20:49:38'),(429,2,'2025-07-17','2025-07-17','Dovolenka','2025-05-19 18:49:40','2025-05-19 20:49:40'),(430,2,'2025-07-18','2025-07-18','Dovolenka','2025-05-19 18:49:41','2025-05-19 20:49:41'),(431,2,'2025-07-21','2025-07-21','Dovolenka','2025-05-19 18:49:43','2025-05-19 20:49:43'),(432,2,'2025-07-22','2025-07-22','Dovolenka','2025-05-19 18:49:45','2025-05-19 20:49:45'),(433,2,'2025-07-23','2025-07-23','Dovolenka','2025-05-19 18:49:46','2025-05-19 20:49:46'),(434,2,'2025-07-24','2025-07-24','Dovolenka','2025-05-19 18:49:47','2025-05-19 20:49:47'),(435,2,'2025-07-25','2025-07-25','Dovolenka','2025-05-19 18:49:49','2025-05-19 20:49:49'),(436,2,'2025-07-28','2025-07-28','Dovolenka','2025-05-19 18:49:51','2025-05-19 20:49:51'),(437,2,'2025-07-29','2025-07-29','Dovolenka','2025-05-19 18:49:53','2025-05-19 20:49:53'),(438,2,'2025-07-30','2025-07-30','Dovolenka','2025-05-19 18:49:54','2025-05-19 20:49:54'),(440,6,'2025-05-21','2025-05-21','Lekár','2025-05-20 07:29:03','2025-05-20 09:29:03'),(443,1,'2025-05-21','2025-05-21','Dovolenka','2025-05-20 11:28:39','2025-05-20 13:28:39'),(460,9,'2025-05-19','2025-05-19','PN','2025-05-20 15:55:18','2025-05-20 17:55:18'),(461,9,'2025-05-20','2025-05-20','PN','2025-05-20 15:55:24','2025-05-20 17:55:24'),(463,8,'2025-05-19','2025-05-19','Dovolenka','2025-05-20 15:56:03','2025-05-20 17:56:03'),(464,2,'2025-05-19','2025-05-19','Lekár','2025-05-20 15:56:48','2025-05-20 17:56:48'),(468,6,'2025-05-26','2025-05-26','Lekár','2025-05-22 08:06:57','2025-05-22 10:06:57'),(469,8,'2025-05-23','2025-05-23','Dovolenka','2025-05-22 19:42:34','2025-05-22 21:42:34'),(470,5,'2025-05-26','2025-05-26','Dovolenka','2025-05-23 12:26:32','2025-05-23 14:26:32'),(475,4,'2025-05-27','2025-05-27','PN','2025-05-26 08:59:00','2025-05-26 10:59:00'),(476,4,'2025-05-28','2025-05-28','PN','2025-05-26 08:59:03','2025-05-26 10:59:03'),(477,4,'2025-05-29','2025-05-29','PN','2025-05-26 08:59:05','2025-05-26 10:59:05'),(478,4,'2025-05-30','2025-05-30','PN','2025-05-26 08:59:07','2025-05-26 10:59:07'),(479,4,'2025-06-02','2025-06-02','PN','2025-05-26 08:59:14','2025-05-26 10:59:14'),(481,7,'2025-07-14','2025-07-14','Dovolenka','2025-05-26 12:35:47','2025-05-26 14:35:47'),(482,7,'2025-07-25','2025-07-25','Dovolenka','2025-05-26 12:35:55','2025-05-26 14:35:55'),(483,7,'2025-07-15','2025-07-15','Dovolenka','2025-05-26 12:36:01','2025-05-26 14:36:01'),(485,7,'2025-07-16','2025-07-16','Dovolenka','2025-05-26 12:36:05','2025-05-26 14:36:05'),(487,7,'2025-07-17','2025-07-17','Dovolenka','2025-05-26 12:36:09','2025-05-26 14:36:09'),(489,7,'2025-07-18','2025-07-18','Dovolenka','2025-05-26 12:36:10','2025-05-26 14:36:10'),(490,7,'2025-07-21','2025-07-21','Dovolenka','2025-05-26 12:36:13','2025-05-26 14:36:13'),(491,7,'2025-07-22','2025-07-22','Dovolenka','2025-05-26 12:36:14','2025-05-26 14:36:14'),(492,7,'2025-07-23','2025-07-23','Dovolenka','2025-05-26 12:36:16','2025-05-26 14:36:16'),(494,7,'2025-07-24','2025-07-24','Dovolenka','2025-05-26 12:36:18','2025-05-26 14:36:18'),(496,3,'2025-05-30','2025-05-30','Dovolenka','2025-05-27 07:13:31','2025-05-27 09:13:31'),(497,3,'2025-06-02','2025-06-02','Dovolenka','2025-05-27 07:13:40','2025-05-27 09:13:40'),(499,12,'2025-05-28','2025-05-28','Sprevádzanie','2025-05-27 11:34:28','2025-05-27 13:34:28'),(501,1,'2025-05-28','2025-05-28','Dovolenka','2025-05-28 16:35:50','2025-05-28 18:35:50'),(503,7,'2025-05-30','2025-05-30','Lekár','2025-05-29 06:34:53','2025-05-29 08:34:53');
/*!40000 ALTER TABLE `absences` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `assignment_metadata`
--

DROP TABLE IF EXISTS `assignment_metadata`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assignment_metadata` (
  `assignment_date` date NOT NULL,
  `oddelenie_id` bigint unsigned NOT NULL,
  `signature` char(32) COLLATE utf8mb4_unicode_ci NOT NULL,
  `updated_at` datetime NOT NULL,
  `last_absence` date DEFAULT NULL,
  `last_downtime` date DEFAULT NULL,
  PRIMARY KEY (`assignment_date`,`oddelenie_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `assignment_metadata`
--

LOCK TABLES `assignment_metadata` WRITE;
/*!40000 ALTER TABLE `assignment_metadata` DISABLE KEYS */;
INSERT INTO `assignment_metadata` VALUES ('2025-04-23',1,'bc9cc6b1a7ef1c1d5acc9871fe43797d','2025-04-23 22:26:45',NULL,NULL),('2025-04-24',1,'1702c81263ea76c43428f5dbdb179d54','2025-04-24 18:13:16',NULL,NULL),('2025-04-25',1,'468029558d130a417f455f2b0de4a229','2025-04-25 09:54:38',NULL,NULL),('2025-04-28',1,'d4a08c0aab7aadd2b5481edf54f0f08c','2025-04-27 18:26:39',NULL,NULL),('2025-04-29',1,'6010a269633dc6005c529fe1bd25441a','2025-04-29 21:30:08',NULL,NULL),('2025-04-30',1,'131102b14b4989cdb90b9ec0a823ebfe','2025-04-30 20:57:35',NULL,NULL),('2025-05-02',1,'db9cc25172aced0bce473c7da896931e','2025-05-01 02:10:56',NULL,NULL),('2025-05-05',1,'9fb84b9559cfd1a589e1e90d39c36a96','2025-05-05 06:56:55',NULL,NULL),('2025-05-06',1,'20f6edd8eb6d6316b2aef24a8f157303','2025-05-05 08:27:03',NULL,NULL),('2025-05-07',1,'92e9e18eb8301a648258e5fa6329b528','2025-05-06 22:46:05',NULL,NULL),('2025-05-09',1,'514a9e35371a8bdc02f86f344cf95ad2','2025-05-09 19:39:18',NULL,NULL),('2025-05-12',1,'88e6215b8ff203257aa76c565335a5b4','2025-05-12 15:42:11',NULL,NULL),('2025-05-13',1,'b4eeb2ccabc77ffc1a0e422c152083b6','2025-05-13 22:58:25',NULL,NULL),('2025-05-14',1,'174897ae03551a81ef73726805db95e1','2025-05-14 21:55:45',NULL,NULL),('2025-05-15',1,'403b80484e16301be524a9bab224369a','2025-05-15 19:30:00',NULL,NULL),('2025-05-16',1,'ae5d4f5e1dc24bfefcb7530972de3da8','2025-05-16 11:35:44',NULL,NULL),('2025-05-19',1,'a762246402c5ed2ff4cac5d1c2055efd','2025-05-20 17:09:34',NULL,NULL),('2025-05-20',1,'488165711ab94ca47ef7b52cee195ad9','2025-05-20 23:16:22',NULL,NULL),('2025-05-21',1,'60dce4968b7c2b84acca6f7376ff94fb','2025-05-21 19:50:57',NULL,NULL),('2025-05-22',1,'6946359325e159a9f96f8fd90ab63138','2025-05-22 23:31:14',NULL,NULL),('2025-05-23',1,'e20c509a784047865a957528ace23054','2025-05-23 21:28:51',NULL,NULL),('2025-05-26',1,'d35791fb5a2223e038eb4461581710c4','2025-05-26 18:35:48',NULL,NULL),('2025-05-27',1,'af72bc1edb41b666fbaec7dd0c24a8eb','2025-05-27 23:53:21',NULL,NULL),('2025-05-28',0,'ddd40721e6db9992dc981b456270395f','2025-05-28 22:36:43',NULL,NULL),('2025-05-29',0,'cd57fa7967a2a03b57b5acae3a24e4d5','2025-05-29 15:03:18',NULL,NULL),('2025-05-30',0,'447a1b1744ed3fdd8d972102e2fbc602','2025-05-29 15:03:08',NULL,NULL),('2025-06-02',0,'d55c906e93c16fbd093fb609fcc4013c','2025-05-29 10:19:05',NULL,NULL),('2025-06-03',0,'9a27279b03a5d43021229b1f97277462','2025-05-29 10:19:08',NULL,NULL),('2025-06-04',0,'a4cdbe0d2e44eb0de8fa3786eac0244a','2025-05-29 10:19:20',NULL,NULL);
/*!40000 ALTER TABLE `assignment_metadata` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `assignments`
--

DROP TABLE IF EXISTS `assignments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assignments` (
  `id` int NOT NULL AUTO_INCREMENT,
  `assignment_date` date NOT NULL,
  `user_id` int NOT NULL,
  `assigned_role` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'veduci, schifflieder, skladnik, strojnik, kontrola',
  `machine_number` int DEFAULT NULL COMMENT '1, 2, or 3 if assigned_role is strojnik',
  `assignment_order` int DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `date_user` (`assignment_date`,`user_id`),
  KEY `user_id` (`user_id`),
  KEY `idx_assignment_date` (`assignment_date`),
  CONSTRAINT `assignments_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=48147 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `assignments`
--

LOCK TABLES `assignments` WRITE;
/*!40000 ALTER TABLE `assignments` DISABLE KEYS */;
INSERT INTO `assignments` VALUES (31851,'2025-04-23',3,'schifflieder',NULL,1),(31852,'2025-04-23',4,'skladnik',NULL,1),(31856,'2025-04-23',11,'kontrola',NULL,1),(31857,'2025-04-23',9,'kontrola',NULL,1),(31859,'2025-04-23',10,'kontrola',NULL,1),(32091,'2025-04-24',2,'schifflieder',NULL,1),(32092,'2025-04-24',4,'skladnik',NULL,1),(32096,'2025-04-24',11,'kontrola',NULL,1),(32098,'2025-04-24',6,'kontrola',NULL,1),(32099,'2025-04-24',8,'kontrola',NULL,1),(32216,'2025-04-25',3,'schifflieder',NULL,1),(32217,'2025-04-25',4,'skladnik',NULL,1),(32221,'2025-04-25',9,'kontrola',NULL,1),(32223,'2025-04-25',10,'kontrola',NULL,1),(32249,'2025-04-24',7,'strojnik',1,1),(32251,'2025-04-24',12,'strojnik',3,1),(32252,'2025-04-24',9,'strojnik',2,1),(32253,'2025-04-23',5,'strojnik',3,1),(32254,'2025-04-23',6,'strojnik',2,1),(32255,'2025-04-23',8,'strojnik',1,1),(32256,'2025-04-22',7,'strojnik',3,1),(32257,'2025-04-22',12,'strojnik',2,1),(32258,'2025-04-22',11,'strojnik',1,1),(32259,'2025-04-17',10,'strojnik',2,1),(32260,'2025-04-17',9,'strojnik',1,1),(32261,'2025-04-17',6,'strojnik',3,1),(32262,'2025-04-16',7,'strojnik',2,1),(32263,'2025-04-16',12,'strojnik',1,1),(32264,'2025-04-16',11,'strojnik',3,1),(32265,'2025-04-15',10,'strojnik',1,1),(32266,'2025-04-15',9,'strojnik',2,1),(32267,'2025-04-15',5,'strojnik',3,1),(32268,'2025-04-14',8,'strojnik',3,1),(32269,'2025-04-14',6,'strojnik',2,1),(32270,'2025-04-14',7,'strojnik',1,1),(32271,'2025-04-11',12,'strojnik',3,1),(32272,'2025-04-11',11,'strojnik',1,1),(32273,'2025-04-11',4,'strojnik',2,1),(32274,'2025-04-10',10,'strojnik',3,1),(32275,'2025-04-10',9,'strojnik',1,1),(32276,'2025-04-10',8,'strojnik',2,1),(32277,'2025-04-09',6,'strojnik',1,1),(32278,'2025-04-09',7,'strojnik',3,1),(32279,'2025-04-09',12,'strojnik',2,1),(32280,'2025-04-08',10,'strojnik',2,1),(32281,'2025-04-08',9,'strojnik',3,1),(32282,'2025-04-08',8,'strojnik',1,1),(32283,'2025-04-07',6,'strojnik',3,1),(32284,'2025-04-07',7,'strojnik',2,1),(32285,'2025-04-07',12,'strojnik',1,1),(32286,'2025-04-04',11,'strojnik',2,1),(32287,'2025-04-04',4,'strojnik',1,1),(32288,'2025-04-04',8,'strojnik',3,1),(32289,'2025-04-03',7,'strojnik',1,1),(32290,'2025-04-03',6,'strojnik',2,1),(32298,'2025-04-02',11,'strojnik',1,1),(32299,'2025-04-02',10,'strojnik',3,1),(32300,'2025-04-02',8,'strojnik',2,1),(32301,'2025-04-01',6,'strojnik',1,1),(32302,'2025-04-01',7,'strojnik',3,1),(32303,'2025-04-01',12,'strojnik',2,1),(32304,'2025-04-03',12,'strojnik',3,1),(32305,'2025-04-25',6,'strojnik',3,1),(32306,'2025-04-25',8,'strojnik',2,1),(32307,'2025-04-25',5,'strojnik',1,1),(34312,'2025-04-27',3,'schifflieder',NULL,1),(34506,'2025-04-28',2,'schifflieder',NULL,1),(34507,'2025-04-28',5,'skladnik',NULL,1),(34511,'2025-04-28',11,'kontrola',NULL,1),(34512,'2025-04-28',12,'kontrola',NULL,1),(34513,'2025-04-28',6,'kontrola',NULL,1),(34514,'2025-04-28',8,'kontrola',NULL,1),(34515,'2025-04-28',7,'kontrola',NULL,1),(34516,'2025-04-28',9,'strojnik',3,1),(34606,'2025-04-28',4,'strojnik',1,1),(34607,'2025-04-28',10,'strojnik',2,1),(35391,'2025-04-29',3,'schifflieder',NULL,2),(35392,'2025-04-29',1,'skladnik',NULL,NULL),(35396,'2025-04-29',12,'strojnik',0,NULL),(35397,'2025-04-29',9,'kontrola',NULL,NULL),(35398,'2025-04-29',10,'kontrola',NULL,NULL),(36177,'2025-04-29',6,'strojnik',0,1),(36178,'2025-04-30',2,'schifflieder',NULL,1),(36179,'2025-04-30',5,'skladnik',NULL,NULL),(36183,'2025-04-30',4,'strojnik',0,NULL),(36184,'2025-04-30',12,'kontrola',NULL,NULL),(36186,'2025-04-30',9,'strojnik',1,1),(36187,'2025-04-30',10,'strojnik',3,1),(36189,'2025-04-30',11,'strojnik',2,1),(36190,'2025-04-29',11,'strojnik',1,1),(36191,'2025-04-29',8,'strojnik',3,1),(36192,'2025-04-29',7,'strojnik',2,1),(36254,'2025-05-02',2,'schifflieder',NULL,1),(36255,'2025-05-02',4,'skladnik',NULL,NULL),(36256,'2025-05-02',12,'strojnik',1,NULL),(36257,'2025-05-02',6,'strojnik',3,NULL),(36258,'2025-05-02',7,'strojnik',0,NULL),(36259,'2025-05-02',11,'kontrola',NULL,NULL),(36260,'2025-05-02',10,'kontrola',NULL,NULL),(36757,'2025-05-05',3,'schifflieder',NULL,2),(36758,'2025-05-05',5,'skladnik',NULL,NULL),(36759,'2025-05-05',8,'strojnik',1,NULL),(36760,'2025-05-05',10,'strojnik',2,NULL),(36761,'2025-05-05',4,'strojnik',3,NULL),(36762,'2025-05-05',9,'strojnik',0,NULL),(36763,'2025-05-05',11,'kontrola',NULL,NULL),(36764,'2025-05-05',12,'kontrola',NULL,NULL),(36765,'2025-05-05',6,'kontrola',NULL,NULL),(36766,'2025-05-05',7,'kontrola',NULL,NULL),(36776,'2025-05-06',3,'schifflieder',NULL,2),(36777,'2025-05-06',5,'skladnik',NULL,NULL),(36778,'2025-05-06',11,'strojnik',1,NULL),(36779,'2025-05-06',12,'strojnik',2,NULL),(36780,'2025-05-06',7,'strojnik',3,NULL),(36781,'2025-05-06',9,'kontrola',NULL,NULL),(36782,'2025-05-06',6,'kontrola',NULL,NULL),(36783,'2025-05-06',10,'kontrola',NULL,NULL),(36784,'2025-05-06',8,'kontrola',NULL,NULL),(36971,'2025-05-07',3,'schifflieder',NULL,2),(36972,'2025-05-07',5,'skladnik',NULL,NULL),(36973,'2025-05-07',6,'strojnik',1,NULL),(36974,'2025-05-07',8,'strojnik',2,NULL),(36975,'2025-05-07',9,'strojnik',3,NULL),(36976,'2025-05-07',11,'kontrola',NULL,NULL),(36977,'2025-05-07',4,'kontrola',NULL,NULL),(36978,'2025-05-07',12,'kontrola',NULL,NULL),(36979,'2025-05-07',10,'kontrola',NULL,NULL),(36980,'2025-05-07',7,'kontrola',NULL,NULL),(39193,'2025-05-09',3,'schifflieder',NULL,2),(39194,'2025-05-09',4,'skladnik',NULL,NULL),(39195,'2025-05-09',10,'strojnik',0,NULL),(39196,'2025-05-09',11,'strojnik',0,NULL),(39197,'2025-05-09',12,'strojnik',0,NULL),(39198,'2025-05-09',7,'strojnik',1,NULL),(39199,'2025-05-09',6,'strojnik',3,NULL),(39200,'2025-05-09',8,'kontrola',NULL,NULL),(40686,'2025-05-12',2,'schifflieder',NULL,1),(40687,'2025-05-12',5,'skladnik',NULL,NULL),(40688,'2025-05-12',9,'strojnik',0,NULL),(40689,'2025-05-12',4,'strojnik',0,NULL),(40690,'2025-05-12',8,'strojnik',3,NULL),(40691,'2025-05-12',10,'strojnik',1,NULL),(40692,'2025-05-12',11,'strojnik',2,NULL),(40693,'2025-05-12',12,'kontrola',NULL,NULL),(40694,'2025-05-12',6,'kontrola',NULL,NULL),(40695,'2025-05-12',7,'kontrola',NULL,NULL),(40868,'2025-05-13',3,'schifflieder',NULL,2),(40869,'2025-05-13',5,'skladnik',NULL,NULL),(40870,'2025-05-13',12,'strojnik',3,NULL),(40871,'2025-05-13',7,'strojnik',2,NULL),(40872,'2025-05-13',6,'strojnik',1,NULL),(40873,'2025-05-13',11,'kontrola',NULL,NULL),(40874,'2025-05-13',8,'kontrola',NULL,NULL),(41648,'2025-05-14',2,'schifflieder',NULL,1),(41649,'2025-05-14',5,'skladnik',NULL,NULL),(41650,'2025-05-14',9,'strojnik',0,NULL),(41651,'2025-05-14',4,'strojnik',0,NULL),(41652,'2025-05-14',8,'strojnik',1,NULL),(41653,'2025-05-14',10,'strojnik',2,NULL),(41654,'2025-05-14',11,'strojnik',3,NULL),(41655,'2025-05-14',6,'kontrola',NULL,NULL),(41656,'2025-05-14',7,'kontrola',NULL,NULL),(41844,'2025-05-15',3,'schifflieder',NULL,2),(41845,'2025-05-15',5,'skladnik',NULL,NULL),(41846,'2025-05-15',12,'strojnik',1,NULL),(41847,'2025-05-15',7,'strojnik',3,NULL),(41848,'2025-05-15',6,'strojnik',2,NULL),(41849,'2025-05-15',11,'kontrola',NULL,NULL),(41850,'2025-05-15',10,'kontrola',NULL,NULL),(41891,'2025-05-16',3,'schifflieder',NULL,2),(41892,'2025-05-16',5,'skladnik',NULL,NULL),(41893,'2025-05-16',8,'strojnik',0,NULL),(41894,'2025-05-16',9,'strojnik',0,NULL),(41895,'2025-05-16',4,'strojnik',0,NULL),(41896,'2025-05-16',10,'strojnik',3,NULL),(41897,'2025-05-16',11,'strojnik',1,NULL),(41898,'2025-05-16',12,'strojnik',2,NULL),(41899,'2025-05-16',6,'kontrola',NULL,NULL),(41900,'2025-05-16',7,'kontrola',NULL,NULL),(43393,'2025-05-19',3,'schifflieder',NULL,2),(43394,'2025-05-19',5,'skladnik',NULL,NULL),(43395,'2025-05-19',8,'strojnik',0,NULL),(43396,'2025-05-19',9,'strojnik',0,NULL),(43398,'2025-05-19',6,'strojnik',3,NULL),(43400,'2025-05-19',11,'kontrola',NULL,NULL),(43401,'2025-05-19',12,'kontrola',NULL,NULL),(43572,'2025-05-19',10,'strojnik',2,1),(43573,'2025-05-19',7,'strojnik',1,1),(44344,'2025-05-20',2,'schifflieder',NULL,1),(44345,'2025-05-20',5,'skladnik',NULL,NULL),(44346,'2025-05-20',4,'strojnik',0,NULL),(44347,'2025-05-20',11,'strojnik',0,NULL),(44348,'2025-05-20',12,'strojnik',3,NULL),(44349,'2025-05-20',7,'strojnik',2,NULL),(44350,'2025-05-20',6,'strojnik',1,NULL),(44351,'2025-05-20',10,'kontrola',NULL,NULL),(44352,'2025-05-20',8,'kontrola',NULL,NULL),(44597,'2025-05-21',3,'schifflieder',NULL,2),(44598,'2025-05-21',5,'skladnik',NULL,NULL),(44599,'2025-05-21',8,'strojnik',3,NULL),(44600,'2025-05-21',9,'strojnik',2,NULL),(44601,'2025-05-21',10,'strojnik',1,NULL),(44602,'2025-05-21',11,'kontrola',NULL,NULL),(44603,'2025-05-21',12,'kontrola',NULL,NULL),(44604,'2025-05-21',7,'kontrola',NULL,NULL),(45363,'2025-05-22',2,'schifflieder',NULL,1),(45364,'2025-05-22',5,'skladnik',NULL,NULL),(45365,'2025-05-22',4,'strojnik',0,NULL),(45366,'2025-05-22',11,'strojnik',2,NULL),(45367,'2025-05-22',12,'strojnik',1,NULL),(45368,'2025-05-22',7,'strojnik',3,NULL),(45369,'2025-05-22',9,'kontrola',NULL,NULL),(45370,'2025-05-22',6,'kontrola',NULL,NULL),(45371,'2025-05-22',10,'kontrola',NULL,NULL),(45372,'2025-05-22',8,'kontrola',NULL,NULL),(45926,'2025-05-23',3,'schifflieder',NULL,2),(45927,'2025-05-23',5,'skladnik',NULL,NULL),(45928,'2025-05-23',8,'strojnik',0,NULL),(45929,'2025-05-23',6,'strojnik',2,NULL),(45930,'2025-05-23',9,'strojnik',1,NULL),(45931,'2025-05-23',10,'strojnik',3,NULL),(45932,'2025-05-23',11,'kontrola',NULL,NULL),(45933,'2025-05-23',12,'kontrola',NULL,NULL),(45934,'2025-05-23',7,'kontrola',NULL,NULL),(46621,'2025-05-26',2,'schifflieder',NULL,1),(46622,'2025-05-26',4,'strojnik',0,NULL),(46623,'2025-05-26',11,'strojnik',3,NULL),(46624,'2025-05-26',12,'strojnik',2,NULL),(46625,'2025-05-26',7,'strojnik',1,NULL),(46626,'2025-05-26',9,'kontrola',NULL,NULL),(46627,'2025-05-26',10,'kontrola',NULL,NULL),(46628,'2025-05-26',8,'kontrola',NULL,NULL),(47137,'2025-05-27',3,'schifflieder',NULL,2),(47138,'2025-05-27',5,'skladnik',NULL,NULL),(47139,'2025-05-27',6,'strojnik',1,NULL),(47140,'2025-05-27',8,'strojnik',2,NULL),(47141,'2025-05-27',9,'strojnik',3,NULL),(47142,'2025-05-27',11,'kontrola',NULL,NULL),(47143,'2025-05-27',12,'kontrola',NULL,NULL),(47144,'2025-05-27',10,'kontrola',NULL,NULL),(47145,'2025-05-27',7,'kontrola',NULL,NULL),(47838,'2025-05-28',2,'schifflieder',NULL,1),(47839,'2025-05-28',5,'skladnik',NULL,NULL),(47840,'2025-05-28',4,'strojnik',0,NULL),(47841,'2025-05-28',12,'strojnik',0,NULL),(47842,'2025-05-28',10,'strojnik',2,NULL),(47843,'2025-05-28',11,'strojnik',1,NULL),(47844,'2025-05-28',7,'strojnik',3,NULL),(47845,'2025-05-28',9,'kontrola',NULL,NULL),(47846,'2025-05-28',6,'kontrola',NULL,NULL),(47847,'2025-05-28',8,'kontrola',NULL,NULL),(48055,'2025-06-02',2,'schifflieder',NULL,1),(48056,'2025-06-02',5,'skladnik',NULL,NULL),(48057,'2025-06-02',7,'strojnik',1,NULL),(48058,'2025-06-02',6,'strojnik',2,NULL),(48059,'2025-06-02',8,'strojnik',3,NULL),(48060,'2025-06-02',11,'kontrola',NULL,NULL),(48061,'2025-06-02',9,'kontrola',NULL,NULL),(48062,'2025-06-02',12,'kontrola',NULL,NULL),(48063,'2025-06-02',10,'kontrola',NULL,NULL),(48064,'2025-06-03',2,'schifflieder',NULL,1),(48065,'2025-06-03',5,'skladnik',NULL,NULL),(48066,'2025-06-03',9,'strojnik',1,NULL),(48067,'2025-06-03',10,'strojnik',3,NULL),(48068,'2025-06-03',4,'strojnik',2,NULL),(48069,'2025-06-03',11,'kontrola',NULL,NULL),(48070,'2025-06-03',12,'kontrola',NULL,NULL),(48071,'2025-06-03',6,'kontrola',NULL,NULL),(48072,'2025-06-03',8,'kontrola',NULL,NULL),(48073,'2025-06-03',7,'kontrola',NULL,NULL),(48074,'2025-06-04',3,'schifflieder',NULL,2),(48075,'2025-06-04',5,'skladnik',NULL,NULL),(48076,'2025-06-04',11,'strojnik',3,NULL),(48077,'2025-06-04',12,'strojnik',1,NULL),(48078,'2025-06-04',7,'strojnik',2,NULL),(48079,'2025-06-04',9,'kontrola',NULL,NULL),(48080,'2025-06-04',4,'kontrola',NULL,NULL),(48081,'2025-06-04',6,'kontrola',NULL,NULL),(48082,'2025-06-04',10,'kontrola',NULL,NULL),(48083,'2025-06-04',8,'kontrola',NULL,NULL),(48129,'2025-05-30',2,'schifflieder',NULL,1),(48130,'2025-05-30',5,'skladnik',NULL,NULL),(48131,'2025-05-30',4,'strojnik',0,NULL),(48132,'2025-05-30',10,'strojnik',1,NULL),(48133,'2025-05-30',11,'strojnik',2,NULL),(48134,'2025-05-30',12,'strojnik',3,NULL),(48135,'2025-05-30',9,'kontrola',NULL,NULL),(48136,'2025-05-30',6,'kontrola',NULL,NULL),(48137,'2025-05-30',8,'kontrola',NULL,NULL),(48138,'2025-05-29',3,'schifflieder',NULL,2),(48139,'2025-05-29',5,'skladnik',NULL,NULL),(48140,'2025-05-29',6,'strojnik',3,NULL),(48141,'2025-05-29',8,'strojnik',1,NULL),(48142,'2025-05-29',9,'strojnik',2,NULL),(48143,'2025-05-29',11,'kontrola',NULL,NULL),(48144,'2025-05-29',12,'kontrola',NULL,NULL),(48145,'2025-05-29',10,'kontrola',NULL,NULL),(48146,'2025-05-29',7,'kontrola',NULL,NULL);
/*!40000 ALTER TABLE `assignments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `holidays`
--

DROP TABLE IF EXISTS `holidays`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `holidays` (
  `id` int NOT NULL AUTO_INCREMENT,
  `year` int NOT NULL,
  `date` date NOT NULL,
  `localName` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `countryCode` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_date` (`date`),
  KEY `idx_holiday_year` (`year`),
  KEY `idx_holiday_date` (`date`)
) ENGINE=InnoDB AUTO_INCREMENT=61 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `holidays`
--

LOCK TABLES `holidays` WRITE;
/*!40000 ALTER TABLE `holidays` DISABLE KEYS */;
INSERT INTO `holidays` VALUES (31,2025,'2025-01-01','Deň vzniku Slovenskej republiky','Day of the Establishment of the Slovak Republic','SK'),(32,2025,'2025-01-06','Zjavenie Pána','Epiphany','SK'),(33,2025,'2025-04-18','Veľkonočný piatok','Good Friday','SK'),(34,2025,'2025-04-21','Veľkonočný pondelok','Easter Monday','SK'),(35,2025,'2025-05-01','Sviatok práce','International Workers\' Day','SK'),(36,2025,'2025-05-08','Deň víťazstva nad fašizmom','Day of victory over fascism','SK'),(37,2025,'2025-07-05','Sviatok svätého Cyrila a svätého Metoda','St. Cyril and Methodius Day','SK'),(38,2025,'2025-08-29','Výročie Slovenského národného povstania','Slovak National Uprising anniversary','SK'),(39,2025,'2025-09-01','Deň Ústavy Slovenskej republiky','Day of the Constitution of the Slovak Republic','SK'),(40,2025,'2025-09-15','Sedembolestná Panna Mária','Day of Our Lady of the Seven Sorrows','SK'),(41,2025,'2025-11-01','Sviatok Všetkých svätých','All Saints’ Day','SK'),(42,2025,'2025-11-17','Deň boja za slobodu a demokraciu','Struggle for Freedom and Democracy Day','SK'),(43,2025,'2025-12-24','Štedrý deň','Christmas Eve','SK'),(44,2025,'2025-12-25','Prvý sviatok vianočný','Christmas Day','SK'),(45,2025,'2025-12-26','Druhý sviatok vianočný','St. Stephen\'s Day','SK'),(46,2026,'2026-01-01','Deň vzniku Slovenskej republiky','Day of the Establishment of the Slovak Republic','SK'),(47,2026,'2026-01-06','Zjavenie Pána','Epiphany','SK'),(48,2026,'2026-04-03','Veľkonočný piatok','Good Friday','SK'),(49,2026,'2026-04-06','Veľkonočný pondelok','Easter Monday','SK'),(50,2026,'2026-05-01','Sviatok práce','International Workers\' Day','SK'),(51,2026,'2026-05-08','Deň víťazstva nad fašizmom','Day of victory over fascism','SK'),(52,2026,'2026-07-05','Sviatok svätého Cyrila a svätého Metoda','St. Cyril and Methodius Day','SK'),(53,2026,'2026-08-29','Výročie Slovenského národného povstania','Slovak National Uprising anniversary','SK'),(54,2026,'2026-09-01','Deň Ústavy Slovenskej republiky','Day of the Constitution of the Slovak Republic','SK'),(55,2026,'2026-09-15','Sedembolestná Panna Mária','Day of Our Lady of the Seven Sorrows','SK'),(56,2026,'2026-11-01','Sviatok Všetkých svätých','All Saints’ Day','SK'),(57,2026,'2026-11-17','Deň boja za slobodu a demokraciu','Struggle for Freedom and Democracy Day','SK'),(58,2026,'2026-12-24','Štedrý deň','Christmas Eve','SK'),(59,2026,'2026-12-25','Prvý sviatok vianočný','Christmas Day','SK'),(60,2026,'2026-12-26','Druhý sviatok vianočný','St. Stephen\'s Day','SK');
/*!40000 ALTER TABLE `holidays` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `machine_downtime`
--

DROP TABLE IF EXISTS `machine_downtime`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `machine_downtime` (
  `id` int NOT NULL AUTO_INCREMENT,
  `machine_number` int NOT NULL COMMENT '1, 2, or 3',
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `reason` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `oddelenie_id` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_machine_date` (`machine_number`,`start_date`,`end_date`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `machine_downtime`
--

LOCK TABLES `machine_downtime` WRITE;
/*!40000 ALTER TABLE `machine_downtime` DISABLE KEYS */;
INSERT INTO `machine_downtime` VALUES (2,1,'2025-04-09','2025-04-22','odstávka',1,'2025-04-23 17:25:17','2025-05-28 19:09:00'),(4,2,'2025-05-02','2025-05-02',NULL,1,'2025-04-29 13:05:16','2025-05-28 19:09:00'),(6,2,'2025-05-09','2025-05-09',NULL,1,'2025-05-09 06:51:07','2025-05-28 19:09:00');
/*!40000 ALTER TABLE `machine_downtime` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `schifflieder_reference`
--

DROP TABLE IF EXISTS `schifflieder_reference`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `schifflieder_reference` (
  `id` int NOT NULL DEFAULT '1',
  `reference_date` date NOT NULL,
  `last_modified` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  CONSTRAINT `single_row` CHECK ((`id` = 1))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `schifflieder_reference`
--

LOCK TABLES `schifflieder_reference` WRITE;
/*!40000 ALTER TABLE `schifflieder_reference` DISABLE KEYS */;
INSERT INTO `schifflieder_reference` VALUES (1,'2025-04-27','2025-04-27 12:08:30');
/*!40000 ALTER TABLE `schifflieder_reference` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `username` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `role1` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `role2` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `role3` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `strojnik_order` int DEFAULT NULL COMMENT 'Order for machine assignment cycle (1-9)',
  `last_machine_assigned` int DEFAULT NULL COMMENT 'Last machine number assigned (1, 2, or 3)',
  `last_assignment_date` date DEFAULT NULL COMMENT 'Date of last assignment (esp. for strojnik)',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `oddelenie_id` bigint unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `strojnik_order` (`strojnik_order`),
  KEY `fk_users_oddelenia` (`oddelenie_id`),
  CONSTRAINT `fk_users_oddelenia` FOREIGN KEY (`oddelenie_id`) REFERENCES `Oddelenia` (`oddelenie_id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,'Guniš Bohumil','bohus','$2y$10$45z.TiedCLkLFA5Iq/7bb.5EDX2W72O1g7zfleolyAZRv9VrW68Y6','veduci','schifflieder','skladnik',NULL,NULL,NULL,1,1),(2,'Brúder Radomír','radis','$2y$10$Au1Nah4khOTIqtPFU2aGCekTN0SxysmgglGv6cR4kRe5jIxaT6Iva','schifflieder','kontrola',NULL,NULL,NULL,NULL,1,1),(3,'Uhrinová Dominika','dominika','$2y$10$40XRjoA5ZfAxWRZdRF11fevWUw5prWkBjCtseeI4mTud1Tw3eRPry','schifflieder','kontrola',NULL,NULL,NULL,NULL,1,1),(4,'Kotesová Martina','matka','$2y$10$D.G3B79luU9A51epG4peBeF5dIzLtjCe45s09xhs5LIPccYN1CAKG','skladnik','strojnik','kontrola',7,2,'2025-06-03',1,1),(5,'Morárová Linda','linda','$2y$10$wjDzZ.CdwynqJJ4dkpjcTOVL1/1kdrfVeUMkLsQ7CK4/QUbh0EFi6','skladnik','strojnik','kontrola',4,3,'2025-06-10',1,1),(6,'Kučerová Miriama','mirka','$2y$10$.AutAvvwLrQfvP52Zxj/SOg9.2ba5g8V8uFX9gDXiyY3acUBaLvGm','strojnik','kontrola',NULL,2,3,'2025-05-29',1,1),(7,'Šesták Lukáš','lukas','$2y$10$VPLASo4hrwc96EQG/CfwseFanzTZUHJpitSy4vu5.1i0A8dp9k4g6','strojnik','kontrola',NULL,1,2,'2025-06-04',1,1),(8,'Mrázková Jana','jankam','$2y$10$8MPXwClhuv6/tAEubnzLl.WaxajpJBz74JsBpwlBjfdud7WqD6Tu6','strojnik','kontrola',NULL,3,1,'2025-05-29',1,1),(9,'Konrád Oto','oto','$2y$10$dkNOVbDKAarpaRHkojH3Uu1/9VrSy2EkApgxqCO.QoBp7K8pLZRZ2','strojnik','kontrola',NULL,5,2,'2025-05-29',1,1),(10,'Martanovičová Darina','darinka','$2y$10$ZaEJCTaWwFCM5dfPzdJgQeWtDddU1Tb1Sz.XZVpkOVLh3SpWE3Qya','strojnik','kontrola',NULL,6,1,'2025-05-30',1,1),(11,'Andrášková Jana','jankaa','$2y$10$U6N801Kx/5o9xlOBTldvdOu3PJrIXRia1KpCoqdlHNIDZ1acf/p6m','strojnik','kontrola',NULL,8,2,'2025-05-30',1,1),(12,'Kramár Marián','marosko','$2y$10$y8Otg6YzU6PSss7c4SVCpeNji1t3x8uGSj127Kd69hxtoKlkjTyoK','strojnik','kontrola',NULL,9,3,'2025-05-30',1,1);
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-29 16:31:03
