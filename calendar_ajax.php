<?php
require_once 'config.php';
require_once 'functions.php';

// Overenie prihlásenia
requireLogin();

// Získanie PDO spojenia
$pdo = getDbConnection();

// Načítanie údajov prihláseného používateľa
$loggedInUserId = $_SESSION['user_id'] ?? 0;
$loggedUser = getUserById($loggedInUserId, $pdo); // Načítame info vrátane roly a mena

// Definícia informácií pre zobrazenie typov absencií (rovnaká ako v dashboard.php)
$absenceDisplayInfo = [
    'Celý deň'    => ['shortcut' => 'D', 'color' => '#8bc34a', 'class' => 'absence-d'],
    'Ráno'        => ['shortcut' => 'R', 'color' => '#2196f3', 'class' => 'absence-r'],
    'Poobede'     => ['shortcut' => 'P', 'color' => '#ff9800', 'class' => 'absence-p'],
    'Iné'         => ['shortcut' => 'X', 'color' => '#607d8b', 'class' => 'absence-x']
];

// --- Získanie dátumu, ktorý bol zobrazený na dashboarde (pre konzistentné navigačné linky) ---
$requested_date_str = $_GET['date'] ?? date('Y-m-d'); // Fallback na dnešný dátum, ak nie je poskytnutý

// --- Získanie aktuálneho mesiaca a roku pre kalendár z GET parametra ---
// Predvolená hodnota by mala byť mesiac z $requested_date_str, ak $_GET['calendar_month'] nie je nastavený
$defaultCalendarMonthDate = new DateTimeImmutable($requested_date_str);
$calendarMonthParam = $_GET['calendar_month'] ?? $defaultCalendarMonthDate->format('Y-m');
$calendarMonthObj = new DateTimeImmutable($calendarMonthParam . '-01');

$currentMonthNum = $calendarMonthObj->format('n');
$currentYearNum = $calendarMonthObj->format('Y');

// --- Názvy mesiacov v slovenčine ---
$skMonths = [1=>'Január',2=>'Február',3=>'Marec',4=>'Apríl',5=>'Máj',6=>'Jún',
             7=>'Júl',8=>'August',9=>'September',10=>'Október',11=>'November',12=>'December'];

// --- Prvý a posledný deň mesiaca ---
$firstDayOfMonth = $calendarMonthObj->modify('first day of this month');
$lastDayOfMonth = $calendarMonthObj->modify('last day of this month');

// --- Predchádzajúci a nasledujúci mesiac pre navigáciu ---
$prevMonthObj = $calendarMonthObj->modify('-1 month');
$nextMonthObj = $calendarMonthObj->modify('+1 month');

// --- Získanie sviatkov pre aktuálny mesiac ---
$holidays = getHolidaysForMonth($currentMonthNum, $currentYearNum, $pdo);

// --- Získanie absencií prihláseného používateľa pre aktuálny mesiac (s typom) ---
$userAbsencesForMonth = getUserAbsencesForMonthWithType($loggedInUserId, $currentMonthNum, $currentYearNum, $pdo);

// --- Aktuálny deň pre zvýraznenie ---
$today = new DateTimeImmutable(); // Používame Immutable
$todayString = $today->format('Y-m-d');

// --- Vygenerovanie HTML kódu kalendára ---
// Tento skript vracia iba HTML obsah pre .monthly-calendar-section
?>
<h3>Mesačný kalendár</h3>

<div class="calendar-navigation">
    <a href="?calendar_month=<?php echo $prevMonthObj->format('Y-m'); ?>&date=<?php echo htmlspecialchars($requested_date_str); ?>">&laquo; <?php echo $skMonths[$prevMonthObj->format('n')] . ' ' . $prevMonthObj->format('Y'); ?></a>
    <span><?php echo $skMonths[$currentMonthNum] . ' ' . $currentYearNum; ?></span>
    <a href="?calendar_month=<?php echo $nextMonthObj->format('Y-m'); ?>&date=<?php echo htmlspecialchars($requested_date_str); ?>"><?php echo $skMonths[$nextMonthObj->format('n')] . ' ' . $nextMonthObj->format('Y'); ?> &raquo;</a>
</div>

<div class="calendar-container">
    <table class="calendar-table">
        <thead>
            <tr>
                <th>Po</th>
                <th>Ut</th>
                <th>St</th>
                <th>Št</th>
                <th>Pi</th>
                <th>So</th>
                <th>Ne</th>
            </tr>
        </thead>
        <tbody>
            <?php
            $firstDayOfWeek = (int)$firstDayOfMonth->format('N'); // 1 (Po) až 7 (Ne)
            // Začíname od prvého dňa týždňa, v ktorom je prvý deň mesiaca
            $currentCalDate = $firstDayOfMonth->modify('-' . ($firstDayOfWeek - 1) . ' days');
            
            // Končíme posledným dňom týždňa, v ktorom je posledný deň mesiaca
            $lastDayOfWeekOfMonth = (int)$lastDayOfMonth->format('N');
            $endDateLoop = $lastDayOfMonth;
            if ($lastDayOfWeekOfMonth < 7) {
                $endDateLoop = $lastDayOfMonth->modify('+' . (7 - $lastDayOfWeekOfMonth) . ' days');
            }
            
            while ($currentCalDate <= $endDateLoop) {
                if ($currentCalDate->format('N') == 1) {
                    echo '<tr>';
                }
                
                $dateString = $currentCalDate->format('Y-m-d');
                $isCurrentMonth = $currentCalDate->format('m') == $currentMonthNum;
                $isToday = ($dateString == $todayString);
                $isWorkDayResult = isWorkingDay($currentCalDate, $pdo); // Funkcia zisťuje, či je prac. deň (nie víkend, nie sviatok)
                $isHoliday = isset($holidays[$dateString]);
                $absenceTypeOnDay = $userAbsencesForMonth[$dateString] ?? null;
                
                $cellClasses = [];
                if (!$isCurrentMonth) $cellClasses[] = 'other-month';
                if ($isToday) $cellClasses[] = 'today';

                if (!$isWorkDayResult) {
                    $cellClasses[] = 'non-working-day';
                    if ($isHoliday) $cellClasses[] = 'holiday'; // Ak je to nepracovný deň kvôli sviatku
                } elseif ($isHoliday) { // Sviatok, ktorý by inak bol pracovný deň
                    $cellClasses[] = 'holiday';
                    $cellClasses[] = 'non-working-day'; // Sviatok je vždy nepracovný
                }
                
                if ($absenceTypeOnDay && isset($absenceDisplayInfo[$absenceTypeOnDay])) {
                    $cellClasses[] = 'has-absence';
                    $cellClasses[] = $absenceDisplayInfo[$absenceTypeOnDay]['class'];
                }
                
                $clickableAttr = '';
                // Klikateľné sú len pracovné dni v aktuálnom mesiaci, ktoré nie sú sviatkom
                if ($isCurrentMonth && $isWorkDayResult && !$isHoliday) {
                    $cellClasses[] = 'clickable-day';
                    if ($absenceTypeOnDay) {
                        $clickableAttr = ' onclick="confirmDeleteVacation(\'' . $dateString . '\', ' . $loggedInUserId . ')"';
                    } else {
                        // Meno používateľa pre addVacation, ak je dostupné
                        $userNameForAddVacation = isset($loggedUser['name']) ? htmlspecialchars($loggedUser['name']) : '';
                        $clickableAttr = ' onclick="addVacation(\'' . $dateString . '\', ' . $loggedInUserId . ', \'' . $userNameForAddVacation . '\', event)"';
                    }
                }
                
                $cellClassStr = !empty($cellClasses) ? ' class="' . implode(' ', array_unique($cellClasses)) . '"' : '';
                
                echo '<td' . $cellClassStr . $clickableAttr . '>';
                echo '<div class="day-number">' . $currentCalDate->format('j') . '</div>';
                
                // Zobrazenie "odznáčika" absencie
                if ($isCurrentMonth && $absenceTypeOnDay && isset($absenceDisplayInfo[$absenceTypeOnDay])) {
                    $displayInfo = $absenceDisplayInfo[$absenceTypeOnDay];
                    echo '<div class="vacation-indicator" style="background-color:' . htmlspecialchars($displayInfo['color']) . ';" title="' . htmlspecialchars($absenceTypeOnDay) . '">' . htmlspecialchars($displayInfo['shortcut']) . '</div>';
                }
                
                echo '</td>';
                
                if ($currentCalDate->format('N') == 7) {
                    echo '</tr>';
                }
                
                $currentCalDate = $currentCalDate->modify('+1 day');
            }
            ?>
        </tbody>
    </table>
</div>

<div class="calendar-legend">
    <div class="legend-item today-legend-text"><span class="legend-color today-legend"></span> Dnešný deň</div>
    <div class="legend-item"><span class="legend-color non-working-day-legend"></span> Víkend/Sviatok</div>
    <?php foreach ($absenceDisplayInfo as $type => $info): ?>
        <div class="legend-item">
            <span class="legend-color" style="background-color: <?php echo htmlspecialchars($info['color']); ?>;"></span>
            <?php echo htmlspecialchars($type) . " (" . htmlspecialchars($info['shortcut']) . ")"; ?>
        </div>
    <?php endforeach; ?>
</div>