<?php
require_once 'config.php';
require_once 'functions.php';

// Kontrola prihlásenia
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$loggedInUserId = $_SESSION['user_id'];
$loggedInUserData = getUserData($loggedInUserId);

// Kontrola oprávnení - len vedúci môže upravovať nastavenia iných používateľov
$isVeduci = in_array('veduci', [$loggedInUserData['role1'], $loggedInUserData['role2'], $loggedInUserData['role3']]);

// Spracovanie POST požiadavky
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $targetUserId = isset($_POST['user_id']) ? (int)$_POST['user_id'] : $loggedInUserId;
    
    // Kontrola oprávnení - používateľ môže upravovať len svoje nastavenia, vedúci môže upravovať všetky
    if ($targetUserId !== $loggedInUserId && !$isVeduci) {
        die('Nemáte oprávnenie upravovať nastavenia iného používateľa.');
    }
    
    $notificationsEnabled = isset($_POST['notifications_enabled']) ? 1 : 0;
    $notificationTime = $_POST['notification_time'] ?? '07:00';
    $workdaysOnly = isset($_POST['workdays_only']) ? 1 : 0;
    
    try {
        // Upsert - vloženie alebo aktualizácia nastavení
        $sql = "INSERT INTO user_notification_settings (user_id, notifications_enabled, notification_time, workdays_only) 
                VALUES (?, ?, ?, ?) 
                ON DUPLICATE KEY UPDATE 
                notifications_enabled = VALUES(notifications_enabled),
                notification_time = VALUES(notification_time),
                workdays_only = VALUES(workdays_only)";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$targetUserId, $notificationsEnabled, $notificationTime, $workdaysOnly]);
        
        $successMessage = "Nastavenia notifikácií boli úspešne uložené.";
    } catch (PDOException $e) {
        $errorMessage = "Chyba pri ukladaní nastavení: " . $e->getMessage();
    }
}

// Získanie všetkých používateľov (pre vedúceho) alebo len aktuálneho používateľa
if ($isVeduci) {
    $sql = "SELECT u.id, u.name, u.username, 
                   COALESCE(uns.notifications_enabled, 1) as notifications_enabled,
                   COALESCE(uns.notification_time, '07:00:00') as notification_time,
                   COALESCE(uns.workdays_only, 1) as workdays_only
            FROM users u
            LEFT JOIN user_notification_settings uns ON u.id = uns.user_id
            WHERE u.is_active = 1
            ORDER BY u.name";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
} else {
    $sql = "SELECT u.id, u.name, u.username, 
                   COALESCE(uns.notifications_enabled, 1) as notifications_enabled,
                   COALESCE(uns.notification_time, '07:00:00') as notification_time,
                   COALESCE(uns.workdays_only, 1) as workdays_only
            FROM users u
            LEFT JOIN user_notification_settings uns ON u.id = uns.user_id
            WHERE u.id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$loggedInUserId]);
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>

<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nastavenia notifikácií</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .notification-settings {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .user-settings {
            border: 1px solid #ddd;
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .user-name {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .setting-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            gap: 10px;
        }
        .setting-label {
            min-width: 200px;
            font-weight: 500;
        }
        .time-input {
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        .save-btn {
            background: #4CAF50;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .save-btn:hover {
            background: #45a049;
        }
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <?php include 'header.php'; ?>
    
    <div class="notification-settings">
        <h1>Nastavenia notifikácií PWA aplikácie</h1>
        
        <?php if (isset($successMessage)): ?>
            <div class="success-message"><?= htmlspecialchars($successMessage) ?></div>
        <?php endif; ?>
        
        <?php if (isset($errorMessage)): ?>
            <div class="error-message"><?= htmlspecialchars($errorMessage) ?></div>
        <?php endif; ?>
        
        <?php if ($isVeduci): ?>
            <p><strong>Ako vedúci môžete upravovať nastavenia notifikácií pre všetkých používateľov.</strong></p>
        <?php else: ?>
            <p>Tu si môžete upraviť nastavenia notifikácií pre PWA aplikáciu.</p>
        <?php endif; ?>
        
        <?php foreach ($users as $user): ?>
            <div class="user-settings">
                <div class="user-name"><?= htmlspecialchars($user['name']) ?> (<?= htmlspecialchars($user['username']) ?>)</div>
                
                <form method="POST" action="">
                    <input type="hidden" name="user_id" value="<?= $user['id'] ?>">
                    
                    <div class="setting-row">
                        <label class="setting-label">
                            <input type="checkbox" name="notifications_enabled" <?= $user['notifications_enabled'] ? 'checked' : '' ?>>
                            Povoliť notifikácie
                        </label>
                    </div>
                    
                    <div class="setting-row">
                        <label class="setting-label">Čas notifikácie:</label>
                        <input type="time" name="notification_time" value="<?= substr($user['notification_time'], 0, 5) ?>" class="time-input">
                    </div>
                    
                    <div class="setting-row">
                        <label class="setting-label">
                            <input type="checkbox" name="workdays_only" <?= $user['workdays_only'] ? 'checked' : '' ?>>
                            Len v pracovné dni (pondelok-piatok)
                        </label>
                    </div>
                    
                    <button type="submit" class="save-btn">Uložiť nastavenia</button>
                </form>
            </div>
        <?php endforeach; ?>
        
        <div style="margin-top: 30px; padding: 15px; background: #e7f3ff; border-radius: 5px;">
            <h3>Inštrukcie pre PWA aplikáciu:</h3>
            <p><strong>Android:</strong> Otvorte Chrome → Menu → "Pridať na plochu" → Potvrďte inštaláciu</p>
            <p><strong>iPhone:</strong> Otvorte Safari → Ikona zdieľania → "Pridať na plochu" → Potvrďte</p>
            <p><strong>PWA aplikácia:</strong> <a href="PWA/indexPWA.php" target="_blank">Otvoriť PWA aplikáciu</a></p>
        </div>
    </div>
    
    <?php include 'footer.php'; ?>
</body>
</html>
